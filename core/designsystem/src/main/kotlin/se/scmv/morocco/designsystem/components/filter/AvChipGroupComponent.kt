package se.scmv.morocco.designsystem.components.filter

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material3.FilterChip
import androidx.compose.material3.FilterChipDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.domain.models.filter.ListingCategoryFilters
import se.scmv.morocco.domain.models.filter.mockFilterItems


/**
 * A component composable that places its children(chip) in horizontal sequence
 *
 * @param modifier: The modifier to be applied to the AvChipGroup
 * @param isPreviewMode: when we need to preview our the component
 * @param items: items that will be show in the FlowRow
 * @param labelText: text that show as component title
 * @param labelIconPath: the icon url that will be show beside the labelText
 */

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun AvChipGroup(
    modifier: Modifier = Modifier,
    isPreviewMode: Boolean = false,
    label: String = "",
    id: String,
    iconUrl: String = "",
    filterItems: List<ListingCategoryFilters.FilterItems> = emptyList(),
    selectedItem: String = "",
    onItemSelected: (Pair<String, String>) -> Unit
) {
    Column(
        modifier = modifier
    ) {
        Row {
            if (iconUrl.isEmpty()){
                if (isPreviewMode){
                    Icon(
                        imageVector = Icons.Default.MoreVert,
                        contentDescription = null
                    )
                }else{
                    AsyncImage(
                        model = iconUrl,
                        contentDescription = null
                    )
                }
            }
            Text(
                modifier = Modifier.padding(
                    start = MaterialTheme.dimens.medium
                ),
                text = label,
                fontSize = MaterialTheme.typography.titleMedium.fontSize,
                fontFamily = MaterialTheme.typography.titleMedium.fontFamily,
            )
        }

        FlowRow(
            overflow = FlowRowOverflow.Visible
        ) {
            filterItems.forEachIndexed { index, filterItem ->
                AvChip(
                    filterItem = filterItem,
                    isSelected = (selectedItem.isNotEmpty() && selectedItem == filterItem.key),
                    isPreviewMode = isPreviewMode,
                ){ chipLabel ->
                    //selectedChipIndex = if (selectedChipIndex == index) null else index
                    onItemSelected(Pair(first = id, chipLabel.key?:""))
                }
            }
        }
    }
}


@Composable
fun AvChip(
    filterItem: ListingCategoryFilters.FilterItems,
    isPreviewMode: Boolean = false,
    isSelected: Boolean = false,
    onSelectedChange : (ListingCategoryFilters.FilterItems) -> Unit
) {

    FilterChip(
        modifier = Modifier.padding(
            horizontal = MaterialTheme.dimens.small
        ),
        colors = FilterChipDefaults.filterChipColors(
            selectedLabelColor = MaterialTheme.colorScheme.primary
        ),
        border = FilterChipDefaults.filterChipBorder(
            enabled = true,
            selected = isSelected,
            borderWidth = 1.dp,
            selectedBorderColor = Color.Blue,
            disabledBorderColor = Color.Black
        ),
        selected = isSelected,
        shape = RoundedCornerShape(20.dp),
        label = {
            Text(
                text = filterItem.label?:"",
                fontWeight = FontWeight.Bold
            )
        }, onClick = {
            onSelectedChange(filterItem)
        })
}

@Preview
@Composable
private fun AvChipPreview() {
    AvitoTheme {
        AvChipGroup(
            modifier = Modifier.fillMaxWidth()
                .wrapContentHeight(),
            id = "",
            selectedItem = mockFilterItems[2].key!!,
            isPreviewMode = true,
            filterItems = mockFilterItems
        ) { }
    }
}

@Preview
@Composable
private fun AvChipGroupPreview() {
    AvitoTheme {
        AvChipGroup(
            modifier = Modifier.padding(
                MaterialTheme.dimens.medium
            ),
            iconUrl = "Trier par prix",
            id = "mm",
            isPreviewMode = true
        ){}
    }
}





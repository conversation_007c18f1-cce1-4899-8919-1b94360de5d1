package se.scmv.morocco.designsystem.components

import android.annotation.SuppressLint
import android.content.res.Configuration
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.InlineTextContent
import androidx.compose.foundation.text.appendInlineContent
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.FavoriteBorder
import androidx.compose.material3.AssistChip
import androidx.compose.material3.AssistChipDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconToggleButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.Placeholder
import androidx.compose.ui.text.PlaceholderVerticalAlign
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import coil.compose.SubcomposeAsyncImage
import coil.decode.SvgDecoder
import coil.request.ImageRequest
import se.scmv.morocco.common.extensions.getTimeAgoInFrench
import se.scmv.morocco.designsystem.R
import se.scmv.morocco.designsystem.extensions.noRippleClickable
import se.scmv.morocco.designsystem.theme.AvitoRead
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.Gray400
import se.scmv.morocco.designsystem.theme.Typography
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.designsystem.utils.PriceFormatter
import se.scmv.morocco.designsystem.utils.buildAdParamIconUrl
import se.scmv.morocco.domain.models.AdParam
import se.scmv.morocco.domain.models.AdPrice
import se.scmv.morocco.domain.models.Area
import se.scmv.morocco.domain.models.City
import se.scmv.morocco.domain.models.ContactMethod
import se.scmv.morocco.domain.models.ContactMethodUtils
import se.scmv.morocco.domain.models.ListingAd
import se.scmv.morocco.domain.models.PriceChangeType
import se.scmv.morocco.domain.models.createMockListingAds

/**
 * This file contains components related to listing cards.
 * Listing cards display a collection of ads, including the "My Ads" section.
 */
@Composable
fun ListingCard(
    listingAd: ListingAd,
    onFavoriteClick: (ListingAd.Published) -> Unit,
    onAdClick: (String) -> Unit,
    isShopSession: Boolean = false,
    showCTAButtons: Boolean = true,
    onPriceInquiry: ((ContactMethod) -> Unit)? = null
) {
    when (listingAd) {
        is ListingAd.Published -> {
            AdCard(
                listingAd = listingAd,
                isShopSession = isShopSession,
                onFavoriteClick = onFavoriteClick,
                onAdClick = onAdClick,
                onPriceInquiry = onPriceInquiry,
                showCTAButtons = showCTAButtons
            )
        }
        is ListingAd.Premium -> {
            PremiumAdCard(
                listingAd = listingAd,
                onAdClick = onAdClick,
                onPriceInquiry = onPriceInquiry,
                showCTAButtons = showCTAButtons
            )
        }
        is ListingAd.NewConstruction -> {
            NewConstructionAdCard(
                listingAd = listingAd,
                onAdClick = onAdClick,
                onPriceInquiry = onPriceInquiry,
                showCTAButtons = showCTAButtons
            )
        }
        else -> {
            // Handle other cases (ExtendedSearch, ExtendedDelivery, DfpBanner)
            // These are handled separately in the calling code
        }
    }
}

@SuppressLint("SuspiciousIndentation")
@Composable
fun AdCard(
    listingAd: ListingAd.Published,
    isShopSession: Boolean = false,
    onFavoriteClick: (ListingAd.Published) -> Unit,
    onAdClick: (String) -> Unit,
    onPriceInquiry: ((ContactMethod) -> Unit)? = null,
    showCTAButtons: Boolean = true
){
    val context = LocalContext.current
    val colorScheme = MaterialTheme.colorScheme
    var storeIcon by remember {
        mutableStateOf( R.drawable.ic_shop)
    }

    var cardBackground by remember {
        mutableStateOf(colorScheme.background)
    }

    if (listingAd.isHighlighted){
        cardBackground = Color(0XFFEAF0FF)
    }

    Card (
        colors = CardDefaults.elevatedCardColors(
            containerColor = cardBackground
        ),
        modifier = Modifier
            .padding(
                MaterialTheme.dimens.medium
            )
            .clickable {
                onAdClick(listingAd.listId)
            },
        border = BorderStroke(
            width = 0.5.dp,
            color = MaterialTheme.colorScheme.outline
        )
    ) {
        //Root layout
        Column(
            horizontalAlignment = Alignment.Start,
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Box(
                    modifier = Modifier.weight(1f)
                ){
                    Row(
                        modifier = Modifier.padding(
                            horizontal = MaterialTheme.dimens.medium,
                            vertical = MaterialTheme.dimens.medium
                        ),
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        AsyncImage(
                            model = listingAd.logo,
                            modifier = Modifier
                                .size(45.dp)
                                .border(
                                    width = 1.dp,
                                    color = MaterialTheme.colorScheme.outline,
                                    shape = CircleShape
                                )
                                .clip(CircleShape),
                            contentDescription = null,
                            contentScale = ContentScale.FillBounds,
                            error = painterResource(R.drawable.ic_profile_placeholder),
                        )

                        Column(
                            modifier = Modifier.padding(
                                start = MaterialTheme.dimens.small
                            )
                        ) {
                            val textWithIcon = buildAnnotatedString {
                                append(listingAd.sellerName.orEmpty()+" ")
                                if (listingAd.isStore){
                                    if (listingAd.isVerifiedSeller){
                                        storeIcon = R.drawable.ic_verified_shop
                                    }
                                    appendInlineContent("icon", "[icon]")
                                }
                            }

                            val inlineContent = mapOf(
                                "icon" to InlineTextContent(
                                    Placeholder(
                                        width = 20.sp,
                                        height = 20.sp,
                                        placeholderVerticalAlign = PlaceholderVerticalAlign.Center
                                    )
                                ) {
                                    storeIcon.let {
                                        Icon(
                                            modifier = Modifier.size(20.dp),
                                            painter = painterResource(id = it),
                                            contentDescription = null,
                                            tint = Color.Unspecified,
                                        )
                                    }
                                }
                            )
                                Text(
                                    text = textWithIcon,
                                    inlineContent = inlineContent,
                                    fontStyle = MaterialTheme.typography.titleSmall.fontStyle,
                                    fontSize = MaterialTheme.typography.titleSmall.fontSize,
                                    fontWeight = MaterialTheme.typography.titleSmall.fontWeight,
                                    maxLines = 2,
                                )

                            Row(
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Image(
                                    painter = painterResource(R.drawable.ic_time_fill),
                                    contentDescription = null,
                                    modifier = Modifier.size(15.dp)
                                )
                                Text(
                                    modifier = Modifier.padding(
                                        start = MaterialTheme.dimens.tiny
                                    ),
                                    fontStyle = Typography.bodyMedium.fontStyle,
                                    fontSize = Typography.bodyMedium.fontSize,
                                    letterSpacing = 0.15.sp,
                                    lineHeight = 20.sp,
                                    maxLines = 2,
                                    text = context.getTimeAgoInFrench(listingAd.date),
                                    color = Color.Gray
                                )
                            }
                        }

                    }
                }
                    if (listingAd.isHighlighted){
                        AvStartLabel()
                    }
            }
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .height(230.dp)
                    .padding(
                        MaterialTheme.dimens.medium
                    ),
                contentAlignment = Alignment.BottomStart
            ) {
                SmartAsyncImage(
                    imageUrl = listingAd.defaultImage?: "",
                )
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    if (listingAd.imageCount > 0) {
                        MediaCountBox(
                            modifier = Modifier.padding(
                                MaterialTheme.dimens.medium,
                            ),
                            mediaCount = listingAd.imageCount,
                            icon = R.drawable.ic_photo_camera
                        )
                    }
                    if (listingAd.videoCount > 0) {
                        MediaCountBox(
                            modifier = Modifier.padding(
                                MaterialTheme.dimens.medium,
                            ),
                            mediaCount = listingAd.videoCount,
                            icon = R.drawable.ic_video
                        )
                    }
                }
                if (listingAd.isUrgent) {
                    AvIsUrgentLabel(
                        modifier = Modifier
                            .align(Alignment.TopEnd)
                            .padding(end = MaterialTheme.dimens.medium),
                        label = stringResource(R.string.urgent_label)
                    )
                }
                Row(
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                        .padding(end = MaterialTheme.dimens.medium),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    val discount = listingAd.discount
                    if (listingAd.isHotDeal && discount != null) {
                        AvPromotionLabel(
                            discount = discount
                        )
                    }
                }
            }
            if (listingAd.location.name.isNotEmpty() && listingAd.category.name.isNotEmpty()){
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.padding(
                        start = MaterialTheme.dimens.medium,
                        top = MaterialTheme.dimens.medium
                    ),
                ) {
                    Image(
                        modifier = Modifier
                            .padding(end = MaterialTheme.dimens.small)
                            .size(18.dp),
                        painter = painterResource(R.drawable.ic_location),
                        contentDescription = "",
                    )
                    Text(
                        fontStyle = Typography.bodyMedium.fontStyle,
                        fontSize = Typography.bodyMedium.fontSize,
                        letterSpacing = 0.15.sp,
                        lineHeight = 20.sp,
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis,
                        text = context.getString(
                            R.string.listing_location,
                            listingAd.category.name,
                            listingAd.location.name,
                            kotlin.runCatching { listingAd.location.area?.name }.getOrElse { "" }
                        ),
                        color = Color.Gray
                    )
                }
            }
            if (listingAd.offersShipping){
                AvECommerceLabel(
                    label = R.string.offer_shipping
                )
            }
            if (listingAd.isEcommerce){
                AvECommerceLabel(
                    label = R.string.ecommerce
                )
            }

            Text(
                text = listingAd.title,
                fontStyle = Typography.titleLarge.fontStyle,
                modifier = Modifier.padding(
                    vertical = MaterialTheme.dimens.medium,
                    horizontal = MaterialTheme.dimens.medium,
                )
            )
            if (listingAd.params.secondary.isNotEmpty()){
                AvListingAdParams(
                    modifier = Modifier
                        .padding(
                            vertical = MaterialTheme.dimens.medium,
                            horizontal = MaterialTheme.dimens.medium
                        ),
                    params = listingAd.params.secondary
                )
            }
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .wrapContentHeight(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                val contactMethod = if (listingAd.price is AdPrice.Unavailable) {
                    ContactMethodUtils.determineContactMethod(
                        phoneNumber = listingAd.phoneNumber,
                        adId = listingAd.id
                    )
                } else null

                PricingWidget(
                    modifier = Modifier.padding(
                        horizontal = MaterialTheme.dimens.medium,
                        vertical = MaterialTheme.dimens.medium
                    ),
                    price = listingAd.price,
                    isHotDeal = listingAd.isHotDeal,
                    contactMethod = contactMethod,
                    onPriceInquiry = onPriceInquiry,
                    showCTAButtons = showCTAButtons
                )
                if (!isShopSession){
                    IconToggleButton(
                        checked = listingAd.isFavorite,
                        onCheckedChange = {
                            onFavoriteClick(listingAd)
                        },
                    ) {
                        Icon(
                            imageVector = if (listingAd.isFavorite) {
                                Icons.Default.Favorite
                            } else Icons.Default.FavoriteBorder,
                            contentDescription = null,
                            tint = if (listingAd.isFavorite) Color.Red else Color.Gray
                        )
                    }
                }
            }
        }
    }
}

@SuppressLint("SuspiciousIndentation")
@Composable
fun PremiumAdCard(
    listingAd: ListingAd.Premium,
    onAdClick: (String) -> Unit,
    onPriceInquiry: ((ContactMethod) -> Unit)? = null,
    showCTAButtons: Boolean = true
){
    val context = LocalContext.current
    val colorScheme = MaterialTheme.colorScheme
    var storeIcon by remember {
        mutableStateOf( R.drawable.ic_shop)
    }

    var cardBackground by remember {
        mutableStateOf(colorScheme.background)
    }


    Card (
        colors = CardDefaults.elevatedCardColors(
            containerColor = cardBackground
        ),
        modifier = Modifier
            .padding(
                MaterialTheme.dimens.medium
            )
            .clickable {
                onAdClick(listingAd.id)
            },
        border = BorderStroke(
            width = 1.dp,
            color = MaterialTheme.colorScheme.outline
        )
    ) {
        //Root layout
        Column(
            horizontalAlignment = Alignment.Start,
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Box(
                    modifier = Modifier.weight(1f)
                ){
                    Row(
                        modifier = Modifier.padding(
                            horizontal = MaterialTheme.dimens.medium,
                            vertical = MaterialTheme.dimens.medium
                        ),
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        AsyncImage(
                            model = listingAd.logo,
                            modifier = Modifier
                                .size(45.dp)
                                .border(
                                    width = 1.dp,
                                    color = MaterialTheme.colorScheme.outline,
                                    shape = CircleShape
                                )
                                .clip(CircleShape),
                            contentDescription = null,
                            contentScale = ContentScale.FillBounds,
                            error = painterResource(R.drawable.ic_profile_placeholder),
                        )

                        Column(
                            modifier = Modifier.padding(
                                start = MaterialTheme.dimens.small
                            )
                        ) {
                            val textWithIcon = buildAnnotatedString {
                                append(listingAd.sellerName.orEmpty()+" ")
                                if (listingAd.isStore){
                                    if (listingAd.isVerifiedSeller){
                                        storeIcon = R.drawable.ic_verified_shop
                                    }
                                    appendInlineContent("icon", "[icon]")
                                }
                            }

                            val inlineContent = mapOf(
                                "icon" to InlineTextContent(
                                    Placeholder(
                                        width = 20.sp,
                                        height = 20.sp,
                                        placeholderVerticalAlign = PlaceholderVerticalAlign.Center
                                    )
                                ) {
                                    storeIcon.let {
                                        Icon(
                                            modifier = Modifier.size(20.dp),
                                            painter = painterResource(id = it),
                                            contentDescription = null,
                                            tint = Color.Unspecified,
                                        )
                                    }
                                }
                            )
                           Text(
                               text = textWithIcon,
                               inlineContent = inlineContent,
                               fontStyle = MaterialTheme.typography.titleSmall.fontStyle,
                               fontSize = MaterialTheme.typography.titleSmall.fontSize,
                               fontWeight = MaterialTheme.typography.titleSmall.fontWeight,
                               maxLines = 2,
                           )

                            Row(
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Image(
                                    painter = painterResource(R.drawable.ic_time_fill),
                                    contentDescription = null,
                                    modifier = Modifier.size(15.dp)
                                )
                                Text(
                                    modifier = Modifier.padding(
                                        start = MaterialTheme.dimens.tiny
                                    ),
                                    fontStyle = Typography.bodyMedium.fontStyle,
                                    fontSize = Typography.bodyMedium.fontSize,
                                    letterSpacing = 0.15.sp,
                                    lineHeight = 20.sp,
                                    maxLines = 2,
                                    text = context.getTimeAgoInFrench(listingAd.date),
                                    color = Color.Gray
                                )
                            }
                        }

                    }
                }

                    AvPremiumLabel()
            }
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .height(230.dp)
                    .padding(
                        MaterialTheme.dimens.medium
                    ),
                contentAlignment = Alignment.BottomStart
            ) {
                SmartAsyncImage(
                    imageUrl = listingAd.defaultImage?: "",
                )
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                   if (listingAd.imageCount > 0) {
                       MediaCountBox(
                           modifier = Modifier.padding(
                               MaterialTheme.dimens.medium,
                           ),
                           mediaCount = listingAd.imageCount,
                           icon = R.drawable.ic_photo_camera
                       )
                   }
                   if (listingAd.videoCount > 0) {
                       MediaCountBox(
                           modifier = Modifier.padding(
                               MaterialTheme.dimens.medium,
                           ),
                           mediaCount = listingAd.videoCount,
                           icon = R.drawable.ic_video
                       )
                   }
                }
               //if (listingAd.isUrgent) {
               //    AvIsUrgentLabel(
               //        modifier = Modifier
               //            .align(Alignment.TopEnd)
               //            .padding(end = MaterialTheme.dimens.medium),
               //        label = stringResource(R.string.urgent_label)
               //    )
               //}
               //Row(
               //    modifier = Modifier
               //        .align(Alignment.TopEnd)
               //        .padding(end = MaterialTheme.dimens.medium),
               //    horizontalArrangement = Arrangement.SpaceBetween
               //) {
               //    val discount = listingAd.discount
               //    if (listingAd.isHotDeal && discount != null && listingAd.price.getCurrentPrice() != null) {
               //        AvPromotionLabel(
               //            discount = discount
               //        )
               //    }
               //}
            }
           if (listingAd.location.name.isNotEmpty() && listingAd.category.name.isNotEmpty()){
               Row(
                   verticalAlignment = Alignment.CenterVertically,
                   modifier = Modifier.padding(
                       start = MaterialTheme.dimens.medium,
                       top = MaterialTheme.dimens.medium
                   ),
               ) {
                   Image(
                       modifier = Modifier
                           .padding(end = MaterialTheme.dimens.small)
                           .size(18.dp),
                       painter = painterResource(R.drawable.ic_location),
                       contentDescription = "",
                   )
                   Text(
                       fontStyle = Typography.bodyMedium.fontStyle,
                       fontSize = Typography.bodyMedium.fontSize,
                       letterSpacing = 0.15.sp,
                       lineHeight = 20.sp,
                       maxLines = 2,
                       overflow = TextOverflow.Ellipsis,
                       text = context.getString(
                           R.string.listing_location,
                           listingAd.category.name,
                           listingAd.location.name,
                           kotlin.runCatching { listingAd.location.area?.name }.getOrElse { "" }
                       ),
                       color = Color.Gray
                   )
               }
           }
            if (listingAd.offersShipping){
                AvECommerceLabel(
                    label = R.string.offer_shipping
                )
            }
            if (listingAd.isEcommerce){
                AvECommerceLabel(
                    label = R.string.ecommerce
                )
            }

            Text(
                text = listingAd.title,
                fontStyle = Typography.titleLarge.fontStyle,
                modifier = Modifier.padding(
                    vertical = MaterialTheme.dimens.medium,
                    horizontal = MaterialTheme.dimens.medium,
                )
            )
            if (listingAd.params.secondary.isNotEmpty()){
                AvListingAdParams(
                    modifier = Modifier.padding(
                        vertical = MaterialTheme.dimens.medium,
                        horizontal = MaterialTheme.dimens.medium
                    ),
                    params = listingAd.params.secondary
                )
            }
            val contactMethod = if (listingAd.price is AdPrice.Unavailable) {
                ContactMethodUtils.determineContactMethod(
                    phoneNumber = listingAd.phoneNumber,
                    adId = listingAd.id
                )
            } else null

            PricingWidget(
                modifier = Modifier.padding(
                    vertical = MaterialTheme.dimens.medium,
                    horizontal = MaterialTheme.dimens.medium
                ),
                price = listingAd.price,
                contactMethod = contactMethod,
                onPriceInquiry = onPriceInquiry,
                showCTAButtons = showCTAButtons
            )

        }
    }
}

@Composable
fun NewConstructionAdCard(
    listingAd: ListingAd.NewConstruction,
    onAdClick: (String) -> Unit,
    onPriceInquiry: ((ContactMethod) -> Unit)? = null,
    showCTAButtons: Boolean = true
){
    val context = LocalContext.current
    val colorScheme = MaterialTheme.colorScheme

    val cardBackground by remember {
        mutableStateOf(colorScheme.background)
    }


    Card (
        colors = CardDefaults.elevatedCardColors(
            containerColor = cardBackground
        ),
        modifier = Modifier
            .padding(
                MaterialTheme.dimens.medium
            )
            .clickable {
                onAdClick(listingAd.externalLink)
            },
        border = BorderStroke(
            width = 1.dp,
            color = MaterialTheme.colorScheme.outline
        )
    ) {
        //Root layout
        Column(
            horizontalAlignment = Alignment.Start,
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Box(
                    modifier = Modifier.weight(1f)
                ){
                    Row(
                        modifier = Modifier
                            .padding(
                                horizontal = MaterialTheme.dimens.medium,
                                vertical = MaterialTheme.dimens.medium
                            )
                            .fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        Image(
                            painter = painterResource(R.drawable.ic_immo),
                            modifier = Modifier
                                .clip(CircleShape)
                                .size(45.dp)
                                .background(color = Color(0XFFF3F4F6)),
                            contentDescription = null,
                            contentScale = ContentScale.Inside,
                        )

                        Text(
                            modifier = Modifier.padding(
                                start = MaterialTheme.dimens.medium
                            ),
                            text = stringResource(R.string.immo_neuf),
                            fontStyle = MaterialTheme.typography.titleSmall.fontStyle,
                            fontSize = MaterialTheme.typography.titleSmall.fontSize,
                            fontWeight = FontWeight.Bold,
                            maxLines = 2,
                        )
                        Spacer(Modifier.weight(1f))
                        AvTextWithEndIcon(
                            modifier = Modifier.padding(end = MaterialTheme.dimens.medium),
                            text = stringResource(R.string.visiter_ici),
                            textColor = Color(0XFF9EA9BB),
                            iconRes = R.drawable.ic_open_url,
                            iconTint = Color(0XFF9EA9BB)
                        )

                    }
                }
            }
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .height(250.dp)
                    .padding(
                        MaterialTheme.dimens.medium
                    ),
                contentAlignment = Alignment.BottomStart
            ) {
                SubcomposeAsyncImage(
                    model = listingAd.defaultImage,
                    contentDescription = null,
                    loading = {
                        IndeterminateLoading()
                    },
                    error = {
                        Image(
                            painter = painterResource(R.drawable.ic_no_image),
                            contentDescription = null
                        )
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .fillMaxHeight()
                        .clip(
                            RoundedCornerShape(
                                8.dp
                            )
                        ),
                    contentScale = ContentScale.Crop
                )
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    if (listingAd.imagesCount > 0) {
                        MediaCountBox(
                            modifier = Modifier.padding(
                                MaterialTheme.dimens.medium,
                            ),
                            mediaCount = listingAd.imagesCount,
                            icon = R.drawable.ic_photo_camera
                        )
                    }
                }
                Row(
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                        .padding(end = MaterialTheme.dimens.medium),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    IsNewConstructorLabel(
                        modifier = Modifier.padding(end = MaterialTheme.dimens.medium)
                    )
                }
            }

            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(
                    start = MaterialTheme.dimens.medium,
                    top = MaterialTheme.dimens.medium
                ),
            ) {
                Image(
                    modifier = Modifier
                        .padding(end = MaterialTheme.dimens.small)
                        .size(18.dp),
                    painter = painterResource(R.drawable.ic_location),
                    contentDescription = ""
                )
                Text(
                    fontStyle = Typography.bodyMedium.fontStyle,
                    fontSize = Typography.bodyMedium.fontSize,
                    letterSpacing = 0.15.sp,
                    lineHeight = 20.sp,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis,
                    text = context.getString(
                        R.string.listing_location,
                        stringResource(R.string.immo_neuf),
                        listingAd.location.name,
                        ""
                    ),
                    color = Color.Gray
                )
            }

            Text(
                text = listingAd.title,
                fontStyle = Typography.titleLarge.fontStyle,
                modifier = Modifier.padding(
                    start = MaterialTheme.dimens.medium,
                    end = MaterialTheme.dimens.medium,
                    top = MaterialTheme.dimens.medium,
                )
            )
            Row(
                modifier = Modifier
                    .padding(
                        vertical = MaterialTheme.dimens.medium,
                        horizontal = MaterialTheme.dimens.medium
                    ),
            ) {
                if (listingAd.rooms > 0) {
                    AvListingAdParam(
                        paramName = listingAd.rooms.toString(),
                        iconPath = buildAdParamIconUrl("rooms"),
                    )
                }
                if (listingAd.bathrooms > 0) {
                    AvListingAdParam(
                        paramName = listingAd.bathrooms.toString(),
                        iconPath = buildAdParamIconUrl("bathroom"),
                    )
                }
                if (listingAd.size > 0) {
                    AvListingAdParam(
                        paramName = listingAd.size.toString(),
                        iconPath = buildAdParamIconUrl("size"),
                    )
                }
            }
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = MaterialTheme.dimens.medium),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                val contactMethod = if (listingAd.price is AdPrice.Unavailable) {
                    ContactMethodUtils.determineContactMethod(
                        phoneNumber = listingAd.phoneNumber,
                        adId = listingAd.externalLink // Using externalLink as adId for NewConstruction
                    )
                } else null

                PricingWidget(
                    modifier = Modifier.padding(
                        bottom = MaterialTheme.dimens.medium
                    ),
                    price = listingAd.price!!,
                    contactMethod = contactMethod,
                    onPriceInquiry = onPriceInquiry,
                    showCTAButtons = showCTAButtons
                )
            }
        }
    }
}

@SuppressLint("UnrememberedMutableState")
@Composable
fun PricingWidget(
    modifier: Modifier = Modifier,
    isHotDeal: Boolean = false,
    price: AdPrice,
    contactMethod: ContactMethod? = null,
    onPriceInquiry: ((ContactMethod) -> Unit)? = null,
    showCTAButtons: Boolean = true
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically)
    {
        when (price) {
            is AdPrice.Available -> {
                when (price.changeType) {
                    PriceChangeType.DECREASE -> R.drawable.decrease_green
                    PriceChangeType.INCREASE -> R.drawable.increase_red
                    else -> null
                }?.let { icon ->
                    Image(
                        modifier = Modifier.size(30.dp),
                        painter = painterResource(icon),
                        contentDescription = null,
                    )
                }
                Text(
                    text = buildAnnotatedString {
                        withStyle(
                            style = SpanStyle(
                                fontWeight = FontWeight.Bold,
                                color = Color(0XFF3AA4FF),
                                fontSize = MaterialTheme.typography.titleLarge.fontSize
                            )
                        ) {
                            append("${PriceFormatter.formatForDisplay(price.currentWithCurrency)}  ")
                        }
                        val oldPrice = price.oldWithCurrency
                        if (oldPrice != null && isHotDeal) {
                            withStyle(
                                style = SpanStyle(
                                    fontWeight = MaterialTheme.typography.labelSmall.fontWeight,
                                    textDecoration = TextDecoration.LineThrough,
                                    color = Gray400,
                                    fontSize = 15.sp
                                )
                            ) {
                                append("  ${PriceFormatter.formatForDisplay(oldPrice)}")
                            }
                        }
                    },
                )
            }

            AdPrice.Unavailable -> {
                if (contactMethod != null && onPriceInquiry != null && showCTAButtons) {
                    PriceInquiryButton(
                        contactMethod = contactMethod,
                        onPriceInquiry = onPriceInquiry,
                        showCTAButtons = showCTAButtons
                    )
                } else {
                    Text(
                        text = buildAnnotatedString {
                            withStyle(
                                style = SpanStyle(
                                    fontWeight = FontWeight.Bold,
                                    color = Color(0XFF3AA4FF),
                                    fontSize = MaterialTheme.typography.titleLarge.fontSize
                                )
                            ) {
                                append(stringResource(R.string.price_not_specified))
                            }
                        },
                    )
                }
            }
        }
    }
}


@Composable
fun MediaCountBox(
    modifier: Modifier = Modifier,
    icon: Int,
    mediaCount: Int = 0
) {
    Box(
        modifier = modifier.background(Color.Black, RoundedCornerShape(5.dp)),
    ) {
        Row(
            modifier = Modifier.padding(
                horizontal = MaterialTheme.dimens.small
            ),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                painter = painterResource(icon),
                modifier = Modifier.size(20.dp),
                alignment = Alignment.Center,
                contentDescription = null,
            )
            Text(
                modifier = Modifier
                    .wrapContentSize()
                    .padding(
                        start = 3.dp
                    ),
                text = mediaCount.toString(),
                fontSize = MaterialTheme.typography.bodySmall.fontSize,
                color = Color.White,
            )
        }
    }

}

@Preview
@Composable
fun AvPremiumLabel(
    modifier: Modifier = Modifier
){
    AssistChip(
        modifier = modifier,
        border = BorderStroke(0.dp, Color.Transparent),
        onClick = {},
        colors = AssistChipDefaults.assistChipColors().copy(
            labelColor = Color(0XFFF48B29),
            leadingIconContentColor = Color(0XFFF48B29)
        ),
        label = {
            Text(
                text = stringResource(R.string.common_premium)
            )
        },
        leadingIcon = {
            Icon(
                painter = painterResource(R.drawable.ic_premium_label),
                contentDescription = null
            )
        }
    )
}

@Preview
@Composable
fun IsNewConstructorLabel(
    modifier: Modifier = Modifier
){
    AssistChip(
        modifier = modifier,
        border = BorderStroke(0.dp, Color.Transparent),
        onClick = {},
        colors = AssistChipDefaults.assistChipColors().copy(
            labelColor = Color.White,
            leadingIconContentColor = Color.White,
            containerColor = Color(0XFF8871FF)
        ),
        label = {
            Text(
                text = stringResource(R.string.commun_project_neuf),
                fontSize = MaterialTheme.typography.bodySmall.fontSize
            )
        },
        leadingIcon = {
            Icon(
                painter = painterResource(R.drawable.ic_new_construction),
                contentDescription = null,
                modifier = Modifier.size(15.dp)
            )
        }
    )
}

@Preview
@Composable
fun AvStartLabel(
    modifier: Modifier = Modifier
){
    AssistChip(
        modifier = modifier,
        border = BorderStroke(0.dp, Color.Transparent),
        onClick = {},
        colors = AssistChipDefaults.assistChipColors().copy(
            labelColor = Color(0XFF2E6BFF),
            leadingIconContentColor = Color(0XFF2E6BFF)
        ),
        label = {
            Text(
                text = stringResource(R.string.common_start)
            )
        },
        leadingIcon = {
            Icon(
                painter = painterResource(R.drawable.ic_star_label),
                contentDescription = null
            )
        }
    )
}
@Composable
fun AvIsUrgentLabel(modifier: Modifier = Modifier, label: String = "Urgent") {
    AssistChip(
        modifier = modifier,
        colors = AssistChipDefaults.assistChipColors(
            containerColor = MaterialTheme.colorScheme.primary,
            labelColor = Color.White,
            leadingIconContentColor = Color.White
        ),
        border = BorderStroke(
            width = 1.dp,
            color = MaterialTheme.colorScheme.primary
        ),
        onClick = {

        }, label = {
            Text(
                text = label
            )

        }, leadingIcon = {
            Icon(
                painter = painterResource(R.drawable.ic_urgent),
                contentDescription = null
            )
        }
    )
}

@Composable
fun AvPromotionLabel(
    modifier: Modifier = Modifier,
    label: String = stringResource(R.string.commun_promo),
    discount: Int
) {
    AssistChip(
        modifier = modifier,
        colors = AssistChipDefaults.assistChipColors(
            containerColor = AvitoRead,
            labelColor = Color.White,
            leadingIconContentColor = Color.White
        ),
        border = BorderStroke(
            width = 1.dp,
            color = AvitoRead
        ),
        onClick = {

        }, label = {
            Text(
                text = "$label -$discount%"
            )

        }, leadingIcon = {
            Icon(
                painter = painterResource(R.drawable.ic_promotion),
                contentDescription = null,
                modifier = Modifier.size(15.dp)
            )
        }
    )
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun AvListingAdParams(
    modifier: Modifier = Modifier,
    params: List<AdParam>
) {
    FlowRow(
        modifier = modifier
    ) {
        params.forEach {
            AvListingAdParam(paramName = it.value, iconPath = it.iconUrl)
        }
    }
}

@Composable
fun AvListingAdParam(
    paramName: String,
    iconPath: String,
) {
    AssistChip(
        modifier = Modifier
            .padding(end = MaterialTheme.dimens.small)
            .wrapContentWidth()
            .noRippleClickable { },
        colors = AssistChipDefaults.assistChipColors(
            containerColor = MaterialTheme.colorScheme.surfaceContainerHighest,
            labelColor = MaterialTheme.colorScheme.onSurface,
        ),
        border = BorderStroke(
            width = 0.dp,
            color = MaterialTheme.colorScheme.background
        ),
        onClick = {

        }, label = {
            Text(
                text = paramName
            )

        }, leadingIcon = {
            AsyncImage(
                modifier = Modifier.size(15.dp),
                model = ImageRequest.Builder(LocalContext.current)
                    .data(iconPath)
                    .decoderFactory(SvgDecoder.Factory())
                    .build(),
                contentDescription = null,
                colorFilter = ColorFilter.tint(color = MaterialTheme.colorScheme.onSurface)
            )
        }
    )
}

@Composable
fun AvECommerceLabel(
    label: Int
){
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.padding(
            start = MaterialTheme.dimens.medium,
            top = MaterialTheme.dimens.small
        )
    ) {
        Image(
            modifier = Modifier
                .padding(end = MaterialTheme.dimens.small)
                .background(Color(0XFFF6F6F6), CircleShape)
                .size(18.dp),
            painter = painterResource(R.drawable.ic_offer_shipping),
            contentDescription = null,
        )
        Text(
            fontStyle = Typography.bodyMedium.fontStyle,
            fontSize = Typography.bodyMedium.fontSize,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            text = stringResource(label),
            color = Color(0XFF2EC966)
        )
    }
}

@Preview
@Composable
fun AvShippingPreview(){
    AvitoTheme {
        AvECommerceLabel(
            label = R.string.offer_shipping
        )
    }
}

@Preview
@Composable
private fun AvListingCardPreview() {
    AvitoTheme {
        LazyColumn {
            items(items = createMockListingAds()) { listingAd ->
                ListingCard(
                    listingAd = listingAd,
                    onFavoriteClick = { listingAd ->

                    },
                    onAdClick = { },
                    showCTAButtons = true
                )
            }
        }
    }
}

@Preview
@Composable
private fun NewConstructionAdCardPreview() {
    AvitoTheme {
        LazyColumn {
            repeat(5) {
                item {
                    NewConstructionAdCard(
                        listingAd = ListingAd.NewConstruction(
                            title = "Modern Apartment Complex",
                            location = City(
                                id = "1",
                                name = "Casablanca",
                                trackingName = "casablanca",
                                area = Area(
                                    id = "1",
                                    name = "Ain Diab",
                                    trackingName = "ain_diab"
                                )
                            ),
                            price = AdPrice.Available(
                                current = 2500000,
                                currentWithCurrency = "2,500,000 dh",
                                old = null,
                                oldWithCurrency = null,
                                changeType = null
                            ),
                            defaultImage = "https://example.com/new-construction.jpg",
                            imagesCount = 8,
                            externalLink = "https://example.com/project",
                            rooms = 3,
                            bathrooms = 2,
                            size = 120
                        ),
                        onAdClick = { }
                    )
                }
            }
        }
    }
}

@Preview
@Composable
fun AvListingAdParamPreview() {
    AvitoTheme {
        AvListingAdParam("74", iconPath = "")
    }
}

@Preview(uiMode = Configuration.UI_MODE_NIGHT_YES or Configuration.UI_MODE_TYPE_NORMAL)
@Composable
fun AvListingAdParamDarkThemPreview() {
    AvitoTheme {
        AvListingAdParam("74", iconPath = "")
    }
}

@Preview
@Composable
fun AvPromotionLabelPreview() {
    AvitoTheme {
        AvPromotionLabel(
            discount = 15
        )
    }
}

@Preview
@Composable
fun AvIsUrgentLabelPreview() {
    AvitoTheme {
        AvIsUrgentLabel()
    }
}

@Preview
@Composable
fun AvIsUrgentLabelPreviewDarkMode() {
    AvitoTheme {
        AvIsUrgentLabel()
    }
}

@Preview
@Composable
fun MediaCountPreview(){
    MediaCountBox(
        modifier = Modifier.padding(
            MaterialTheme.dimens.medium,
        ),
        mediaCount = 23,
        icon = R.drawable.ic_video
    )
}


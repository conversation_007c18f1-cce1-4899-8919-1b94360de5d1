package se.scmv.morocco.designsystem.components

import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.platform.LocalContext
import se.scmv.morocco.domain.models.Account
import se.scmv.morocco.domain.models.ListingAd

/**
 * Composable function that handles chat functionality for listing screens
 * Checks authentication status and shows appropriate UI (login or FirstMessageBottomSheet)
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ChatHandler(
    account: Account,
    adId: String?,
    adDetails: ListingAd?,
    onNavigateToAuthentication: () -> Unit,
    onSendMessage: (String) -> Unit,
    onDismiss: () -> Unit
) {
    val context = LocalContext.current
    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)
    var showFirstMessageDialog by remember { mutableStateOf(false) }

    // Handle chat action based on authentication status
    LaunchedEffect(adId) {
        if (adId != null) {
            when (account) {
                is Account.Connected -> {
                    // User is logged in, show FirstMessageBottomSheet
                    showFirstMessageDialog = true
                }
                is Account.NotConnected -> {
                    // User is not logged in, navigate to authentication
                    onNavigateToAuthentication()
                }
            }
        }
    }

    // Show FirstMessageBottomSheet if user is authenticated
    if (showFirstMessageDialog && adDetails != null) {
        ModalBottomSheet(
            onDismissRequest = { showFirstMessageDialog = false },
            sheetState = sheetState
        ) {
            when(adDetails){
                is ListingAd.DfpBanner -> {}
                is ListingAd.ExtendedSearch -> {}
                is ListingAd.NewConstruction -> {}
                is ListingAd.Premium -> {}
                is ListingAd.Published -> {
                    FirstMessageBottomSheet(
                        sellerName = adDetails.sellerName,
                        adImageUrl = adDetails.defaultImage,
                        adTitle = adDetails.title,
                        adPrice = "",
                        onMessageSend = { messageText ->
                            onSendMessage(messageText)
                            showFirstMessageDialog = false
                        }
                    )
                }
            }
        }
    }
}

package se.scmv.morocco.designsystem.components.filter

import android.annotation.SuppressLint
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ShoppingCart
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.Label
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.PlainTooltip
import androidx.compose.material3.RangeSlider
import androidx.compose.material3.RangeSliderState
import androidx.compose.material3.SliderDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.DpSize
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import coil.decode.SvgDecoder
import coil.request.ImageRequest
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import se.scmv.morocco.designsystem.components.AvResponsiveText
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.domain.models.filter.ListingCategoryFilters
import se.scmv.morocco.domain.models.filter.RangeParam
import kotlin.math.roundToInt

@SuppressLint("CoroutineCreationDuringComposition")
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AvRangeSlider(
    modifier: Modifier = Modifier,
    sliderLabel: String = "",
    sliderIcon: String = "",
    isPreviewMode: Boolean = false,
    filterItems: List<ListingCategoryFilters.FilterItems> = emptyList(),
    selectedRangeValues: List<RangeParam> = emptyList(),
    id: String,
    onValueSelected: (String, String) -> Unit
) {
    // Use rememberCoroutineScope for debounce behavior
    val scope = rememberCoroutineScope()
    //I need to reverse the list if the slider item is regdate = register date
    val items = if (id != "regdate"){
        filterItems
    }else{
        filterItems.reversed()
    }
    // Slider value state
    var rangeStart by remember { mutableFloatStateOf(0f) }
    var rangeEnd by remember { mutableFloatStateOf((items.size - 1).toFloat()) }

    // Precompute the range
    val range = remember {
        0f..(items.size - 1).toFloat()
    }

    // Labels for the slider
    var minLabel by remember { mutableStateOf(items.firstOrNull()?.label ?: "") }
    var maxLabel by remember { mutableStateOf(items.lastOrNull()?.label ?: "") }


    // Variables to store the selected values
    var greaterThanOrEqual by rememberSaveable { mutableStateOf("") }
    var lessThanOrEqual by rememberSaveable { mutableStateOf("") }

    // Initialize range values from selectedRangeValues
    if (selectedRangeValues.isNotEmpty()) {
        val currentRange = selectedRangeValues.find { it.name == id }
        val minIndex = items.indexOfFirst { currentRange?.value?.greaterThanOrEqual?.toInt().toString() == it.key }
        val maxIndex = items.indexOfFirst { currentRange?.value?.lessThanOrEqual?.toInt().toString() == it.key }

        rangeStart = minIndex.takeIf { it != -1 }?.toFloat()?.coerceIn(0f, range.endInclusive) ?: 0f
        rangeEnd = maxIndex.takeIf { it != -1 }?.toFloat()?.coerceIn(0f, range.endInclusive) ?: range.endInclusive
    }else{
        rangeStart = 0f
        rangeEnd = items.size - 1f
    }

    // RangeSliderState
    val rangeSliderState = remember {
        RangeSliderState(
            activeRangeStart = rangeStart,
            activeRangeEnd = rangeEnd,
            valueRange = range,
            steps = filterItems.size - 2,
            onValueChangeFinished = {
                // Debounce to avoid excessive callbacks
                scope.launch {
                    delay(200L)
                    onValueSelected(
                        lessThanOrEqual,
                        greaterThanOrEqual
                    )
                }
            }
        )
    }

    // Composable UI
    Column(modifier = modifier.padding(horizontal = 16.dp)) {
        // Header with icon and label
        Row(
            modifier = Modifier.padding(vertical = MaterialTheme.dimens.extraBig),
            horizontalArrangement = Arrangement.Start,
            verticalAlignment = Alignment.CenterVertically
        ) {
            if (isPreviewMode) {
                Icon(imageVector = Icons.Default.ShoppingCart, contentDescription = null)
            } else {
                AsyncImage(
                    modifier = Modifier.size(30.dp),
                    model = ImageRequest.Builder(LocalContext.current)
                        .data(sliderIcon)
                        .decoderFactory(SvgDecoder.Factory())
                        .build(),
                    contentDescription = null
                )
            }
            Text(
                modifier = Modifier.padding(start = MaterialTheme.dimens.medium),
                text = sliderLabel,
                fontWeight = MaterialTheme.typography.titleMedium.fontWeight,
                fontSize = MaterialTheme.typography.titleMedium.fontSize
            )
        }

        // RangeSlider Component
        RangeSlider(
            state = rangeSliderState,
            startThumb = {
                AvDump(
                    stepValue = { minLabel },
                )
            },
            endThumb = {
                AvDump(
                    stepValue = { maxLabel },
                )
            },
            track = { rangeSliderState ->
                // Update labels and keys when range changes
                minLabel = items[rangeSliderState.activeRangeStart.roundToInt()].label?: ""
                maxLabel = items[rangeSliderState.activeRangeEnd.roundToInt()].label?: ""

                lessThanOrEqual = items[rangeSliderState.activeRangeStart.roundToInt()].key!!
                greaterThanOrEqual = items[rangeSliderState.activeRangeEnd.roundToInt()].key!!

                // Track styling
                SliderDefaults.Track(
                    modifier = Modifier.height(8.dp),
                    rangeSliderState = rangeSliderState
                )
            }
        )
    }
}

@Composable
@Preview
private fun AvRangeSliderPreview(){
    AvRangeSlider(
        id = "pfiscale"
    ) { greaterThanOrEqual, lessThanOrEqual ->

    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun AvDump(
    stepValue: () -> String
){
    Box(
        modifier = Modifier.wrapContentSize()
    ){
        Label(
            label = {
                PlainTooltip(
                    modifier = Modifier
                        .wrapContentWidth(),
                    containerColor = MaterialTheme.colorScheme.primary,
                    caretSize = DpSize(0.dp, 0.dp),
                ) {
                    AvResponsiveText(stepValue())
                }
            },
            isPersistent = true
        ) {
            Box(
                modifier = Modifier
                    .size(15.dp)
                    .shadow(
                        elevation = 1.dp,
                        shape = CircleShape,
                    )
                    .background(Color.White)
            )
        }
    }
}

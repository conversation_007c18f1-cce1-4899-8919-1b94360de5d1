package se.scmv.morocco.designsystem.theme

import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Immutable
import androidx.compose.runtime.ReadOnlyComposable
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

@Immutable
data class Dimens(
    val none: Dp = 0.dp,
    val tiny: Dp = 2.dp,
    val small: Dp = 4.dp,
    val betweenSmallMedium: Dp = 6.dp,
    val medium: Dp = 8.dp,
    val large: Dp = 10.dp,
    val regular: Dp = 12.dp,
    val betweenRegularDefault: Dp = 14.dp,
    val default: Dp = 16.dp,
    val big: Dp = 20.dp,
    val bigger: Dp = 24.dp,
    val mediumBig: Dp = 36.dp,
    val extraBig: Dp = 40.dp,
    val extraExtraBig: Dp = 60.dp,
    val screenPaddingHorizontal: Dp = 20.dp,
    val bottomSheetPaddingHorizontal: Dp = 30.dp,
    val screenPaddingVertical: Dp = 20.dp,
    val screenImageHeight: Dp = 200.dp
)

/**
 * A composition local for [Dimens].
 */
private val LocalDimens = staticCompositionLocalOf { Dimens() }

val MaterialTheme.dimens
    @Composable
    @ReadOnlyComposable
    get() = LocalDimens.current


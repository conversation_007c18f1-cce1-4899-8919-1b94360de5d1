package se.scmv.morocco.designsystem.components

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PagerScope
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowLeft
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowRight
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.material3.rememberBottomAppBarState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import se.scmv.morocco.designsystem.R
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.Blue600
import se.scmv.morocco.designsystem.theme.Green700
import se.scmv.morocco.designsystem.theme.dimens
import kotlin.time.Duration.Companion.seconds

@Stable
data class AvStepperButtonState(
    val text: Int = R.string.common_continue,
    val enable: Boolean = true,
    val loading: Boolean = false
)

@Stable
data class AvStepperHeaderStep(@DrawableRes val icon: Int)

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AvStepper(
    modifier: Modifier = Modifier,
    pagerState: PagerState,
    steps: ImmutableList<AvStepperHeaderStep>,
    footerLeftContent: (@Composable RowScope.() -> Unit)?,
    footerBtnState: State<AvStepperButtonState> = remember { mutableStateOf(AvStepperButtonState()) },
    onBackClicked: () -> Unit,
    onCloseClicked: () -> Unit,
    onNextClicked: () -> Unit,
    pageContent: @Composable PagerScope.(page: Int) -> Unit,
) {
    rememberBottomAppBarState()
    Column(modifier = modifier.fillMaxSize()) {
        AvStepperHeader(
            modifier = Modifier
                .fillMaxWidth()
                .height(TopAppBarDefaults.TopAppBarExpandedHeight),
            steps = steps,
            currentPage = pagerState.currentPage,
            onBackClicked = onBackClicked,
            onCloseClicked = onCloseClicked
        )
        HorizontalPager(
            modifier = Modifier
                .weight(1f)
                .background(MaterialTheme.colorScheme.surfaceContainerHighest),
            state = pagerState,
            contentPadding = PaddingValues(MaterialTheme.dimens.large),
            pageSpacing = MaterialTheme.dimens.large,
            userScrollEnabled = false,
            pageContent = pageContent
        )
        AvStepperFooter(
            leftContent = footerLeftContent,
            state = footerBtnState,
            onNextClicked = onNextClicked
        )
    }
}

@Composable
private fun AvStepperHeader(
    modifier: Modifier = Modifier,
    steps: ImmutableList<AvStepperHeaderStep>,
    currentPage: Int,
    onBackClicked: () -> Unit,
    onCloseClicked: () -> Unit,
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically
    ) {
        AvIconButton(
            icon = Icons.AutoMirrored.Filled.KeyboardArrowLeft,
            colors = IconButtonDefaults.outlinedIconButtonColors(),
            onClick = onBackClicked
        )
        Row(
            modifier = Modifier.weight(1f),
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = Alignment.CenterVertically
        ) {
            steps.forEachIndexed { index, step ->
                Box(
                    modifier = Modifier
                        .background(
                            color = when {
                                index > currentPage -> MaterialTheme.colorScheme.outline
                                index < currentPage -> Green700
                                else -> Blue600
                            },
                            shape = CircleShape
                        )
                        .padding(MaterialTheme.dimens.medium),
                    contentAlignment = Alignment.Center
                ) {
                    Image(
                        modifier = Modifier.size(MaterialTheme.dimens.big),
                        painter = painterResource(step.icon),
                        contentDescription = null
                    )
                }
                if (index < steps.lastIndex) {
                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.KeyboardArrowRight,
                        contentDescription = null,
                        tint = if (index <= currentPage) {
                            MaterialTheme.colorScheme.primary
                        } else MaterialTheme.colorScheme.onBackground
                    )
                }
            }
        }
        AvIconButton(
            icon = Icons.Filled.Close,
            colors = IconButtonDefaults.outlinedIconButtonColors(),
            onClick = onCloseClicked
        )
    }
}

@Composable
private fun AvStepperFooter(
    modifier: Modifier = Modifier,
    leftContent: (@Composable RowScope.() -> Unit)?,
    state: State<AvStepperButtonState> = remember { mutableStateOf(AvStepperButtonState()) },
    onNextClicked: () -> Unit
) {
    Row(
        modifier = modifier
            .padding(MaterialTheme.dimens.medium)
            .fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        if (leftContent != null) {
            leftContent()
        }
        Spacer(modifier = Modifier.width(MaterialTheme.dimens.small))
        AvPrimaryButton(
            text = stringResource(state.value.text),
            enabled = state.value.enable,
            loading = state.value.loading,
            onClick = onNextClicked
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun AvStepperPreview() {
    AvitoTheme {
        val steps = listOf(
            AvStepperHeaderStep(icon = R.drawable.ic_ad_insert_step1),
            AvStepperHeaderStep(icon = R.drawable.ic_ad_insert_step2),
            AvStepperHeaderStep(icon = R.drawable.ic_ad_insert_step3),
            AvStepperHeaderStep(icon = R.drawable.ic_ad_insert_step4)
        ).toImmutableList()
        val pagerState = rememberPagerState { steps.size }
        val footerBtnState = remember { mutableStateOf(AvStepperButtonState()) }
        val scope = rememberCoroutineScope()
        AvStepper(
            steps = steps,
            onBackClicked = {
                scope.launch { pagerState.animateScrollToPage(pagerState.currentPage - 1) }
            },
            onCloseClicked = {

            },
            onNextClicked = {
                scope.launch {
                    footerBtnState.value = footerBtnState.value.copy(loading = true, enable = false)
                    delay(2.seconds)
                    footerBtnState.value = footerBtnState.value.copy(loading = false, enable = true)
                    pagerState.animateScrollToPage(pagerState.currentPage + 1)
                }
            },
            footerLeftContent = {
                TextButton(
                    modifier = Modifier.weight(4f),
                    onClick = { }
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            painter = painterResource(R.drawable.ic_light_bulb),
                            contentDescription = null,
                            tint = Blue600
                        )
                        Text(text = "This a tips title, tips title, tips title", style = MaterialTheme.typography.bodyLarge)
                    }
                }
            },
            footerBtnState = footerBtnState,
            pagerState = pagerState
        ) {
            Text(text = "Page $it content", style = MaterialTheme.typography.bodyLarge)
        }
    }
}
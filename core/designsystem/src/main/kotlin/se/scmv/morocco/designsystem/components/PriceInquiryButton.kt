package se.scmv.morocco.designsystem.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import se.scmv.morocco.designsystem.R
import se.scmv.morocco.designsystem.theme.AvitoColors
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.domain.models.ContactMethod

/**
 * Button component for price inquiries that replaces empty price displays
 * Shows different icons and actions based on available contact methods
 */
@Composable
fun PriceInquiryButton(
    contactMethod: ContactMethod,
    onPriceInquiry: (ContactMethod) -> Unit,
    showCTAButtons: Boolean = true,
    modifier: Modifier = Modifier
) {
    if (!showCTAButtons) {
        return
    }
    
    val (iconRes, backgroundColor, textColor) = when (contactMethod) {
        is ContactMethod.WhatsApp -> {
            Triple(
                R.drawable.ic_whatsapp,
                AvitoColors.PriceInquiryWhatsAppBackground,
                AvitoColors.PriceInquiryWhatsAppForeground
            )
        }
        is ContactMethod.PhoneCall -> {
            Triple(
                R.drawable.ic_call,
                AvitoColors.PriceInquiryPhoneBackground,
                AvitoColors.PriceInquiryPhoneForeground
            )
        }
        is ContactMethod.Chat -> {
            Triple(
                R.drawable.ic_message,
                AvitoColors.PriceInquiryChatBackground,
                AvitoColors.PriceInquiryChatForeground
            )
        }
    }

    Row(
        modifier = modifier
            .background(
                color = backgroundColor,
                shape = RoundedCornerShape(MaterialTheme.dimens.medium)
            )
            .clickable { onPriceInquiry(contactMethod) }
            .padding(
                horizontal = MaterialTheme.dimens.medium,
                vertical = MaterialTheme.dimens.small
            ),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small)
    ) {
        Icon(
            painter = painterResource(id = iconRes),
            contentDescription = null,
            modifier = Modifier.size(20.dp),
            tint = textColor
        )
        
        Text(
            text = stringResource(R.string.ask_for_price),
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            color = textColor
        )
    }
}

@Preview
@Composable
private fun PriceInquiryButtonWhatsAppPreview() {
    AvitoTheme {
        PriceInquiryButton(
            contactMethod = ContactMethod.WhatsApp("0612345678"),
            onPriceInquiry = { },
            showCTAButtons = true
        )
    }
}

@Preview
@Composable
private fun PriceInquiryButtonChatPreview() {
    AvitoTheme {
        PriceInquiryButton(
            contactMethod = ContactMethod.Chat("12345"),
            onPriceInquiry = { },
            showCTAButtons = true
        )
    }
}

@Preview
@Composable
private fun PriceInquiryButtonPhoneCallPreview() {
    AvitoTheme {
        PriceInquiryButton(
            contactMethod = ContactMethod.PhoneCall("0612345678"),
            onPriceInquiry = { },
            showCTAButtons = true
        )
    }
}

package se.scmv.morocco.designsystem.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import se.scmv.morocco.designsystem.R
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.Gray500

@Composable
fun EmptyNoResultsScreen(onSearchClick: () -> Unit){
    Column(
        modifier = Modifier.fillMaxSize(),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = stringResource(R.string.oups),
            fontWeight = MaterialTheme.typography.titleLarge.fontWeight,
            fontSize = MaterialTheme.typography.titleLarge.fontSize
        )
        Text(
            text = stringResource(R.string.empty_no_result_message),
            fontWeight = MaterialTheme.typography.titleSmall.fontWeight,
            fontSize = MaterialTheme.typography.titleSmall.fontSize,
            color = Gray500
        )
        Image(
            modifier = Modifier.size(200.dp),
            painter = painterResource(R.drawable.no_search_result_illustration),
            contentDescription = null
        )
        Button(
            shape = RoundedCornerShape(5.dp),
            colors = ButtonDefaults.buttonColors(
                containerColor = MaterialTheme.colorScheme.primary
            ),
            onClick = {
                onSearchClick()
            }) {
            Text(text = stringResource(R.string.global_echercher))
        }
    }
}

@Preview
@Composable
fun EmptyNoResultsScreenPreview(){
    AvitoTheme {
        EmptyNoResultsScreen(){}
    }
}
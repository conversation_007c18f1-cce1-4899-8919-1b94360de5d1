package se.scmv.morocco.designsystem.components

import android.icu.text.CaseMap.Title
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.OutlinedCard
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import se.scmv.morocco.designsystem.R
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.dimens

@Composable
fun ShowWarningPhoneCallDialog(
    vendorName: String,
    phoneNumber: String,
    onDismissRequest: () -> Unit,
    onCallClick: () -> Unit
) {
    Dialog(
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true
        ),
        onDismissRequest = {
            onDismissRequest()
        }) {
        OutlinedCard {
            Column(
                modifier = Modifier
                    .padding(
                        MaterialTheme.dimens.big
                    )
                    .fillMaxWidth()
                    .wrapContentHeight()
                    .background(color = MaterialTheme.colorScheme.background),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Image(
                    modifier = Modifier.padding(
                        top = MaterialTheme.dimens.big
                    ),
                    painter = painterResource(R.drawable.ic_show_phone_warning_illustration),
                    contentDescription = null
                )
                Text(
                    text = stringResource(R.string.call_ad_owner_warning).uppercase(),
                    fontSize = MaterialTheme.typography.titleMedium.fontSize,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xffd73e53)
                )
                Text(
                    modifier = Modifier.padding(
                        MaterialTheme.dimens.big
                    ),
                    text = stringResource(R.string.call_ad_owner_warning_message),
                    textAlign = TextAlign.Center,
                    style = TextStyle(
                        fontSize = 13.sp,
                        lineHeight = 16.sp
                    )
                )
                Text(
                    text = vendorName,
                    textAlign = TextAlign.Center,
                    fontSize = MaterialTheme.typography.titleMedium.fontSize,
                    style = TextStyle(
                        fontSize = 13.sp,
                        lineHeight = 16.sp
                    )
                )
                AvButtonWithTextAndIcon(
                    modifier = Modifier.padding(
                        vertical = MaterialTheme.dimens.bigger
                    ),
                    icon = R.drawable.ic_call_grid,
                    buttonTitle = phoneNumber,
                    shape = RoundedCornerShape(6.dp)
                ) {
                    onCallClick()
                }
            }
        }
    }

}

@Preview
@Composable
fun ShowWarningPhoneCallDialogPreview() {
    AvitoTheme {
        ShowWarningPhoneCallDialog(
            vendorName = "Avito Boutique",
            phoneNumber = "0252521511541",
            onDismissRequest = {}
        ) {}
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AvAttachmentTypeChooserBtmSheet(
    modifier: Modifier = Modifier,
    title: String,
    onDismiss: () -> Unit,
    onCamera: (() -> Unit)?,
    onGallery: () -> Unit
) {
    ModalBottomSheet(
        modifier = modifier,
        onDismissRequest = onDismiss
    ) {
        Column(
            modifier = Modifier
                .padding(MaterialTheme.dimens.default)
                .fillMaxWidth(),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.default)
        ) {
            Text(text = title, style = MaterialTheme.typography.titleMedium)
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceAround
            ) {
                if (onCamera != null) {
                    TextButton(
                        onClick = {
                            onDismiss()
                            onCamera()
                        },
                        shape = MaterialTheme.shapes.small
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Image(
                                modifier = Modifier.size(MaterialTheme.dimens.extraBig),
                                painter = painterResource(R.drawable.ic_camera),
                                contentDescription = null
                            )
                            Text(
                                text = "Appareil photo",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onBackground
                            )
                        }
                    }
                }
                TextButton(
                    onClick = {
                        onDismiss()
                        onGallery()
                    },
                    shape = MaterialTheme.shapes.small
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Image(
                            modifier = Modifier.size(MaterialTheme.dimens.extraBig),
                            painter = painterResource(R.drawable.ic_gallery),
                            contentDescription = null
                        )
                        Text(
                            text = "Galerie",
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onBackground
                        )
                    }
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun AvAttachmentTypeChooserBtmSheetPreview() {
    AvitoTheme {
        AvAttachmentTypeChooserBtmSheet(
            title = "Photos",
            onDismiss = {},
            onCamera = {  },
            onGallery = {}
        )
    }
}
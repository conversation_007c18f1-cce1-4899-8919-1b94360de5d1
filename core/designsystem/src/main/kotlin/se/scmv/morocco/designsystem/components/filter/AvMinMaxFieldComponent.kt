package se.scmv.morocco.designsystem.components.filter

import android.content.res.Configuration
import android.util.Log
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusDirection
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.OffsetMapping
import androidx.compose.ui.text.input.TransformedText
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.core.text.isDigitsOnly
import coil.compose.AsyncImage
import coil.decode.SvgDecoder
import coil.request.ImageRequest
import se.scmv.morocco.designsystem.components.AvTextField
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.Gray100
import se.scmv.morocco.designsystem.theme.Gray500
import se.scmv.morocco.designsystem.theme.dimens

/**
 * A component field that accepts minimum and maximum values.
 *
 * @param isPreviewMode Indicates whether the component is in preview mode. When `true`, the icon path is not required, allowing a visual preview of the component. Defaults to `false`.
 * @param iconPath The URL of the icon image.
 */


@Composable
fun MinMaxTextField(
    modifier: Modifier = Modifier,
    minValue: String,
    maxValue: String,
    minPlaceHolder: String = "0",
    maxPlaceHolder: String = "0",
    label: String,
    iconPath: String,
    isPreviewMode: Boolean = false,
    onValueChanged: (String, String) -> Unit,
) {

    val focusManager = LocalFocusManager.current

    var minTextInput by remember {
        mutableStateOf(minValue)
    }
    var maxTextInput by remember {
        mutableStateOf(maxValue)
    }

    Column(
        modifier = Modifier.padding(
            horizontal = MaterialTheme.dimens.big
        )
    ) {

         Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    MaterialTheme.dimens.medium
                )
        ) {
            if (isPreviewMode){
                Icon(
                    imageVector = Icons.Default.CheckCircle,
                    contentDescription = null
                )
            }else{
                AsyncImage(
                    modifier = Modifier.size(30.dp),
                    model = ImageRequest.Builder(LocalContext.current)
                        .data(iconPath)
                        .decoderFactory(SvgDecoder.Factory())
                        .build(),
                    contentDescription = null
                )
            }

            Text(
                text = label,
                fontSize = MaterialTheme.typography.titleMedium.fontSize,
                fontFamily = MaterialTheme.typography.titleMedium.fontFamily
            )
        }

        Row(
            modifier = modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            // Minimum Value Input
            OutlinedTextField(
                value = if (minTextInput == minPlaceHolder) {
                    ""
                }else{
                    minTextInput
                }  ,
                onValueChange = { value ->
                    minTextInput = value
                    onValueChanged(minTextInput, maxTextInput)
                },
                placeholder = {
                    Text(
                        text = minPlaceHolder,
                        color = Gray500
                    )
                },
                keyboardOptions = KeyboardOptions.Default.copy(
                    keyboardType = KeyboardType.Number,
                    imeAction = ImeAction.Next
                ),
                keyboardActions = KeyboardActions(
                    onNext = {
                        focusManager.moveFocus(FocusDirection.Right)
                    }
                ),
                modifier = Modifier.weight(1f),
                singleLine = true
            )

            Text(
                "-",
                style = MaterialTheme.typography.bodyLarge,
                modifier = Modifier.padding(horizontal = 8.dp)
            )

            // Maximum Value Input
            OutlinedTextField(
                value = if (maxTextInput == maxPlaceHolder){
                    ""
                }else{
                    maxTextInput
                },
                onValueChange = { value ->
                    maxTextInput = value
                    onValueChanged(minTextInput, maxTextInput)
                },
                placeholder = {
                   Text(
                       text = maxPlaceHolder,
                       color =  Gray500
                   )

                },
                keyboardOptions = KeyboardOptions.Default.copy(
                    keyboardType = KeyboardType.Number,
                    imeAction = ImeAction.Done
                ),
                keyboardActions = KeyboardActions(
                    onDone = {
                        focusManager.moveFocus(focusDirection = FocusDirection.Down)
                    }
                ),
                modifier = Modifier.weight(1f),
                singleLine = true
            )
        }
    }
}


@Preview(showBackground = true)
@Composable
fun AvFieldPreview(){
    AvitoTheme {
        Column (
            modifier = Modifier.padding(10.dp)
        ){
            AvTextField(
                modifier = Modifier.padding(),
                value = "",
                onValueChanged = {

                })
        }
    }

}
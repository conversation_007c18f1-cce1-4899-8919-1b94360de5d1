package se.scmv.morocco.designsystem.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImagePainter
import coil.compose.rememberAsyncImagePainter
import se.scmv.morocco.designsystem.R

/**
 * A reusable image composable that handles async image loading using Coil's `rememberAsyncImagePainter`.
 *
 * This composable displays:
 * - A loading indicator while the image is being fetched.
 * - The actual image when loading succeeds.
 * - A fallback image (`ic_no_image`) if loading fails.
 *
 * @param imageUrl The URL of the image to load.
 * @param modifier Optional [Modifier] for styling and layout control.
 * @param contentDescription Description of the image for accessibility.
 */

@Composable
fun SmartAsyncImage(
    imageUrl: String,
    modifier: Modifier = Modifier,
    contentDescription: String? = null
) {
    val painter = rememberAsyncImagePainter(model = imageUrl)
    val state = painter.state

    Box(
        modifier = modifier
            .clip(RoundedCornerShape(5.dp)),
        contentAlignment = Alignment.Center,
    ) {
        Image(
            painter = painter,
            contentDescription = contentDescription,
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.Crop
        )

        when (state) {
            is AsyncImagePainter.State.Loading -> {
                IndeterminateLoading()
            }

            is AsyncImagePainter.State.Error -> {
                Image(
                    painter = painterResource(R.drawable.ic_no_image),
                    contentDescription = contentDescription,
                    modifier = Modifier.fillMaxSize(),
                    contentScale = ContentScale.Crop
                )
            }

            else -> Unit // Do nothing if success
        }
    }
}


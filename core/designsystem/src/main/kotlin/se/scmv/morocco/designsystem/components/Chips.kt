package se.scmv.morocco.designsystem.components

import androidx.annotation.StringRes
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Info
import androidx.compose.material3.FilterChip
import androidx.compose.material3.FilterChipDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.LocalMinimumInteractiveComponentSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.Stable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import coil.decode.SvgDecoder
import coil.request.ImageRequest
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.toPersistentList
import se.scmv.morocco.designsystem.R
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.designsystem.utils.IconUrlOrDrawable

@Stable
data class ChipData(
    val id: String,
    val name: String,
    val selected: Boolean,
    val icon: IconUrlOrDrawable? = null
)

object ChipStateHandler {

    fun handleMonoSelect(
        currentState: List<ChipData>,
        clickedChip: ChipData
    ): List<ChipData> {
        return currentState.map {
            if (it != clickedChip) {
                it.copy(selected = false)
            } else {
                it.copy(selected = !it.selected)
            }
        }
    }

    fun handleMultiSelect(
        currentState: List<ChipData>,
        clickedChip: ChipData
    ): List<ChipData> {
        val indexOfItem = currentState.indexOf(clickedChip)
        if (indexOfItem == -1) throw IllegalArgumentException("Item doesn't exist in the list")

        return currentState.toMutableList()
            .apply {
                val oldChip = get(indexOfItem)
                val newChip = oldChip.copy(selected = !oldChip.selected)
                set(indexOfItem, newChip)
            }
            .toList()
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun AvChipGroup(
    modifier: Modifier = Modifier,
    chips: ImmutableList<ChipData>,
    @StringRes title: Int? = null,
    @StringRes explanation: Int? = null,
    required: Boolean = false,
    error: String? = null,
    onChipClicked: (ChipData) -> Unit
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.tiny)
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium)
        ) {
            title?.let {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(2.dp)
                ) {
                    if (required) {
                        Text(
                            text = "*",
                            style = MaterialTheme.typography.titleMedium,
                            color = MaterialTheme.colorScheme.error
                        )
                    }
                    Text(
                        text = stringResource(id = it),
                        style = MaterialTheme.typography.titleSmall
                    )
                }
            }
            if (explanation != null) {
                var openDialog by remember { mutableStateOf(false) }
                Icon(
                    modifier = Modifier
                        .clip(CircleShape)
                        .clickable {
                            openDialog = true
                        },
                    imageVector = Icons.Default.Info,
                    contentDescription = "Show explanation",
                    tint = MaterialTheme.colorScheme.primary
                )
                if (openDialog) {
                    AvAlertDialog(
                        type = AvAlertType.Info,
                        text = stringResource(explanation),
                        onDismissRequest = {
                            openDialog = false
                        }
                    )
                }
            }
        }
        CompositionLocalProvider(LocalMinimumInteractiveComponentSize provides 0.dp) {
            FlowRow(
                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium),
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium)
            ) {
                chips.forEach {
                    FilterChip(
                        modifier = Modifier.testTag(it.name),
                        selected = it.selected,
                        label = {
                            Text(text = it.name)
                        },
                        shape = MaterialTheme.shapes.large,
                        colors = FilterChipDefaults.filterChipColors(
                            selectedContainerColor = MaterialTheme.colorScheme.primary,
                            selectedLabelColor = MaterialTheme.colorScheme.onPrimary,
                            selectedLeadingIconColor = MaterialTheme.colorScheme.onPrimary
                        ),
                        onClick = { onChipClicked(it) }
                    )
                }
            }
        }
        error?.let {
            Text(
                text = it,
                color = MaterialTheme.colorScheme.error,
                style = MaterialTheme.typography.titleSmall
            )
        }
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
private fun AvChipGroupMonoSelectPreview() {
    AvitoTheme {
        var chips by remember {
            mutableStateOf(
                List(10) {
                    ChipData(
                        id = "$it",
                        name = "Chip $it",
                        selected = false
                    )
                }
            )
        }
        AvChipGroup(
            modifier = Modifier.fillMaxWidth(),
            chips = chips.toPersistentList(),
            title = R.string.shop_account_sign_up_screen_shop_category_label,
            explanation = R.string.shop_account_sign_up_screen_shop_category_label,
            required = true
        ) {
            chips = ChipStateHandler.handleMonoSelect(chips, it)
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun AvChipGroupMultiSelectPreview() {
    AvitoTheme {
        var chips by remember {
            mutableStateOf(
                List(10) {
                    ChipData(
                        id = "$it",
                        name = "Chip $it",
                        selected = false
                    )
                }
            )
        }
        AvChipGroup(
            modifier = Modifier.fillMaxWidth(),
            chips = chips.toPersistentList(),
            title = R.string.shop_account_sign_up_screen_shop_category_label,
            required = true
        ) {
            chips = ChipStateHandler.handleMultiSelect(chips, it)
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun AvChipGroupErrorPreview() {
    AvitoTheme {
        var chips by remember {
            mutableStateOf(
                List(10) {
                    ChipData(
                        id = "$it",
                        name = "Chip $it",
                        selected = false
                    )
                }
            )
        }
        AvChipGroup(
            modifier = Modifier.fillMaxWidth(),
            chips = chips.toPersistentList(),
            title = R.string.shop_account_sign_up_screen_shop_category_label,
            required = true,
            error = stringResource(R.string.shop_account_sign_up_screen_shop_category_required)
        ) {
            chips = ChipStateHandler.handleMultiSelect(chips, it)
        }
    }
}

@Composable
fun AvHorizontalChipGroup(
    chips: ImmutableList<ChipData>,
    onChipClicked: (ChipData) -> Unit,
    modifier: Modifier = Modifier,
    state: LazyListState = rememberLazyListState(),
    staticChip: ChipData? = null,
    onStaticChipClicked: (ChipData) -> Unit = {}
) {
    LazyRow(
        modifier = modifier,
        state = state,
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium),
        contentPadding = PaddingValues(horizontal = MaterialTheme.dimens.medium)
    ) {
        staticChip?.let {
            item {
                AvHorizontalChip(
                    chip = it,
                    onClicked = { onStaticChipClicked(it) }
                )
            }
        }
        items(chips) {
           AvHorizontalChip(
                chip = it,
                onClicked = { onChipClicked(it) }
            )
        }
    }
}

@Composable
fun AvHorizontalChip(
    modifier: Modifier = Modifier,
    chip: ChipData,
    onClicked: () -> Unit,
) {
    FilterChip(
        selected = chip.selected,
        onClick = onClicked,
        label = { Text(text = chip.name, style = MaterialTheme.typography.labelLarge) },
        modifier = modifier.testTag(chip.name),
        leadingIcon = if (chip.icon != null) {
            {
                when(val icon = chip.icon) {
                    is IconUrlOrDrawable.Url -> {
                        AsyncImage(
                            modifier = Modifier.size(MaterialTheme.dimens.big),
                            model = ImageRequest.Builder(LocalContext.current)
                                .data(icon.url)
                                .decoderFactory(SvgDecoder.Factory())
                                .build(),
                            contentDescription = null
                        )
                    }
                    is IconUrlOrDrawable.Drawable -> {
                        Icon(
                            modifier = Modifier.size(MaterialTheme.dimens.big),
                            painter = painterResource(icon.id),
                            contentDescription = null
                        )
                    }

                    else -> {}
                }
            }
        } else {
            null
        },
        shape = CircleShape,
        colors = FilterChipDefaults.filterChipColors(
            containerColor = MaterialTheme.colorScheme.surfaceContainerHighest,
            labelColor = MaterialTheme.colorScheme.onBackground,
            iconColor = MaterialTheme.colorScheme.onBackground,
            selectedContainerColor = MaterialTheme.colorScheme.primary,
            selectedLabelColor = MaterialTheme.colorScheme.onPrimary,
            selectedLeadingIconColor = MaterialTheme.colorScheme.onPrimary
        ),
        border = null
    )
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
private fun AvHorizontalChipGroupPreview() {
    AvitoTheme {
        var chips by remember {
            mutableStateOf(
                List(10) {
                    ChipData(
                        id = "$it",
                        name = "Chip $it",
                        selected = it % 2 == 0,
                        icon = IconUrlOrDrawable.Drawable(R.drawable.ic_ad_insert_step1)
                    )
                }
            )
        }
        AvHorizontalChipGroup(
            modifier = Modifier.fillMaxWidth(),
            chips = chips.toPersistentList(),
            onChipClicked = {
                chips = ChipStateHandler.handleMonoSelect(chips, it)
            }
        )
    }
}
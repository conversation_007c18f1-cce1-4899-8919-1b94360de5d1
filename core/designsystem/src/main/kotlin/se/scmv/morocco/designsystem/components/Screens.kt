package se.scmv.morocco.designsystem.components

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Button
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import se.scmv.morocco.designsystem.R
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.dimens

@Composable
fun NotConnectedScreen(
    modifier: Modifier = Modifier,
    @StringRes title: Int = R.string.account_master_screen_not_connected_title,
    @StringRes description: Int = R.string.account_master_screen_not_connected_description,
    @DrawableRes image: Int = R.drawable.img_sign_in_required,
    onLoginClicked: () -> Unit
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(MaterialTheme.dimens.big),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = stringResource(id = title),
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            textAlign = TextAlign.Center
        )
        Spacer(modifier = Modifier.height(MaterialTheme.dimens.small))
        Text(
            text = stringResource(id = description),
            style = MaterialTheme.typography.bodySmall,
            fontWeight = FontWeight.Normal,
            textAlign = TextAlign.Center
        )
        Spacer(modifier = Modifier.height(MaterialTheme.dimens.big))
        Image(
            modifier = Modifier
                .width(200.dp)
                .height(200.dp),
            painter = painterResource(id = image),
            contentDescription = null,
            contentScale = ContentScale.Crop
        )
        Spacer(modifier = Modifier.height(MaterialTheme.dimens.large))
        Button(onClick = onLoginClicked) {
            Text(text = stringResource(id = R.string.common_login))
        }
    }
}

@Preview
@Composable
private fun NotConnectedScreenPreview() {
    AvitoTheme {
        Surface {
            NotConnectedScreen(onLoginClicked = {})
        }
    }
}

@Composable
fun ScreenEmptyState(
    modifier: Modifier = Modifier,
    @DrawableRes image: Int = R.drawable.img_empty_state_orders,
    @StringRes title: Int = R.string.order_screen_empty_state_title,
    @StringRes description: Int = R.string.order_screen_empty_state_description,
    @StringRes actionText: Int = R.string.order_screen_empty_state_action,
    onActionClicked: () -> Unit,
) {
    Column(
        modifier = modifier
            .padding(horizontal = MaterialTheme.dimens.screenPaddingHorizontal),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Image(
            modifier = Modifier
                .width(200.dp)
                .height(200.dp),
            painter = painterResource(id = image),
            contentDescription = null,
            contentScale = ContentScale.Crop
        )
        Spacer(modifier = Modifier.height(MaterialTheme.dimens.big))
        Text(
            text = stringResource(id = title),
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            textAlign = TextAlign.Center
        )
        Spacer(modifier = Modifier.height(MaterialTheme.dimens.small))
        Text(
            text = stringResource(id = description),
            style = MaterialTheme.typography.bodySmall,
            fontWeight = FontWeight.Normal,
            textAlign = TextAlign.Center
        )
        Spacer(modifier = Modifier.height(MaterialTheme.dimens.big))
        Button(onClick = onActionClicked) {
            Text(text = stringResource(id = actionText))
        }
    }
}

@Composable
fun ScreenErrorState(
    modifier: Modifier = Modifier,
    title: String ,
    description: String,
    image: Int = R.drawable.img_unexpected_error,
    actionText: String,
    onActionClicked: () -> Unit
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(MaterialTheme.dimens.big),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = title,
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            textAlign = TextAlign.Center
        )
        Spacer(modifier = Modifier.height(MaterialTheme.dimens.small))
        Text(
            text = description,
            style = MaterialTheme.typography.bodySmall,
            fontWeight = FontWeight.Normal,
            textAlign = TextAlign.Center
        )
        Spacer(modifier = Modifier.height(MaterialTheme.dimens.big))
        Image(
            modifier = Modifier
                .width(200.dp)
                .height(200.dp),
            painter = painterResource(id = image),
            contentDescription = null,
            contentScale = ContentScale.Crop
        )
        Spacer(modifier = Modifier.height(MaterialTheme.dimens.large))
        Button(onClick = onActionClicked) {
            Text(text = actionText)
        }
    }
}



@Preview
@Composable
private fun ScreenErrorStatePreview() {
    AvitoTheme {
        Surface {
            ScreenErrorState(
                title = "Unexpected error",
                description = "Something went wrong while requesting your orders",
                actionText = "Refresh",
                image = R.drawable.img_unexpected_error,
                onActionClicked = {}
            )
        }
    }
}

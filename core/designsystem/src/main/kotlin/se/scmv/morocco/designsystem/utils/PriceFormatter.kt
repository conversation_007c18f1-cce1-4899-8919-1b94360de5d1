package se.scmv.morocco.designsystem.utils

import se.scmv.morocco.common.lang.LocaleManager

object PriceFormatter {


    /**
     * Formats the given price with currency for visual representation.
     *
     * This function takes a price string with currency and formats it for display purposes.
     * It splits the input string into price and currency components, then formats the price
     * by adding a delimiter between every three digits (or other specified delimiter).
     * The currency is then appended to the formatted price.
     *
     * @param priceWithCurrency The input string containing the price and currency, separated by a space.
     * @param delimiter The delimiter to use for separating groups of digits in the formatted price.
     *        Defaults to a single space.
     * @return The formatted price string with currency, ready for visual representation.
     */
    fun formatForDisplay(priceWithCurrency: String, delimiter: String = " "): String {

        // Extract currency and price from the input string.
        val currency = priceWithCurrency.split(" ").getOrNull(1)
        val price = priceWithCurrency.split(" ").getOrNull(0)

        // If the price is null or already formatted, return it as is.
        if (price == null || price.length == 3) return priceWithCurrency

        // Reverse the price string and chunk it into groups of three digits.
        var chunked = price.reversed().chunked(3)

        // If the locale is Arabic, reverse the chunks again to maintain correct order.
        if (LocaleManager.isAr()) {
            chunked = chunked.reversed()
        }

        // Join the chunks with the delimiter and reverse the result to get the final formatted price.
        val formattedPrice = chunked.joinToString(delimiter).reversed()

        // If currency exists, append it to the formatted price; otherwise, return the formatted price alone.
        return currency?.let {
            String.format("%s %s", formattedPrice, it)
        } ?: formattedPrice
    }
}
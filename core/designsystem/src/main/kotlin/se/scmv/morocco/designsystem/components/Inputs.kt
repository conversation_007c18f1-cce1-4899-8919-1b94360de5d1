package se.scmv.morocco.designsystem.components

import android.content.res.Configuration
import androidx.annotation.StringRes
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextFieldColors
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import se.scmv.morocco.designsystem.R
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.Gray500
import se.scmv.morocco.designsystem.theme.dimens


val avTextFieldColors: TextFieldColors
    @Composable
    get() = TextFieldDefaults.colors(
        focusedContainerColor = MaterialTheme.colorScheme.surfaceContainer,
        unfocusedContainerColor = MaterialTheme.colorScheme.surfaceContainer,
        disabledContainerColor = MaterialTheme.colorScheme.outline,
        unfocusedIndicatorColor = Color.Transparent,
        disabledIndicatorColor = Color.Transparent,
    )

val avOutlinedTextFieldColors: TextFieldColors
    @Composable
    get() = TextFieldDefaults.colors(
        focusedContainerColor = Color.Transparent,
        unfocusedContainerColor = Color.Transparent,
        disabledContainerColor = MaterialTheme.colorScheme.outline,
        unfocusedIndicatorColor = MaterialTheme.colorScheme.outline,
        disabledIndicatorColor = Color.Transparent,
    )

val avReadOnlyTextFieldColors: TextFieldColors
    @Composable
    get() = TextFieldDefaults.colors(
        disabledContainerColor = MaterialTheme.colorScheme.surfaceContainer,
        unfocusedIndicatorColor = MaterialTheme.colorScheme.surfaceContainer,
        disabledIndicatorColor = MaterialTheme.colorScheme.surfaceContainer,
        disabledTextColor = MaterialTheme.colorScheme.onBackground,
        focusedContainerColor = Color.Transparent,
        unfocusedContainerColor = Color.Transparent,
    )

val avReadOnlyDisabledTextFieldColors: TextFieldColors
    @Composable
    get() = TextFieldDefaults.colors(
        focusedContainerColor = Color.Transparent,
        unfocusedContainerColor = Color.Transparent,
        disabledContainerColor = Color.Transparent,
        disabledTextColor = MaterialTheme.colorScheme.onBackground,
        unfocusedIndicatorColor = MaterialTheme.colorScheme.outline,
        disabledIndicatorColor = MaterialTheme.colorScheme.outline,
    )


@Composable
fun AvTextField(
    modifier: Modifier = Modifier,
    value: String,
    onValueChanged: (String) -> Unit,
    required: Boolean = false,
    enabled: Boolean = true,
    readOnly: Boolean = false,
    @StringRes title: Int? = null,
    titleArgs: ImmutableList<Any> = persistentListOf<Any>(),
    @StringRes placeholder: Int? = null,
    error: String? = null,
    leadingIcon: ( @Composable () -> Unit)? = null,
    trailingIcon: ( @Composable () -> Unit)? = null,
    visualTransformation: VisualTransformation = VisualTransformation.None,
    keyboardOptions: KeyboardOptions = KeyboardOptions.Default,
    keyboardActions: KeyboardActions = KeyboardActions.Default,
    maxLines: Int = 1,
    minLines: Int = 1,
    singleLine: Boolean = false,
    shape: Shape = MaterialTheme.shapes.small,
    colors: TextFieldColors = avTextFieldColors
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium)
    ) {
        title?.let {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(2.dp)
            ) {
                if (required) {
                    Text(
                        text = "*",
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.error
                    )
                }
                Text(
                    text = stringResource(id = it, *titleArgs.toTypedArray()),
                    style = MaterialTheme.typography.titleSmall
                )
            }
        }
        OutlinedTextField(
            modifier = modifier,
            value = value,
            onValueChange = onValueChanged,
            enabled = enabled,
            readOnly = readOnly,
            textStyle = MaterialTheme.typography.bodyMedium,
            placeholder = placeholder?.let {
                {
                    Text(
                        text = stringResource(id = it),
                        style = MaterialTheme.typography.bodyMedium,
                        color = Gray500
                    )
                }
            },
            leadingIcon = leadingIcon,
            trailingIcon = trailingIcon,
            supportingText = error?.let {
                {
                    Text(
                        text = it,
                        style = MaterialTheme.typography.titleSmall
                    )
                }
            },
            visualTransformation = visualTransformation,
            keyboardOptions = keyboardOptions,
            keyboardActions = keyboardActions,
            isError = error != null,
            singleLine = singleLine,
            maxLines = maxLines,
            minLines = minLines,
            shape = shape,
            colors = colors
        )
    }
}

@Preview(showSystemUi = true)
@Preview(showBackground = true,
    uiMode = Configuration.UI_MODE_NIGHT_YES or Configuration.UI_MODE_TYPE_NORMAL
)
@Composable
private fun AvTextFieldPreview() {
    AvitoTheme {
        Surface {
            Column(
                modifier = Modifier.verticalScroll(state = rememberScrollState()),
                verticalArrangement = Arrangement.spacedBy(10.dp)
            ) {
                var text by remember { mutableStateOf("") }
                AvTextField(
                    value = text,
                    onValueChanged = { text = it },
                    title = R.string.private_account_sign_in_screen_email_or_phone_field_label,
                    placeholder = R.string.private_account_sign_in_screen_email_or_phone_field_label,
                )
                AvTextField(
                    value = "Error text",
                    onValueChanged = { text = it },
                    required = true,
                    title = R.string.private_account_sign_in_screen_email_or_phone_field_label,
                    error = "Email invalid"
                )
                AvTextField(
                    value = "Text with trailing icon",
                    onValueChanged = { text = it },
                    title = R.string.private_account_sign_in_screen_email_or_phone_field_label,
                    trailingIcon = {
                        Icon(
                            modifier = Modifier.size(MaterialTheme.dimens.bigger),
                            painter = painterResource(id =  R.drawable.ic_time),
                            contentDescription = null,
                            tint = Color.Green
                        )
                    }
                )
                AvTextField(
                    modifier = Modifier.fillMaxWidth(),
                    value = "Rounded text",
                    onValueChanged = { text = it },
                    title = R.string.edit_shop_account_screen_phone_label,
                    titleArgs = persistentListOf(1),
                    shape = MaterialTheme.shapes.extraLarge
                )
                AvTextField(
                    modifier = Modifier.fillMaxWidth(),
                    value = "disabled",
                    onValueChanged = { text = it },
                    enabled = false,
                    title = R.string.private_account_sign_in_screen_email_or_phone_field_label,
                    shape = MaterialTheme.shapes.extraLarge
                )
                AvTextField(
                    modifier = Modifier.fillMaxWidth(),
                    value = "Rounded text with customized colors",
                    onValueChanged = { text = it },
                    title = R.string.edit_shop_account_screen_phone_label,
                    titleArgs = persistentListOf(1),
                    shape = MaterialTheme.shapes.extraLarge,
                    colors = avTextFieldColors.copy(
                        focusedContainerColor = Color.Transparent,
                        unfocusedContainerColor = Color.Transparent,
                        unfocusedIndicatorColor = MaterialTheme.colorScheme.outline
                    ),
                )
                AvTextField(
                    modifier = Modifier.fillMaxWidth(),
                    value = "avOutlinedTextFieldColors",
                    onValueChanged = { text = it },
                    title = R.string.edit_shop_account_screen_phone_label,
                    titleArgs = persistentListOf(1),
                    shape = MaterialTheme.shapes.extraLarge,
                    colors = avOutlinedTextFieldColors,
                )
                AvTextField(
                    modifier = Modifier.fillMaxWidth(),
                    value = "avReadOnlyTextFieldColors",
                    readOnly = false,
                    enabled = false,
                    onValueChanged = { text = it },
                    title = R.string.edit_shop_account_screen_phone_label,
                    titleArgs = persistentListOf(1),
                    shape = MaterialTheme.shapes.extraLarge,
                    colors = avReadOnlyTextFieldColors,
                )
                AvTextField(
                    modifier = Modifier.fillMaxWidth(),
                    value = "avReadOnlyDisabledTextFieldColors",
                    readOnly = false,
                    enabled = false,
                    onValueChanged = { text = it },
                    title = R.string.edit_shop_account_screen_phone_label,
                    titleArgs = persistentListOf(1),
                    shape = MaterialTheme.shapes.extraLarge,
                    colors = avReadOnlyDisabledTextFieldColors,
                )
            }
        }
    }
}

@Composable
fun AvPasswordField(
    modifier: Modifier = Modifier,
    value: String,
    onValueChanged: (String) -> Unit,
    enabled: Boolean = true,
    @StringRes title: Int? = null,
    @StringRes placeholder: Int? = null,
    error: String? = null,
    keyboardOptions: KeyboardOptions = KeyboardOptions.Default,
    keyboardActions: KeyboardActions = KeyboardActions.Default,
    maxLines: Int = 1,
    shape: Shape = MaterialTheme.shapes.small
) {
    var showPassword by remember { mutableStateOf(false) }
    Column(
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium)
    ) {
        title?.let {
            Text(
                text = stringResource(id = it),
                style = MaterialTheme.typography.titleSmall
            )
        }
        OutlinedTextField(
            modifier = modifier,
            value = value,
            onValueChange = onValueChanged,
            enabled = enabled,
            textStyle = MaterialTheme.typography.bodyMedium,
            placeholder = placeholder?.let {
                {
                    Text(
                        text = stringResource(id = it),
                        style = MaterialTheme.typography.bodyMedium,
                        color = Gray500
                    )
                }
            },
            trailingIcon = {
                IconButton(onClick = { showPassword = showPassword.not() }) {
                    Icon(
                        modifier = Modifier.size(MaterialTheme.dimens.bigger),
                        painter = painterResource(
                            id = if (showPassword) R.drawable.ic_eye_off else R.drawable.ic_eye_on
                        ),
                        contentDescription = "Show/Hide password",
                    )
                }
            },
            supportingText = error?.let {
                {
                    Text(
                        text = it,
                        style = MaterialTheme.typography.titleSmall
                    )
                }
            },
            visualTransformation = if (showPassword) {
                VisualTransformation.None
            } else PasswordVisualTransformation(),
            keyboardOptions = keyboardOptions.copy(keyboardType = KeyboardType.Password),
            keyboardActions = keyboardActions,
            isError = error != null,
            maxLines = maxLines,
            shape = shape,
            colors = avTextFieldColors
        )
    }
}

@Preview
@Composable
private fun AvPasswordFieldPreview() {
    AvitoTheme {
        Surface {
            var password by remember { mutableStateOf("") }
            AvPasswordField(
                value = password,
                onValueChanged = { password = it }
            )
        }
    }
}
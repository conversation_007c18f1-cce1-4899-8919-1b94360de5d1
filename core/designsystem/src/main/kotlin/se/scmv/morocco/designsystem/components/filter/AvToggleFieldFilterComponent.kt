package se.scmv.morocco.designsystem.components.filter

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.DateRange
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Switch
import androidx.compose.material3.SwitchDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.layoutId
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.ConstraintSet
import androidx.constraintlayout.compose.Dimension
import coil.compose.AsyncImage
import coil.decode.SvgDecoder
import coil.request.ImageRequest
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.Gray100
import se.scmv.morocco.designsystem.theme.Gray150
import se.scmv.morocco.designsystem.theme.dimens

@Composable
fun AvToggleFieldFilter(
    modifier: Modifier = Modifier,
    isPreviewMode: Boolean = false,
    iconUrl: String = "",
    isChecked: Boolean = false,
    label: String,
    description: String,
    key: String,
    onItemChecked: (Pair<String, Boolean>) -> Unit
){

    val constraints = ConstraintSet {
        val iconRef = createRefFor(AvToggleFieldFilterComponentConstant.ICON_REF)
        val labelRef = createRefFor(AvToggleFieldFilterComponentConstant.LABEL_REF)
        val toggleButtonRef = createRefFor(AvToggleFieldFilterComponentConstant.TOGGLE_BUTTON_REF)
        val descriptionRef = createRefFor(AvToggleFieldFilterComponentConstant.DESCRIPTION_LABLE_REF)

        constrain(iconRef){
            centerVerticallyTo(parent)
            start.linkTo(parent.start)
            width = Dimension.value(40.dp)
            height = Dimension.value(30.dp)
        }

        constrain(labelRef){
            centerVerticallyTo(parent)
            start.linkTo(iconRef.end)
        }

        constrain(toggleButtonRef){
            centerVerticallyTo(parent)
            end.linkTo(parent.end)
        }

        constrain(descriptionRef){
            top.linkTo(labelRef.bottom)
            start.linkTo(parent.start)
        }
    }
    ConstraintLayout(
        constraintSet = constraints,
        modifier = modifier.fillMaxWidth()
    ) {
        if (isPreviewMode){
            Icon(
                modifier = Modifier
                    .padding(horizontal = MaterialTheme.dimens.medium)
                    .layoutId(AvToggleFieldFilterComponentConstant.ICON_REF),
                imageVector = Icons.Default.DateRange,
                contentDescription = null
            )
        }else{
            AsyncImage(
                modifier = Modifier
                    .padding(horizontal = MaterialTheme.dimens.medium)
                    .layoutId(AvToggleFieldFilterComponentConstant.ICON_REF),
                model = ImageRequest.Builder(LocalContext.current)
                    .data(iconUrl)
                    .decoderFactory(SvgDecoder.Factory())
                    .build(),
                colorFilter = ColorFilter.tint(color = MaterialTheme.colorScheme.onSurface),
                contentDescription = null
            )
        }
        Switch(
            modifier = Modifier
                .scale(0.70f)
                .layoutId(AvToggleFieldFilterComponentConstant.TOGGLE_BUTTON_REF),
            checked = isChecked,
            onCheckedChange = {
                onItemChecked(Pair(key, it))
            },
            thumbContent = {
                if (isChecked){
                    Icon(
                        imageVector = Icons.Default.CheckCircle,
                        contentDescription = null
                    )
                }
            },
            colors = SwitchDefaults.colors(
                checkedIconColor = MaterialTheme.colorScheme.onPrimary,
                uncheckedIconColor = Gray100,
                checkedBorderColor = MaterialTheme.colorScheme.primary,
                uncheckedBorderColor = Gray150,
                checkedThumbColor = MaterialTheme.colorScheme.primary,
                uncheckedTrackColor = Gray100
            )
        )
        Text(
            modifier = Modifier
                .layoutId(AvToggleFieldFilterComponentConstant.LABEL_REF),
            text = label,
            textAlign = TextAlign.Start,
            fontWeight = FontWeight.Bold,
            fontSize = MaterialTheme.typography.titleSmall.fontSize
        )
        Text(
            modifier = modifier
                .padding(
                    start = MaterialTheme.dimens.medium,
                    end = MaterialTheme.dimens.medium
                )
                .layoutId(AvToggleFieldFilterComponentConstant.DESCRIPTION_LABLE_REF),
            text = description,
            maxLines = 3,
            fontSize = MaterialTheme.typography.bodyMedium.fontSize,
            textAlign = TextAlign.Start,
            color = MaterialTheme.colorScheme.outline
        )
    }
}

@Preview
@Composable
fun AvToggleFieldFilterPreview(){
    AvitoTheme {
        AvToggleFieldFilter(
            label = "Annonce avec livraison",
            isPreviewMode = true,
            description = "Afficher seulement les annonces avec livraison",
            key = "hasPrice"
        ){

        }
    }
}

object AvToggleFieldFilterComponentConstant{
    const val ICON_REF = "iconref"
    const val LABEL_REF = "labelref"
    const val TOGGLE_BUTTON_REF = "togglebuttonref"
    const val DESCRIPTION_LABLE_REF = "descriptionlabelref"
}
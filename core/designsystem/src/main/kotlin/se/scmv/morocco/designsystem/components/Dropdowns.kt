package se.scmv.morocco.designsystem.components

import android.content.res.Configuration
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ExposedDropdownMenuBox
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.MenuAnchorType
import androidx.compose.material3.Text
import androidx.compose.material3.TextFieldColors
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Immutable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.toImmutableList
import se.scmv.morocco.designsystem.R
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.Blue500
import se.scmv.morocco.designsystem.theme.White
import se.scmv.morocco.designsystem.theme.dimens

@Immutable
data class DropdownData(val id: String, val name: String)

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AvDropdown(
    items: ImmutableList<DropdownData>,
    selectedItem: DropdownData?,
    onItemSelected: (DropdownData) -> Unit,
    modifier: Modifier = Modifier,
    title: String? = null,
    placeholder: Int? = null,
    required: Boolean = false,
    error: String? = null,
    colors: TextFieldColors = avReadOnlyDisabledTextFieldColors,
) {
    // Controls dropdown visibility
    var expanded by remember { mutableStateOf(false) }
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium)
    ) {
        title?.let {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(2.dp)
            ) {
                if (required) {
                    Text(
                        text = "*",
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.error
                    )
                }
                Text(
                    text = it,
                    style = MaterialTheme.typography.titleSmall,
                )
            }
        }
        ExposedDropdownMenuBox(
            modifier = Modifier.fillMaxWidth().wrapContentHeight(),
            expanded = expanded,
            onExpandedChange = { expanded = it }
        ) {
            AvTextField(
                modifier = Modifier.fillMaxWidth().menuAnchor(MenuAnchorType.PrimaryNotEditable),
                value = selectedItem?.name.orEmpty(),
                onValueChanged = {},
                readOnly = true,
                enabled = false,
                placeholder = placeholder,
                trailingIcon = {
                    Icon(
                        modifier = Modifier.size(MaterialTheme.dimens.default),
                        painter = painterResource(id = if (expanded) R.drawable.ic_dropdown_up else R.drawable.ic_dropdown_down),
                        contentDescription = null,
                    )
                },
                colors = colors,
            )
            ExposedDropdownMenu(
                expanded = expanded,
                onDismissRequest = { expanded = false },
            ) {
                val sizeOfOneItem by remember {
                    mutableStateOf(48.dp) //assuming height of one menu item 50dp
                }
                val configuration = LocalConfiguration.current
                val dimens = MaterialTheme.dimens
                val screenHeight50 by remember {
                    val screenHeight = configuration.screenHeightDp.dp
                    mutableStateOf(screenHeight / 2) //assuming the drop down menu anchor is in middle of the screen. This is the maximum height that popup menu can take.
                }
                val height by remember(items.size) {
                    val itemsSize = sizeOfOneItem * items.size
                    mutableStateOf(minOf(itemsSize, screenHeight50))
                }
                val width by remember { mutableStateOf(configuration.screenWidthDp.dp - dimens.screenPaddingHorizontal) }
                LazyColumn(
                    modifier = Modifier
                        .width(width)
                        .height(height),
                ) {
                    items(items, key = { it.id }) { item ->
                        val selected = item == selectedItem
                        val bgColor = if (selected) {
                            Blue500
                        } else Color.Unspecified
                        DropdownMenuItem(
                            modifier = Modifier.background(bgColor),
                            text = {
                                val textColor = if (selected) {
                                    White
                                } else Color.Unspecified
                                Text(
                                    text = item.name,
                                    style = MaterialTheme.typography.bodyLarge,
                                    color = textColor
                                )
                            },
                            onClick = {
                                expanded = false
                                onItemSelected(item)
                            },
                        )
                    }
                }
            }
        }
        error?.let {
            Text(
                text = error,
                color = MaterialTheme.colorScheme.error,
                style = MaterialTheme.typography.titleSmall,
            )
        }
    }
}

@Preview(showBackground = true)
@Preview(
    showBackground = true,
    uiMode = Configuration.UI_MODE_NIGHT_YES or Configuration.UI_MODE_TYPE_NORMAL
)
@Composable
private fun AvDropdownPreview() {
    val items = remember {
        List(100) {
            DropdownData("$it", "item $it")
        }.toImmutableList()
    }
    var selectedItem by remember { mutableStateOf<DropdownData?>(null) }
    AvitoTheme {
        Row (
            modifier = Modifier.fillMaxSize(),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium)
        ) {
            AvDropdown(
                modifier = Modifier.weight(1f),
                items = items,
                selectedItem = selectedItem,
                title = stringResource(R.string.common_city_field_label),
                placeholder = R.string.common_min,
                onItemSelected = { selectedItem = it }
            )
            AvDropdown(
                modifier = Modifier.weight(1f),
                items = items,
                selectedItem = selectedItem,
                title = stringResource(R.string.common_city_field_label),
                placeholder = R.string.common_max,
                onItemSelected = { selectedItem = it }
            )
        }
    }
}
package se.scmv.morocco.designsystem.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import se.scmv.morocco.designsystem.R
import se.scmv.morocco.designsystem.theme.dimens


@Composable
fun FirstMessageBottomSheet(
    sellerName: String?,
    adImageUrl: String?,
    adTitle: String,
    adPrice: String?,
    onMessageSend: (String) -> Unit
) {
    val initialText = if (adPrice.isNullOrEmpty()) stringResource(R.string.what_is_the_price) else ""
    var messageText by remember { mutableStateOf(initialText) }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = MaterialTheme.dimens.default)
            .padding(bottom = 26.dp)
    ) {
        Text(
            text = stringResource(id = R.string.send_message_to, sellerName.orEmpty()),
            fontSize = 15.sp,
            fontWeight = FontWeight.Bold
        )

        Spacer(modifier = Modifier.height(MaterialTheme.dimens.default))

        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            if (adImageUrl == null || adImageUrl.isEmpty()) {
                Box(
                    modifier = Modifier
                        .size(64.dp)
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .background(MaterialTheme.colorScheme.onSurface.copy(alpha = 0.03f)),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            painter = painterResource(R.drawable.ic_no_image),
                            contentDescription = "No Image",
                            modifier = Modifier.size(28.dp),
                            tint = MaterialTheme.colorScheme.onSurface
                        )
                    }
                }

            } else {
                AsyncImage(
                    model = adImageUrl,
                    contentDescription = null,
                    modifier = Modifier
                        .size(64.dp)
                        .clip(RoundedCornerShape(MaterialTheme.dimens.medium))
                )
            }

            Spacer(modifier = Modifier.width(MaterialTheme.dimens.regular))

            Column {
                if (!adPrice.isNullOrEmpty()) {
                    Text(
                        text = adPrice,
                        fontSize = 15.sp,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary
                    )
                }
                Text(text = adTitle, style = MaterialTheme.typography.bodyLarge)
            }
        }

        Spacer(modifier = Modifier.height(MaterialTheme.dimens.default))

        // Use ChatInput component for consistent styling
        ChatInput(
            text = messageText,
            onTextChange = { messageText = it },
            onSendMessage = {
                if (messageText.isNotBlank()) {
                    onMessageSend(messageText)
                    messageText = ""
                }
            },
            onAttachmentClick = { /* No attachment support in first message */ },
            isBlocked = false,
            isSending = false,
            modifier = Modifier.fillMaxWidth(),
            attachments = emptyList(),
            onRemoveAttachment = { /* No attachment support in first message */ },
            showAttachmentButton = false
        )
    }
}

package se.scmv.morocco.designsystem.theme

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color

private val LightColorScheme = lightColorScheme(
    primary = Blue600,
    onPrimary = White,
    background = White,
    surface = White,
    surfaceContainer = White600,
    surfaceContainerLowest = White,
    surfaceContainerLow = White600,
    surfaceContainerHighest = White600,
    onSurface = Black,
    outline = Color( 0XFFE6E6E6),
    outlineVariant = Gray200,
    tertiaryContainer = White600,
)

private val DarkColorScheme = darkColorScheme(
    primary = Blue600,
    onPrimary = White,
    background = Black,
    surface = Black,
    surfaceContainer = Black900,
    surfaceContainerLowest = Black900,
    surfaceContainerLow = Black900,
    surfaceContainerHighest = Black900,
    onSurface = White,
    outline = Black700,
    outlineVariant = Gray200,
    tertiaryContainer = White600,
)

@Composable
fun AvitoTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    // Dynamic color is available on Android 12+
    //dynamicColor: Boolean = true,
    content: @Composable () -> Unit
) {
    val colorScheme =  when {
        // Uncomment this code when we're ready to use dynamic colors (Material you)
        /*dynamicColor && Build.VERSION.SDK_INT >= Build.VERSION_CODES.S -> {
            val context = LocalContext.current
            if (darkTheme) dynamicDarkColorScheme(context) else dynamicLightColorScheme(context)
        }*/

        darkTheme -> DarkColorScheme
        else -> LightColorScheme
    }

    MaterialTheme(
        // Lets use dark theme for now
        colorScheme = colorScheme,
        typography = Typography,
        shapes = Shapes,
        content = content
    )
}
package se.scmv.morocco.designsystem.components

import android.content.res.Configuration
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.BasicAlertDialog
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import se.scmv.morocco.designsystem.R
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.Black900
import se.scmv.morocco.designsystem.theme.Blue300
import se.scmv.morocco.designsystem.theme.Blue500
import se.scmv.morocco.designsystem.theme.Blue800
import se.scmv.morocco.designsystem.theme.Brown100
import se.scmv.morocco.designsystem.theme.Brown400
import se.scmv.morocco.designsystem.theme.Brown800
import se.scmv.morocco.designsystem.theme.Gray100
import se.scmv.morocco.designsystem.theme.Red200
import se.scmv.morocco.designsystem.theme.Red500
import se.scmv.morocco.designsystem.theme.Red700
import se.scmv.morocco.designsystem.theme.dimens

enum class AvAlertType(
    val bgColor: Color,
    val textColor: Color,
    val icon: ImageVector,
    val iconTint: Color,
) {
    Info(bgColor = Blue300, textColor = Blue800, Icons.Default.Info, iconTint = Blue500),
    Warning(bgColor = Brown100, textColor = Brown800, Icons.Default.Warning, iconTint = Brown400),
    Error(bgColor = Red200, textColor = Red700, Icons.Default.Info, iconTint = Red500),
}

@Composable
fun AvAlert(
    modifier: Modifier = Modifier,
    text: String,
    type: AvAlertType,
) {
    Row(
        modifier = modifier
            .background(type.bgColor, MaterialTheme.shapes.large)
            .padding(MaterialTheme.dimens.large),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.large)
    ) {
        Icon(imageVector = type.icon, contentDescription = null, tint = type.iconTint)
        Text(
            text = text,
            color = type.textColor,
            style = MaterialTheme.typography.labelLarge
        )
    }
}

@Preview
@Composable
private fun AvAlertPreview() {
    AvitoTheme {
        Surface {
            Column(verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.big)) {
                AvAlert(
                    type = AvAlertType.Info,
                    text = stringResource(R.string.shop_subscription_explanation)
                )
                AvAlert(
                    type = AvAlertType.Warning,
                    text = stringResource(R.string.shop_subscription_explanation)
                )
                AvAlert(
                    type = AvAlertType.Error,
                    text = stringResource(R.string.shop_subscription_explanation)
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AvAlertDialog(
    modifier: Modifier = Modifier,
    text: String,
    type: AvAlertType,
    onDismissRequest: () -> Unit,
) {
    BasicAlertDialog(
        modifier = modifier,
        onDismissRequest = onDismissRequest,
    ) {
        Box(
            modifier = Modifier
                .background(type.bgColor, MaterialTheme.shapes.large)
                .padding(MaterialTheme.dimens.medium),
        ) {
            AvIconButton(
                modifier = Modifier.align(Alignment.TopEnd),
                icon = Icons.Default.Close,
                onClick = onDismissRequest,
                colors = IconButtonDefaults.outlinedIconButtonColors(
                    containerColor = Gray100,
                    contentColor = Black900
                )
            )
            Column(
                modifier = Modifier.padding(top = MaterialTheme.dimens.extraBig, bottom = MaterialTheme.dimens.big),
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.large),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Icon(
                    modifier = Modifier.size(30.dp),
                    imageVector = type.icon,
                    contentDescription = null, tint = type.iconTint
                )
                Text(
                    text = text,
                    color = type.textColor,
                    style = MaterialTheme.typography.bodyLarge,
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}

@Preview
@Composable
private fun AvAlertDialogPreview() {
    AvitoTheme {
        Surface {
            Box(modifier = Modifier.fillMaxSize()) {
                AvAlertDialog(
                    type = AvAlertType.Info,
                    text = stringResource(R.string.shop_subscription_explanation),
                    onDismissRequest = {}
                )
            }
        }
    }
}

@Composable
fun AvConfirmationAlertDialog(
    modifier: Modifier = Modifier,
    title: String,
    description: String,
    type: AvAlertType? = null,
    confirmText: String = stringResource(R.string.common_yes),
    onConfirm: () -> Unit,
    cancelText: String = stringResource(R.string.common_no),
    onCancel: () -> Unit,
    properties: DialogProperties = DialogProperties()
) {
    AlertDialog(
        modifier = modifier,
        icon = type?.let {
            {
                Icon(
                    modifier = Modifier.size(40.dp),
                    imageVector = it.icon,
                    contentDescription = null, tint = type.iconTint
                )
            }
        },
        title = {
            Text(text = title, style = MaterialTheme.typography.titleMedium)
        },
        text = {
            Text(text = description, style = MaterialTheme.typography.bodyMedium)
        },
        confirmButton = {
            AvPrimaryButton(text = confirmText, onClick = onConfirm)
        },
        dismissButton = {
            AvSecondaryButton(
                modifier = Modifier.padding(horizontal = MaterialTheme.dimens.small),
                text = cancelText,
                onClick = onCancel
            )
        },
        onDismissRequest = onCancel,
        shape = MaterialTheme.shapes.large,
        containerColor = MaterialTheme.colorScheme.surfaceContainerLowest,
        titleContentColor = MaterialTheme.colorScheme.onBackground,
        textContentColor = MaterialTheme.colorScheme.onBackground,
        properties = properties
    )
}

@Preview(uiMode = Configuration.UI_MODE_NIGHT_YES or Configuration.UI_MODE_TYPE_NORMAL)
@Composable
private fun AvConfirmationAlertDialogPreview() {
    AvitoTheme {
        Surface {
            Box(modifier = Modifier.fillMaxSize()) {
                AvConfirmationAlertDialog(
                    modifier = Modifier,
                    title = "Cancel order",
                    description = "Are you sure you want to cancel this order?",
                    type = AvAlertType.Info,
                    confirmText = "Yes",
                    onConfirm = {},
                    cancelText = "No",
                    onCancel = {},
                )
            }
        }
    }
}

@Composable
fun AvProgressBar(
    text: String,
    properties: DialogProperties = DialogProperties(
        dismissOnClickOutside = false,
        dismissOnBackPress = false
    )
) {
    Dialog(
        onDismissRequest = {},
        properties = properties
    ) {
        Row(
            modifier = Modifier
                .background(MaterialTheme.colorScheme.background, MaterialTheme.shapes.large)
                .padding(MaterialTheme.dimens.default),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium),
            verticalAlignment = Alignment.CenterVertically
        ) {
            CircularProgressIndicator()
            Text(text = text)
        }
    }
}

@Preview
@Composable
private fun AvProgressBarPreview() {
    AvitoTheme {
        Surface {
            Box(modifier = Modifier.fillMaxSize()) {
                AvProgressBar(text = stringResource(R.string.order_screen_cancel_order_processing))
            }
        }
    }
}
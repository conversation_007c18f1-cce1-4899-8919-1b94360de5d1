package se.scmv.morocco.designsystem.components

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.material3.Icon
import androidx.compose.material3.LeadingIconTab
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.TabRow
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.toImmutableList
import se.scmv.morocco.designsystem.R
import se.scmv.morocco.designsystem.theme.AvitoTheme

/**
 * Data class representing the information required to define a tab.
 *
 * @property text The string resource ID for the tab's text.
 * @property icon The drawable resource ID for the tab's icon, or `null` if no icon is used.
 */
@Stable
data class TabData(
    @StringRes val text: Int,
    @DrawableRes val icon: Int? = null
)

/**
 * A composable function that displays a customizable tab row with text and optional icons.
 *
 * @param modifier Modifier to be applied to the TabRow.
 * @param tabs An immutable list of [TabData] objects, each defining a tab's text and optional icon.
 * @param selectedIndex The index of the currently selected tab.
 * @param onTabClicked A lambda function invoked with the index of the clicked tab.
 */
@Composable
fun AvTabs(
    modifier: Modifier = Modifier,
    tabs: ImmutableList<TabData>,
    selectedIndex: Int,
    onTabClicked: (Int) -> Unit
) {
    TabRow(
        modifier = modifier,
        selectedTabIndex = selectedIndex,
        divider = {}
    ) {
        tabs.forEachIndexed { index, tab ->
            val selected = index == selectedIndex
            AvTab(
                selected = selected,
                tab = tab,
                onTabClicked = { onTabClicked(index) }
            )
        }
    }
}

/**
 * A composable function that represents a single tab with an optional icon and text.
 *
 * This tab can display a selected or unselected state based on the `selected` parameter.
 * When clicked, the provided `onTabClicked` callback is invoked.
 *
 * @param selected Indicates whether the tab is currently selected.
 * @param tab A [TabData] object that defines the tab's text and optional icon.
 * @param onTabClicked A lambda function invoked when the tab is clicked.
 */
@Composable
fun AvTab(
    selected: Boolean,
    tab: TabData,
    onTabClicked: () -> Unit
) {
    LeadingIconTab(
        selected = selected,
        text = {
            Text(
                text = stringResource(id = tab.text),
                style = MaterialTheme.typography.bodyLarge,
                color = if (selected) MaterialTheme.colorScheme.primary else Color.Unspecified
            )
        },
        icon = {
            tab.icon?.let { icon ->
                Icon(
                    painter = painterResource(icon),
                    contentDescription = null,
                    tint = if (selected) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onBackground.copy(0.4f)
                )
            }
        },
        selectedContentColor = MaterialTheme.colorScheme.onBackground,
        unselectedContentColor = MaterialTheme.colorScheme.onBackground.copy(alpha = 0.6f),
        onClick = onTabClicked
    )
}


@Preview
@Composable
private fun AvTabsPreview() {
    AvitoTheme {
        Surface {
            var selectedIndex by remember { mutableIntStateOf(0) }
            AvTabs(
                tabs = listOf(
                    TabData(
                        text = R.string.private_account_auth_screen_sign_in_tab,
                        icon = R.drawable.ic_heart
                    ),
                    TabData(
                        text = R.string.private_account_auth_screen_sign_up_tab,
                        icon = R.drawable.ic_star
                    ),
                ).toImmutableList(),
                selectedIndex = selectedIndex
            ) {
                selectedIndex = it
            }
        }
    }
}
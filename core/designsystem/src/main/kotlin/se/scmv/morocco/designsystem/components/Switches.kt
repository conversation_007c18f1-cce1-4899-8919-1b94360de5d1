package se.scmv.morocco.designsystem.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import se.scmv.morocco.designsystem.R
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.Green300
import se.scmv.morocco.designsystem.theme.dimens

@Composable
fun AvSwitch(
    modifier: Modifier = Modifier,
    checked: Boolean,
    title: Int? = null,
    onCheckChanged: (Boolean) -> Unit
) {
    AvSwitch(
        modifier = modifier,
        checked = checked,
        title = title?.let { stringResource(it) },
        onCheckChanged = onCheckChanged
    )
}

@Composable
fun AvSwitch(
    modifier: Modifier = Modifier,
    checked: Boolean,
    title: String? = null,
    onCheckChanged: (Boolean) -> Unit
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        title?.let {
            Text(
                modifier = Modifier.weight(1f),
                text = it,
                style = MaterialTheme.typography.titleSmall
            )
        }
        Switch(
            checked = checked,
            onCheckedChange = onCheckChanged,
        )
    }
}

@Composable
@Preview(showBackground = true)
fun AvSwitchPreview() {
    AvitoTheme {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .background(Green300, CircleShape),
                contentAlignment = Alignment.Center
            ) {
                Image(
                    painter = painterResource(R.drawable.ic_calls_circle),
                    contentDescription = null
                )
            }
            AvSwitch(
                checked = true,
                title = "At vero eos et accusamus et iusto odio dignissimos ducimus, qui blanditiis praesentium voluptatum",
                onCheckChanged = { },
            )
        }
    }
}

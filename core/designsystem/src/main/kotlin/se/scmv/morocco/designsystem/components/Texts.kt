package se.scmv.morocco.designsystem.components

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import se.scmv.morocco.designsystem.R
import se.scmv.morocco.designsystem.theme.AvitoTheme

@Composable
fun AvScreenTitle(
    modifier: Modifier = Modifier,
    @StringRes title: Int,
    color: Color = Color.Unspecified,
    style: TextStyle = MaterialTheme.typography.titleLarge,
    textAlign: TextAlign = TextAlign.Start
) {
    Text(
        modifier = modifier,
        text = stringResource(id = title),
        color = color,
        style = style,
        textAlign = textAlign
    )
}

@Composable
fun AvScreenSubTitle(
    modifier: Modifier = Modifier,
    @StringRes title: Int,
    vararg args: Any,
    color: Color = Color.Unspecified,
    style: TextStyle = MaterialTheme.typography.bodyMedium,
    textAlign: TextAlign = TextAlign.Start
) {
    Text(
        modifier = modifier,
        text = stringResource(id = title, *args),
        color = color,
        style = style,
        textAlign = textAlign
    )
}

@Composable
fun AvResponsiveText(
    text: String,
    modifier: Modifier = Modifier,
    maxLength: Int = 10,
    defaultFontSize: TextUnit = 16.sp,
    reducedFontSize: TextUnit = 12.sp,
    color: Color = Color.White
){
    val fontSize = if (text.length > maxLength) reducedFontSize else defaultFontSize

    Text(
        modifier = modifier,
        text = text,
        fontSize = fontSize,
        color = color
    )
}

@Composable
fun AvTextWithEndIcon(
    modifier: Modifier = Modifier,
    textColor: Color = Color.Unspecified,
    iconTint: Color = Color.Unspecified,
    text: String,
    @DrawableRes iconRes: Int
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier.wrapContentWidth()
    ) {
        Text(
            text = text,
            maxLines = 1,
            color = textColor,
            overflow = TextOverflow.Ellipsis,
        )
        Spacer(modifier = Modifier.width(4.dp))
        Icon(
            painter = painterResource(id = iconRes),
            contentDescription = null,
            modifier = Modifier.size(16.dp),
            tint = iconTint
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun TextWithEndIconPreview() {
    AvTextWithEndIcon(text = "Text with end icon", iconRes = R.drawable.ic_upload)
}

@Composable
fun AvDynamicText(
    text: String,
    modifier: Modifier = Modifier,
    color: Color = Color.Unspecified,
    style: TextStyle = MaterialTheme.typography.bodyLarge,
    maxLines: Int = 1,
    overflow: TextOverflow = TextOverflow.Ellipsis,
    textAlign: TextAlign = TextAlign.Start
) {
    Text(
        modifier = modifier,
        text = text,
        color = color,
        style = style,
        maxLines = maxLines,
        overflow = overflow,
        textAlign = textAlign
    )
}

@Preview(showSystemUi = true)
@Composable
private fun AvTextsPreview() {
    AvitoTheme {
        Column {
            AvScreenTitle(title = R.string.private_account_sign_in_screen_title)
            AvScreenSubTitle(
                title = R.string.private_account_sign_in_screen_subtitle,
                style = TextStyle.Default,
                args = arrayOf("**********")
            )
            AvResponsiveText(
                text = "this short"
            )
            AvResponsiveText(
                text = "this long text for responsive"
            )
            AvDynamicText(
                text = "Dynamic text example",
                color = MaterialTheme.colorScheme.onSurface
            )
        }
    }
}
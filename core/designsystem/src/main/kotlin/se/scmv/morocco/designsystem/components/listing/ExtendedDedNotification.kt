package se.scmv.morocco.designsystem.components.listing

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.AssistChip
import androidx.compose.material3.AssistChipDefaults
import androidx.compose.material3.ElevatedCard
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import se.scmv.morocco.designsystem.R
import se.scmv.morocco.designsystem.theme.dimens

@Composable
fun ExtendedDedNotification(
    message: String,
    buttonText: Int = R.string.extended_search_keep_current_search,
    onClick : () -> Unit
) {
    ElevatedCard(
        modifier = Modifier
            .padding(
                MaterialTheme.dimens.medium
            )
    ) {
        Box(
            modifier = Modifier.fillMaxWidth()
                .background(color = MaterialTheme.colorScheme.surfaceContainerHighest)
        ){
            Column {
                //should contain a Avito Icon and title
                Row(
                    modifier = Modifier.padding(
                        vertical = 8.dp,
                        horizontal = 16.dp
                    )
                ) {
                    Image(
                        painter = painterResource(R.drawable.avito_logo_with_loop),
                        contentDescription = "Avito Logo",
                        modifier = Modifier.size(40.dp)
                    )
                }
                //should contain a message
                Text(
                    modifier = Modifier.padding(
                        start = 16.dp,
                        end = 16.dp,
                    ),
                    text = message,
                    fontSize = MaterialTheme.typography.bodyMedium.fontSize,
                    color = MaterialTheme.colorScheme.onSurface
                )
                //Button
                AssistChip(
                    modifier = Modifier.padding(start = 16.dp),
                    onClick = {
                        onClick()
                    },
                    colors = AssistChipDefaults.assistChipColors(
                        containerColor = MaterialTheme.colorScheme.surface,
                        labelColor = MaterialTheme.colorScheme.onSurface
                    ),
                    label = {
                        Text(
                            text = stringResource(buttonText)
                        )
                    })
            }
        }
    }
}

@Preview
@Composable
fun ExtendedSearchNotificationPreview(){
    ExtendedDedNotification(
        message = "Aucune annonce trouvée selon vos filtres. Nous avons élargi votre recherche à des catégories plus vastes. Poursuivez votre exploration!"
    ){}
}

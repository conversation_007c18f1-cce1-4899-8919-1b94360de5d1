package se.scmv.morocco.designsystem.utils

import junit.framework.TestCase
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class AdPriceFormatterTest {

    @Test
    fun formatForDisplay() {

        // Test with default delimiter
        TestCase.assertEquals("1 000 DH", PriceFormatter.formatForDisplay("1000 DH"))
        TestCase.assertEquals("10 000 DH", PriceFormatter.formatForDisplay("10000 DH"))
        TestCase.assertEquals("100 000 DH", PriceFormatter.formatForDisplay("100000 DH"))
        TestCase.assertEquals("1 000 000 DH", PriceFormatter.formatForDisplay("1000000 DH"))

        // Test with different delimiter
        TestCase.assertEquals("1,000 DH", PriceFormatter.formatForDisplay("1000 DH", ","))
        TestCase.assertEquals("10,000 DH", PriceFormatter.formatForDisplay("10000 DH", ","))
        TestCase.assertEquals("100,000 DH", PriceFormatter.formatForDisplay("100000 DH", ","))
        TestCase.assertEquals("1,000,000 DH", PriceFormatter.formatForDisplay("1000000 DH", ","))

        // Test with different currency
        TestCase.assertEquals("1 000 EUR", PriceFormatter.formatForDisplay("1000 EUR"))
        TestCase.assertEquals("10 000 EUR", PriceFormatter.formatForDisplay("10000 EUR"))
        TestCase.assertEquals("100 000 EUR", PriceFormatter.formatForDisplay("100000 EUR"))
        TestCase.assertEquals("1 000 000 EUR", PriceFormatter.formatForDisplay("1000000 EUR"))

        // Test with small price (no formatting)
        TestCase.assertEquals("100 DH", PriceFormatter.formatForDisplay("100 DH"))
        TestCase.assertEquals("999 DH", PriceFormatter.formatForDisplay("999 DH"))

        // Test with null price
        TestCase.assertEquals("DH", PriceFormatter.formatForDisplay("DH"))

        // Test with null currency
        TestCase.assertEquals("1 000", PriceFormatter.formatForDisplay("1000"))
    }
}
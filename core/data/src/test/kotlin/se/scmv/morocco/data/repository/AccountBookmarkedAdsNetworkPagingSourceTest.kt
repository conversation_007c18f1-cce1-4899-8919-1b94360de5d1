package se.scmv.morocco.data.repository

import junit.framework.TestCase.assertEquals
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import se.scmv.morocco.data.repository.utils.getNextPageNumber

@RunWith(JUnit4::class)
class GetNextPageTest {

    @Test
    fun `getNextPageNumber, with remainder`() {
        assertEquals(
            2,
            getNextPageNumber(1,12, 35)
        )
        assertEquals(
            3,
            getNextPageNumber(2,12, 35)
        )
        assertEquals(
            null,
            getNextPageNumber(3,12, 35)
        )
    }

    @Test
    fun `getNextPageNumber, without remainder`() {
        assertEquals(
            2,
            getNextPageNumber(1,12, 24)
        )
        assertEquals(
            null,
            getNextPageNumber(2,12, 24)
        )
    }

    @Test
    fun `getNextPageNumber, null`() {
        assertEquals(
            null,
            getNextPageNumber(1,12, 0)
        )
        assertEquals(
            null,
            getNextPageNumber(1,0, 24)
        )
    }
}
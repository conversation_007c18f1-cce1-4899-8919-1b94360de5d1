package se.scmv.morocco.data.database.daos

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import se.scmv.morocco.data.database.entities.CategoryData
import se.scmv.morocco.data.database.entities.CategoryTree

@Dao
interface CategoryDataDao {
    @Insert(onConflict = OnConflictStrategy.IGNORE)
    fun insert(categoryData: CategoryData)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(categoryData: List<CategoryData>)

    @Update
    fun update(categoryData: CategoryData)

    @Delete
    fun delete(categoryData: CategoryData)

    @Query("SELECT * FROM CategoryData")
    fun getAll(): List<CategoryData>

    @Query("SELECT * FROM CategoryData WHERE category = :category")
    fun getByCategory(category: CategoryTree): List<CategoryData>
}
package se.scmv.morocco.data.repository.ad

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.apollographql.apollo3.ApolloClient
import se.scmv.morocco.GetListingAdsQuery
import se.scmv.morocco.analytics.AnalyticsHelper
import se.scmv.morocco.analytics.models.AnalyticsEvent
import se.scmv.morocco.analytics.models.Param
import se.scmv.morocco.common.lang.LocaleManager
import se.scmv.morocco.data.mappers.toExtendedSearchOrNull
import se.scmv.morocco.data.mappers.toPremiumAdsOrEmpty
import se.scmv.morocco.data.mappers.toPublishedOrNewConstructionAdsOrEmpty
import se.scmv.morocco.data.repository.utils.Constants
import se.scmv.morocco.data.repository.utils.wrapWithErrorHandling
import se.scmv.morocco.domain.models.ListingAd
import se.scmv.morocco.domain.models.orion.OrionBaseComponentValue
import se.scmv.morocco.domain.models.orion.OrionKeyRangeValue
import se.scmv.morocco.domain.models.orion.OrionKeyStringValue
import se.scmv.morocco.domain.models.orion.OrionMultipleSelectSmartDropDownValue
import se.scmv.morocco.domain.models.orion.OrionSingleSelectCategoryDropdownValue
import se.scmv.morocco.domain.usecases.MixListingAdsUseCase
import java.util.UUID

class AdsListingPagingSource(
    private val apolloClient: ApolloClient,
    private val analyticsHelper: AnalyticsHelper,
    private val filters: List<OrionBaseComponentValue>,
    private val extendSearch: Boolean
) : PagingSource<Int, ListingAd>() {

    companion object {
        const val START_PAGE = 0
        const val PAGE_SIZE = 35
        private const val MAX_DFP_BANNER_ADS = 2
        private const val KEY_CATEGORY_ID = "categoryId"
        private const val KEY_CITY_ID = "cityId"
        private const val KEY_CITY = "city"
        private const val KEY_MIN_PRICE = "minprice"
        private const val KEY_MAX_PRICE = "maxprice"
        private const val KEY_KEYWORD = "keyword"
    }

    private val scrollIds = mutableListOf<Pair<String?, String?>>().apply {
        add(START_PAGE, null to null)
    }

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, ListingAd> {
        // Start refresh at page 1 if undefined.
        val pageNumber = params.key ?: START_PAGE

        val publishedAndNCAdsNextScrollId = scrollIds.getOrNull(pageNumber)?.first
        val premiumAdsNextScrollId = scrollIds.getOrNull(pageNumber)?.second
        return wrapWithErrorHandling(
            call = {
                apolloClient.query(
                    AdsListingHelper.buildGetListingAdsQuery(
                        values = filters,
                        extendSearch = extendSearch,
                        isNewScroll = publishedAndNCAdsNextScrollId == null,
                        publishedAndNCAdsNextScrollId = publishedAndNCAdsNextScrollId,
                        premiumAdsNextScrollId = premiumAdsNextScrollId
                    )
                ).execute()
            },
            toData = { response ->
                response.data
            },
            toPage = { data ->
                val publishedAndNCAds = data.publishedAndNC.publishedAndNCAds
                    .toPublishedOrNewConstructionAdsOrEmpty()
                val premiumAds = data.premium.premiumAds.toPremiumAdsOrEmpty()
                val dfpBannerAds = generateDfpBannerAds(filters)
                var ads: List<ListingAd> = MixListingAdsUseCase.invoke(
                    publishedAndNewConstructionAds = publishedAndNCAds,
                    premiumAds = premiumAds,
                    dfpBannerAds = dfpBannerAds
                )
                ads = addExtendedSearchItemIfExist(ads = ads, pageNumber = pageNumber, data = data)

                val nextScrollIds = getScrollIds(data = data)
                // If the response doesn't contain a next scroll ID for normal ads,
                // we know we're at the end of the list.
                val nextKey = nextScrollIds.first?.let { pageNumber + 1 }
                nextKey?.let { scrollIds.add(it, nextScrollIds) }
                val adsCount = data.publishedAndNC.publishedAndNCAdsCount.total
                trackPageAsScreenViewEvent(adsCount)
                if (pageNumber == START_PAGE) {
                    trackSearchResultEvent(adsCount)
                }
                LoadResult.Page(
                    data = ads,
                    prevKey = null,
                    nextKey = nextKey
                )
            }
        )
    }

    override fun getRefreshKey(state: PagingState<Int, ListingAd>): Int? {
        return state.anchorPosition?.let { anchorPosition ->
            val anchorPage = state.closestPageToPosition(anchorPosition)
            anchorPage?.prevKey?.plus(1) ?: anchorPage?.nextKey?.minus(1)
        }
    }

    private fun generateDfpBannerAds(filters: List<OrionBaseComponentValue>): List<ListingAd.DfpBanner> {
        val params: Map<String, String> = getDFPCustomTarget(filters)
        return List(MAX_DFP_BANNER_ADS) {
            ListingAd.DfpBanner(
                uuid = UUID.randomUUID().toString(),
                subject = "",
                paramsValues = params
            )
        }
    }

    private fun getScrollIds(data: GetListingAdsQuery.Data): Pair<String?, String?> {
        // As discussed with the backend team, if the the size of the ads in the response
        // is less than the page size ==> END OF PAGINATION.
        // Discussion link: https://avito-ny73802.slack.com/archives/C01K4FZSP5Z/p1705323116084769.
        if (data.publishedAndNC.publishedAndNCAds.publishedAndNCDetails.size.let { it < PAGE_SIZE }) {
            return null to null
        }
        val normalAdsScrollId =
            data.publishedAndNC.publishedAndNCAds.publishedAndNCMetadata?.nextScrollId
        val premiumAdsScrollId =
            data.premium.premiumAds.premiumMetadata?.nextScrollId

        return normalAdsScrollId to premiumAdsScrollId
    }

    private fun getDFPCustomTarget(filters: List<OrionBaseComponentValue>): Map<String, String> {
        val params = mutableMapOf<String, String>()
        val orionCategoryValue = filters.firstOrNull {
            it.id == Constants.KEY_CATEGORY
        } as? OrionSingleSelectCategoryDropdownValue
        if (orionCategoryValue != null) {
            params[KEY_CATEGORY_ID] = orionCategoryValue.category.id
        }
        val city = filters.firstOrNull {
            it.id == Constants.KEY_CITY
        } as? OrionMultipleSelectSmartDropDownValue
        if (city != null) {
            params[KEY_CITY_ID] = city.parents.firstOrNull()?.id ?: ""
            params[KEY_CITY] = city.parents.firstOrNull()?.trackingName ?: ""
        }
        val price = filters.firstOrNull { it.id == Constants.KEY_PRICE } as? OrionKeyRangeValue
        if (price != null) {
            params[KEY_MIN_PRICE] = price.min.toString()
            params[KEY_MAX_PRICE] = price.max.toString()
        }
        val text = filters.firstOrNull { it.id == Constants.KEY_KEYWORD } as? OrionKeyStringValue
        if (text != null) {
            params[KEY_KEYWORD] = text.value
        }
        return params
    }

    private fun addExtendedSearchItemIfExist(
        ads: List<ListingAd>,
        pageNumber: Int,
        data: GetListingAdsQuery.Data,
    ): List<ListingAd> {
        if (pageNumber == START_PAGE) {
            val extendedSearch =
                data.publishedAndNC.publishedAndNCAds.searchExtension?.toExtendedSearchOrNull()
            extendedSearch?.let {
                return ads.toMutableList().apply { add(0, it) }.toList()
            }
        }
        return ads
    }

    // Log each loaded page as a screen view.
    private fun trackPageAsScreenViewEvent(resultCount: Int) {
        val properties = mutableSetOf(
            Param(
                key = AnalyticsEvent.ParamKeys.LANG,
                value = LocaleManager.getCurrentLanguage()
            ),
            Param(
                key = AnalyticsEvent.ParamKeys.SCREEN_NAME,
                value = AnalyticsEvent.ParamValues.SEARCH
            )
        )
        trackListingEvent(
            eventName = AnalyticsEvent.Types.SCREEN_VIEW,
            startProperties = properties,
            resultCount = resultCount
        )
    }

    private fun trackSearchResultEvent(resultCount: Int) {
        trackListingEvent(
            eventName = AnalyticsEvent.Types.SEARCH_RESULT,
            startProperties = emptySet(),
            resultCount = resultCount
        )
    }

    private fun trackListingEvent(
        eventName: String,
        startProperties: Set<Param>,
        resultCount: Int
    ) {
        val properties = mutableSetOf<Param>().apply {
            addAll(startProperties)
            add(
                Param(
                    key = AnalyticsEvent.ParamKeys.RESULT_COUNT,
                    value = resultCount.toString()
                )
            )
        }
        val keyword = filters.firstOrNull { it.id == Constants.KEY_KEYWORD }
            ?.let { it as? OrionKeyStringValue }
            ?.value
        properties.add(Param(AnalyticsEvent.ParamKeys.KEYWORD, keyword.orEmpty()))
        val category = filters.filterIsInstance<OrionSingleSelectCategoryDropdownValue>()
            .firstOrNull()
        if (category != null) {
            properties.addAll(category.category.getAnalyticsParams())
            properties.add(
                Param(
                    key = AnalyticsEvent.ParamKeys.AD_TYPE,
                    value = category.adTypeKey?.name.orEmpty()
                )
            )
        }
        val dropDownValues = filters.filterIsInstance<OrionMultipleSelectSmartDropDownValue>()
        properties.addAll(
            extractMultipleSelectProperties(
                filters = dropDownValues,
                valueKey = Constants.KEY_CITY,
                firstParentKey = AnalyticsEvent.ParamKeys.CITY_ID,
                firstParentTrackingNameKey = AnalyticsEvent.ParamKeys.CITY,
                nbrParentKey = AnalyticsEvent.ParamKeys.NBR_CITIES,
                firstChildKey = AnalyticsEvent.ParamKeys.AREA_ID,
                firstChildTrackingNameKey = AnalyticsEvent.ParamKeys.AREA,
                nbrChildKey = AnalyticsEvent.ParamKeys.NBR_AREAS
            )
        )
        properties.addAll(
            extractMultipleSelectProperties(
                filters = dropDownValues,
                valueKey = Constants.KEY_BRAND,
                firstParentKey = AnalyticsEvent.ParamKeys.BRAND_ID,
                firstParentTrackingNameKey = AnalyticsEvent.ParamKeys.BRAND,
                nbrParentKey = AnalyticsEvent.ParamKeys.NBR_BRANDS,
                firstChildKey = AnalyticsEvent.ParamKeys.MODEL_ID,
                firstChildTrackingNameKey = AnalyticsEvent.ParamKeys.MODEL,
                nbrChildKey = AnalyticsEvent.ParamKeys.NBR_MODELS
            )
        )
        filters.firstOrNull { it.id == Constants.KEY_SELLER_TYPE }
            ?.let { it as? OrionKeyStringValue }
            ?.let { type ->
                val sellerType = when (type.value) {
                    Constants.VALUE_SELLER_TYPE_STORE -> AnalyticsEvent.ParamValues.SELLER_TYPE_PRO
                    Constants.VALUE_SELLER_TYPE_PRIVATE -> AnalyticsEvent.ParamValues.SELLER_TYPE_PRIVATE
                    else -> AnalyticsEvent.ParamValues.SELLER_TYPE_ALL
                }
                Param(AnalyticsEvent.ParamKeys.SELLER_TYPE, sellerType)
            }
        analyticsHelper.logEvent(event = AnalyticsEvent(name = eventName, properties = properties))
    }

    private fun extractMultipleSelectProperties(
        filters: List<OrionMultipleSelectSmartDropDownValue>,
        valueKey: String,
        firstParentKey: String,
        firstParentTrackingNameKey: String,
        nbrParentKey: String,
        firstChildKey: String,
        firstChildTrackingNameKey: String,
        nbrChildKey: String
    ): Set<Param> {
        val properties = mutableSetOf<Param>()
        val value = filters.firstOrNull { it.id == valueKey }
        if (value != null) {
            val firstParent = value.parents.firstOrNull()
            if (firstParent != null) {
                properties.addAll(
                    setOf(
                        Param(key = firstParentKey, value = firstParent.id),
                        Param(key = firstParentTrackingNameKey, value = firstParent.trackingName)
                    )
                )
            }
            properties.add(
                Param(
                    key = nbrParentKey,
                    value = value.parents.size.toString()
                )
            )

            val firstChild = value.children.firstOrNull()
            if (firstChild != null) {
                properties.addAll(
                    setOf(
                        Param(key = firstChildKey, value = firstChild.id),
                        Param(key = firstChildTrackingNameKey, value = firstChild.trackingName)
                    )
                )
            }
            properties.add(
                Param(
                    key = nbrChildKey,
                    value = value.children.size.toString()
                )
            )
        }
        return properties
    }
}
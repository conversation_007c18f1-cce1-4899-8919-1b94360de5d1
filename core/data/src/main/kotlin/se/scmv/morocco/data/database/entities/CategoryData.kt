package se.scmv.morocco.data.database.entities

import androidx.annotation.Keep
import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverter
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken

@Keep
@Entity
data class AdTypes(
    @PrimaryKey
    @SerializedName("key") var key: String,
    @SerializedName("name") var name: String,
    @SerializedName("trackingName") var trackingName: String
)


@Keep
@Entity
data class CategoryData(
    @SerializedName("children") var children: ArrayList<CategoryData> = arrayListOf(),
    @SerializedName("icon") var icon: String? = null,
    @SerializedName("index") var index: Int,
    @PrimaryKey
    @SerializedName("category") var category: CategoryTree = CategoryTree("0"),
    @SerializedName("name") var name: String? = null,
    @SerializedName("trackingName") var trackingName: String? = null,
    @SerializedName("adTypes") var adTypes: List<AdTypes>? = null
)

@Entity
@Keep
data class CategoryTree(
    @SerializedName("id") var id: String,
)

class Converters {

    @TypeConverter
    fun fromChildrenList(value: ArrayList<CategoryData>): String {
        val gson = Gson()
        val type = object : TypeToken<ArrayList<CategoryData>>() {}.type
        return gson.toJson(value, type)
    }

    @TypeConverter
    fun toChildrenList(value: String): ArrayList<CategoryData> {
        val gson = Gson()
        val type = object : TypeToken<ArrayList<CategoryData>>() {}.type
        return gson.fromJson(value, type)
    }

    @TypeConverter
    fun fromCategoryTree(value: CategoryTree?): String? {
        val gson = Gson()
        return gson.toJson(value)
    }

    @TypeConverter
    fun toCategoryTree(value: String?): CategoryTree? {
        val gson = Gson()
        return gson.fromJson(value, CategoryTree::class.java)
    }

    @TypeConverter
    fun fromAdTypesList(value: List<AdTypes>?): String? {
        val gson = Gson()
        return gson.toJson(value)
    }

    @TypeConverter
    fun toAdTypesList(value: String?): List<AdTypes>? {
        val gson = Gson()
        val type = object : TypeToken<List<AdTypes>>() {}.type
        return gson.fromJson(value, type)
    }
}
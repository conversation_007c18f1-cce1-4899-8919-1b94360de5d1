package se.scmv.morocco.data.repository.account

import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import com.apollographql.apollo3.ApolloClient
import com.apollographql.apollo3.api.Optional
import kotlinx.coroutines.flow.Flow
import se.scmv.morocco.ActivateAdMutation
import se.scmv.morocco.BulkDeactivateAdsMutation
import se.scmv.morocco.BulkDeleteAdsMutation
import se.scmv.morocco.GetMyAdsCountQuery
import se.scmv.morocco.PatchAdMutation
import se.scmv.morocco.data.mappers.toAccountAdStatus
import se.scmv.morocco.data.mappers.toDeactivationReasonInput
import se.scmv.morocco.data.mappers.toGraphqlFiltersStatus
import se.scmv.morocco.data.repository.account.MyAdsListingPagingSource.Companion.PAGE_SIZE
import se.scmv.morocco.data.repository.utils.wrapWithErrorHandling
import se.scmv.morocco.domain.models.AccountAd
import se.scmv.morocco.domain.models.AccountDeactivationReasonInput
import se.scmv.morocco.domain.models.CountStatus
import se.scmv.morocco.domain.models.MyAccountAdsFilterInput
import se.scmv.morocco.domain.models.NetworkAndBackendErrors
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.repositories.AccountAdsRepository
import se.scmv.morocco.type.PatchAdInput
import javax.inject.Inject

class AccountAdsRepositoryMyImpl @Inject constructor(
    private val apolloClient: ApolloClient,
) : AccountAdsRepository {


    override fun getMyAds(myAccountAdsFilterInput: MyAccountAdsFilterInput): Flow<PagingData<AccountAd>> {
        return Pager(
            config = PagingConfig(pageSize = PAGE_SIZE),
            pagingSourceFactory = {
                MyAdsListingPagingSource(
                    apolloClient,
                    myAccountAdsFilterInput.toGraphqlFiltersStatus()
                )
            }
        ).flow
    }

    override suspend fun getAdsCount(): Resource<List<CountStatus>, NetworkAndBackendErrors> {
        return wrapWithErrorHandling(
            call = {
                apolloClient.query(GetMyAdsCountQuery()).execute()
            },
            toData = { apolloResponse -> apolloResponse.data?.getMyAdsCount },
            toDomainModel = { data ->
                data.countByStatus.map {
                    with(it) { CountStatus(status = status.toAccountAdStatus(), count = count) }
                }
            }
        )
    }

    override suspend fun patchAd(
        adId: String,
        isUrgent: Boolean,
        isHotDeal: Boolean,
        discount: Int
    ): Resource<Unit, NetworkAndBackendErrors> {
        return wrapWithErrorHandling(
            call = {
                val input = PatchAdInput(
                    adId = adId,
                    urgent = Optional.present(isUrgent),
                    hotDeal = Optional.present(isHotDeal),
                    discount = Optional.present(discount)
                )
                apolloClient.mutation(PatchAdMutation(input)).execute()
            },
            toData = { apolloResponse -> apolloResponse.data?.patchAd },
            toDomainModel = {}
        )
    }

    override suspend fun bulkDeactivateAds(
        adIds: List<String>,
        reason: AccountDeactivationReasonInput
    ): Resource<Unit, NetworkAndBackendErrors> {
        return wrapWithErrorHandling(
            call = {
                apolloClient.mutation(
                    BulkDeactivateAdsMutation(adIds, reason.toDeactivationReasonInput())
                ).execute()
            },
            toData = { it.data?.bulkDeactivateAds },
            toDomainModel = {}
        )
    }

    override suspend fun bulkDeleteAds(
        adIds: List<String>,
        reason: AccountDeactivationReasonInput
    ): Resource<Unit, NetworkAndBackendErrors> {
        return wrapWithErrorHandling(
            call = {
                apolloClient.mutation(
                    BulkDeleteAdsMutation(adIds, reason.toDeactivationReasonInput())
                ).execute()
            },
            toData = { it.data?.bulkDeleteAds },
            toDomainModel = {}
        )
    }

    override suspend fun activateAd(adId: String): Resource<Unit, NetworkAndBackendErrors> {
        return wrapWithErrorHandling(
            call = {
                apolloClient.mutation(ActivateAdMutation(adId)).execute()
            },
            toData = { it.data?.ActivateAd },
            toDomainModel = {}
        )
    }
}

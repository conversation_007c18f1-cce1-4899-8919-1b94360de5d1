package se.scmv.morocco.data.di

import androidx.datastore.core.DataStore
import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import se.scmv.morocco.data.R
import se.scmv.morocco.data.session.SessionManager
import se.scmv.morocco.datastore.PbAccount
import se.scmv.morocco.datastore.prefs.AvitoPreferencesDataSource
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object FactoryModule {

    @Provides
    @Singleton
    fun provideSessionManager(
        prefsDataStore: AvitoPreferencesDataSource,
        accountDataStore: DataStore<PbAccount>
    ): SessionManager {
        return SessionManager(prefsDataSource = prefsDataStore, accountDataStore = accountDataStore)
    }

    @Provides
    @Singleton
    fun provideFirebaseRemoteConfig(): FirebaseRemoteConfig {
        val remoteConfig = FirebaseRemoteConfig.getInstance()
        remoteConfig.setDefaultsAsync(R.xml.remote_config_defaults)
        return remoteConfig
    }
}
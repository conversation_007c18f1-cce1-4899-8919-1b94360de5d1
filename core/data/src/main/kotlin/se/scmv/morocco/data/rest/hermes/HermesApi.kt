package se.scmv.morocco.data.rest.hermes

import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Query

/**
 * This is the API for a server in Avito's office.
 */
interface HermesApi {

    @GET("hermes_net_v5/ondata/ondataconnect.asmx/SetCallback")
    suspend fun registerShopAccount(
        @Query("Did") activity: Int,
        @Query("Comments") membership: String?,
        @Query("Elapsed_Time") elapsedTime: Int,
        @Query("Tel") phoneNumber: String?,
        @Query("Email") email: String?
    ): Response<Unit>
}
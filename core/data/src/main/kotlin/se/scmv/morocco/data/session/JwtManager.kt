package se.scmv.morocco.data.session

import android.util.Base64
import android.util.Log
import org.json.JSONException
import org.json.JSONObject
import se.scmv.morocco.domain.models.AllowedAccess

object JwtManager {

    /**
     * Maps a JWT token to a [Session] instance.
     *
     * This function decodes the specified part of the JWT token into a JSON object and maps it to a [Session].
     * If the JSON object cannot be decoded or mapped, it returns [Session.NotLogged].
     *
     * @param token The JWT token containing session information.
     * @param index The index of the JWT segment to decode and parse as JSON. Typically, 1 is used for the payload.
     * @return A [Session] instance, which could be [Session.Shop], [Session.Private], or [Session.NotLogged].
     */
    fun mapToSession(token: String, index: Int): Session {
        val jsonObject = decodeJwtPayloadPartAsJson(token, index)
        return jsonObject?.let { mapToSession(jsonObject) } ?: Session.NotLogged
    }

    /**
     * Checks if the JWT token contains ECOM_STORE_OWNER role
     * @param token The JWT token to check
     * @return true if the token contains ECOM_STORE_OWNER role, false otherwise
     */
    fun isEcommerceStoreOwner(token: String): Boolean {
        val jsonObject = decodeJwtPayloadPartAsJson(token, 1)
        jsonObject?.let { json ->
            if (json.has("roles")) {
                val rolesArray = json.getJSONArray("roles")
                for (i in 0 until rolesArray.length()) {
                    val role = rolesArray.getString(i)
                    if (role.contains("ECOM_STORE_OWNER")) {
                        return true
                    }
                }
            }
        }
        return false
    }

    /**
     * Extracts a JSON object from a specified segment of a JWT token.
     *
     * This function splits the given JWT token into its components based on the `.` delimiter,
     * decodes the specified segment using Base64 URL-safe encoding, and converts the resulting string into a JSON object.
     *
     * @param jwtToken The JWT token to extract a segment from.
     * @param index The index of the segment to decode and parse as JSON. Typically, 1 is used to target the payload.
     * @return A [JSONObject] representing the decoded and parsed segment, or `null` if an error occurs during decoding or parsing.
     */
    private fun decodeJwtPayloadPartAsJson(jwtToken: String, index: Int): JSONObject? {
        return try {
            val splitToken = jwtToken.split("\\.".toRegex()).dropLastWhile { it.isEmpty() }
                .toTypedArray()
            val str = String(Base64.decode(splitToken[index], Base64.URL_SAFE))
            JSONObject(str)
        } catch (e: JSONException) {
            e.printStackTrace()
            Log.e(
                "Failed to parse token: ",
                e.message + " : Used token = " + jwtToken + " : Token : " + jwtToken
            )
            null
        } catch (e: IllegalArgumentException) {
            e.printStackTrace()
            Log.e(
                "Failed to parse token: ",
                e.message + " : Used token = " + jwtToken + " : Token : " + jwtToken
            )
            null
        }
    }

    /**
     * Maps a JSON object to a [Session] instance.
     *
     * This function extracts session information and store information from the provided JSON object.
     * If the store information is missing or cannot be parsed, it creates a [Session.Private] instance.
     * If session information cannot be parsed, it returns [Session.NotLogged].
     *
     * @param jsonObject The JSON object containing session and store information.
     * @return A [Session] instance, which could be [Session.Shop], [Session.Private], or [Session.NotLogged].
     */
    private fun mapToSession(jsonObject: JSONObject): Session {
        return try {
            val sessionInfo = extractSessionInfo(jsonObject)
            val sessionStoreInfo = try {
                extractAllowedAccess(jsonObject)
            } catch (e: JSONException) {
                e.printStackTrace()
                null
            }
            if (sessionStoreInfo == null) {
                Session.Private(sessionInfo = sessionInfo)
            } else Session.Shop(sessionInfo = sessionInfo, allowedAccess = sessionStoreInfo)
        } catch (e: JSONException) {
            e.printStackTrace()
            Session.NotLogged
        }
    }

    private fun extractAllowedAccess(jsonObject: JSONObject): AllowedAccess {
        return with(jsonObject.getJSONObject("allowedAccess")) {
            AllowedAccess(
                adHotdealAllowed = getBoolean("adHotdealAllowed"),
                adMaxImages = getInt("adMaxImages"),
                adMaxVideos = getInt("adMaxVideos"),
                adUrgentAllowed = getBoolean("adUrgentAllowed"),
                adsBoostedFilterAllowed = getBoolean("adsBoostedFilterAllowed"),
                adsBulkDeleteAllowed = getBoolean("adsBulkDeleteAllowed"),
                avitoTokenAllowed = getBoolean("avitoTokenAllowed"),
                deliveryAllowed = getBoolean("deliveryAllowed"),
                statsPerAdAllowed = getBoolean("statsPerAdAllowed"),
                supportViaWhatsappAllowed = getBoolean("supportViaWhatsappAllowed"),
                vasConfigureExecTimeAllowed = getBoolean("vasConfigureExecTimeAllowed")
            )
        }
    }

    private fun extractSessionInfo(jsonObject: JSONObject) = SessionInfo(
        sessionId = jsonObject.getString("sessionId"),
        accountId = jsonObject.getLong("sub"),
        expiresAt = jsonObject.getLong("exp"),
        name = jsonObject.getString("name"),
        email = jsonObject.getString("email"),
    )
}
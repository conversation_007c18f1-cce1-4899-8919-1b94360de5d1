package se.scmv.morocco.data.mappers

import kotlinx.datetime.LocalDate
import kotlinx.datetime.format
import kotlinx.datetime.format.char
import se.scmv.morocco.data.repository.utils.buildCategoryIconUrl
import se.scmv.morocco.datastore.PbAccount
import se.scmv.morocco.datastore.PbAccountInfo
import se.scmv.morocco.datastore.PbAllowedAccess
import se.scmv.morocco.datastore.PbCategory
import se.scmv.morocco.datastore.PbCity
import se.scmv.morocco.datastore.PbConnectedAccount
import se.scmv.morocco.datastore.PbNotConnectedAccount
import se.scmv.morocco.datastore.PbPrivateAccount
import se.scmv.morocco.datastore.PbShopAccount
import se.scmv.morocco.datastore.PbStoreInfo
import se.scmv.morocco.domain.models.Account
import se.scmv.morocco.domain.models.AccountInfo
import se.scmv.morocco.domain.models.AllowedAccess
import se.scmv.morocco.domain.models.Category
import se.scmv.morocco.domain.models.City
import se.scmv.morocco.domain.models.StoreInfo

fun Account.toPbAccount(): PbAccount {
    return when (this) {
        Account.NotConnected -> PbAccount(connected = null, notConnected = PbNotConnectedAccount())
        is Account.Connected.Private -> PbAccount(
            notConnected = null,
            connected = PbConnectedAccount(
                privateAccount = PbPrivateAccount(
                    contact = contact.toPbAccountInfo(),
                    isPhoneHidden = isPhoneHidden
                ),
                shopAccount = null
            )
        )

        is Account.Connected.Shop -> PbAccount(
            notConnected = null,
            connected = PbConnectedAccount(
                privateAccount = null,
                shopAccount = PbShopAccount(
                    contact = contact.toPbAccountInfo(),
                    store = store.toPbStoreInfo()
                ),
            )
        )
    }
}

fun PbAccount.toAccount(): Account {
    return if (connected == null) {
        Account.NotConnected
    } else {
        val shopContact = connected?.shopAccount?.contact
        val shopStore = connected?.shopAccount?.store
        val privateContact = connected?.privateAccount?.contact
        when {
            shopContact != null && shopStore != null -> Account.Connected.Shop(
                contact = shopContact.toAccountInfo(),
                store = shopStore.toStoreInfo()
            )

            privateContact != null -> Account.Connected.Private(
                contact = privateContact.toAccountInfo(),
                isPhoneHidden = connected?.privateAccount?.isPhoneHidden ?: false
            )

            else -> Account.NotConnected
        }
    }
}

private fun StoreInfo.toPbStoreInfo(): PbStoreInfo = PbStoreInfo(
    points = points,
    pointsExpirationDate = pointsExpirationDate?.format(
        LocalDate.Format {
            year()
            char('-')
            monthNumber()
            char('-')
            dayOfMonth()
        }
    ),
    membership = membership,
    category = with(category) {
        PbCategory(id = id, name = name, trackingName = trackingName)
    },
    website = website,
    verified = verified,
    shortDescription = shortDescription,
    longDescription = longDescription,
    logoUrl = logoUrl,
    cities = cities.map { it.toPbCity() },
    phones = phones,
    startDate = startDate?.format(
        LocalDate.Format {
            year()
            char('-')
            monthNumber()
            char('-')
            dayOfMonth()
        }
    ),
    expirationDate = expirationDate?.format(
        LocalDate.Format {
            year()
            char('-')
            monthNumber()
            char('-')
            dayOfMonth()
        }
    ),
    allowedAccess = allowedAccess.toPbAllowedAccess()
)

private fun PbStoreInfo.toStoreInfo(): StoreInfo = StoreInfo(
    points = points,
    pointsExpirationDate = pointsExpirationDate?.let { LocalDate.parse(it) },
    membership = membership,
    category = with(category) {
        Category(
            id = id,
            name = name,
            icon = buildCategoryIconUrl(id),
            trackingName = trackingName
        )
    },
    website = website,
    verified = verified,
    shortDescription = shortDescription,
    longDescription = longDescription,
    logoUrl = logoUrl,
    cities = cities.map { it.toCity() },
    phones = phones,
    expirationDate = expirationDate?.let { LocalDate.parse(it) },
    startDate = startDate?.let { LocalDate.parse(it) },
    allowedAccess = allowedAccess.toAllowedAccess(),
)

private fun PbAccountInfo.toAccountInfo(): AccountInfo = AccountInfo(
    accountId = accountId,
    name = name,
    email = email,
    phone = phone,
    location = location?.toCity(),
    creationDate = creationDate
)

private fun AccountInfo.toPbAccountInfo(): PbAccountInfo = PbAccountInfo(
    accountId = accountId,
    name = name,
    email = email,
    phone = phone,
    location = location?.toPbCity(),
    creationDate = creationDate
)

private fun PbAllowedAccess.toAllowedAccess(): AllowedAccess = AllowedAccess(
    adHotdealAllowed = adHotdealAllowed,
    adMaxImages = adMaxImages,
    adMaxVideos = adMaxVideos,
    adUrgentAllowed = adUrgentAllowed,
    adsBoostedFilterAllowed = adsBoostedFilterAllowed,
    adsBulkDeleteAllowed = adsBulkDeleteAllowed,
    avitoTokenAllowed = avitoTokenAllowed,
    deliveryAllowed = deliveryAllowed,
    statsPerAdAllowed = statsPerAdAllowed,
    supportViaWhatsappAllowed = supportViaWhatsappAllowed,
    vasConfigureExecTimeAllowed = vasConfigureExecTimeAllowed,
)

private fun AllowedAccess.toPbAllowedAccess(): PbAllowedAccess = PbAllowedAccess(
    adHotdealAllowed = adHotdealAllowed,
    adMaxImages = adMaxImages,
    adMaxVideos = adMaxVideos,
    adUrgentAllowed = adUrgentAllowed,
    adsBoostedFilterAllowed = adsBoostedFilterAllowed,
    adsBulkDeleteAllowed = adsBulkDeleteAllowed,
    avitoTokenAllowed = avitoTokenAllowed,
    deliveryAllowed = deliveryAllowed,
    statsPerAdAllowed = statsPerAdAllowed,
    supportViaWhatsappAllowed = supportViaWhatsappAllowed,
    vasConfigureExecTimeAllowed = vasConfigureExecTimeAllowed,
)

fun PbCity.toCity() = City(
    id = id,
    name = name,
    trackingName = trackingName,
    address = address
)

fun City.toPbCity(): PbCity = PbCity(
    id = id,
    name = name,
    trackingName = trackingName,
    address = address
)
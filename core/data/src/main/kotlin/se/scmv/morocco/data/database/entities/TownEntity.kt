package se.scmv.morocco.data.database.entities

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "towns")
data class TownEntity(
    @PrimaryKey val id: String,
    @ColumnInfo(name = "name_ar") val nameAr: String,
    @ColumnInfo(name = "name_Fr") val nameFr: String,
    @ColumnInfo(name = "tracking_name") val trackingName: String,
    @ColumnInfo(name = "city_id") val cityId: String
)
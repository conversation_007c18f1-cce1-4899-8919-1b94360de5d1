package se.scmv.morocco.data.repository

import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import se.scmv.morocco.domain.repositories.RemoteConfigRepository
import javax.inject.Inject

class RemoteConfigRepositoryFirebaseImpl @Inject constructor(
    private val firebaseRemoteConfig: FirebaseRemoteConfig,
) : RemoteConfigRepository {

    init {
        firebaseRemoteConfig.fetchAndActivate()
    }

    override fun getString(key: String): String {
        return firebaseRemoteConfig.getString(key)
    }

    override fun getBoolean(key: String): Bo<PERSON>an {
        return firebaseRemoteConfig.getBoolean(key)
    }

    override fun getLong(key: String): Long {
        return firebaseRemoteConfig.getLong(key)
    }
}
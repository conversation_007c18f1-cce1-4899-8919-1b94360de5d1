package se.scmv.morocco.data.session

import androidx.datastore.core.DataStore
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.mapLatest
import se.scmv.morocco.data.mappers.toAccount
import se.scmv.morocco.data.mappers.toPbAccount
import se.scmv.morocco.datastore.PbAccount
import se.scmv.morocco.datastore.prefs.AvitoPreferencesDataSource
import se.scmv.morocco.domain.models.Account
import javax.inject.Inject

// TODO Think about migrating the existing users to this mechanism!
class SessionManager @Inject constructor(
    private val prefsDataSource: AvitoPreferencesDataSource,
    private val accountDataStore: DataStore<PbAccount>
) {
    companion object {
        const val KEY_ACCESS_TOKEN = "key_access_token"
        const val KEY_REFRESH_TOKEN = "key_refresh_token"
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    val currentAccount: Flow<Account> = accountDataStore.data.mapLatest { it.toAccount() }

    suspend fun createSession(token: Token, account: Account.Connected) {
        prefsDataSource.saveString(KEY_ACCESS_TOKEN, token.accessToken)
        prefsDataSource.saveString(KEY_REFRESH_TOKEN, token.refreshToken)
        accountDataStore.updateData { account.toPbAccount() }
    }

    suspend fun endSession() {
        prefsDataSource.delete(KEY_ACCESS_TOKEN)
        prefsDataSource.delete(KEY_REFRESH_TOKEN)
        accountDataStore.updateData { Account.NotConnected.toPbAccount() }
    }

    suspend fun setAccessToken(token: String) {
        prefsDataSource.saveString(KEY_ACCESS_TOKEN, token)
    }

    suspend fun getAccessToken(): String? = prefsDataSource.getString(KEY_ACCESS_TOKEN)

    suspend fun getRefreshToken(): String? = prefsDataSource.getString(KEY_REFRESH_TOKEN)

    suspend fun isLogged(): Boolean {
        val account = accountDataStore.data.firstOrNull()
        return account?.connected != null
    }
}
package se.scmv.morocco.data.session

import se.scmv.morocco.domain.models.AllowedAccess

sealed interface Session {
    data object NotLogged : Session
    data class Private(val sessionInfo: SessionInfo) : Session
    data class Shop(val sessionInfo: SessionInfo, val allowedAccess: AllowedAccess) : Session

    fun isLogged() = this != NotLogged
}

data class SessionInfo(
    val sessionId: String,
    val accountId: Long,
    val expiresAt: Long,
    val name: String,
    val email: String,
)


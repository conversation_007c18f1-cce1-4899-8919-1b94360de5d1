package se.scmv.morocco.data.repository

import com.apollographql.apollo3.ApolloClient
import com.apollographql.apollo3.api.Optional
import se.scmv.morocco.GetChatConversationWithAdQuery
import se.scmv.morocco.GetEcommerceDetailsQuery
import se.scmv.morocco.GetPublishedAdQuery
import se.scmv.morocco.SendFirstChatMessageMutation
import se.scmv.morocco.data.mappers.toAdDetails
import se.scmv.morocco.data.mappers.toSimilarDetails
import se.scmv.morocco.data.repository.utils.wrapWithErrorHandling
import se.scmv.morocco.data.rest.ad.AdReportApi
import se.scmv.morocco.data.rest.ad.dtos.AdReportDto
import se.scmv.morocco.data.rest.car.CarCheckApi
import se.scmv.morocco.data.rest.car.dtos.LeadDto
import se.scmv.morocco.data.rest.car.dtos.LeadRequestDto
import se.scmv.morocco.data.rest.car.dtos.TagsDto
import se.scmv.morocco.data.rest.tp.TouchingPointApi
import se.scmv.morocco.data.rest.tp.dtos.toAdTouchingPoint
import se.scmv.morocco.domain.models.AdDetails
import se.scmv.morocco.domain.models.AdTouchingPoint
import se.scmv.morocco.domain.models.CachedAdDetails
import se.scmv.morocco.domain.models.ChatConversation
import se.scmv.morocco.domain.models.ChatConversationResult
import se.scmv.morocco.domain.models.ChatMessage
import se.scmv.morocco.domain.models.FirstChatMessageResult
import se.scmv.morocco.domain.models.MessageAttachment
import se.scmv.morocco.domain.models.NetworkAndBackendErrors
import se.scmv.morocco.domain.models.NetworkErrors
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.repositories.AdViewRepository
import se.scmv.morocco.type.SendChatMessageInput
import java.io.IOException
import javax.inject.Inject

class AdViewRepositoryImpl @Inject constructor(
    private val apolloClient: ApolloClient,
    private val touchingPointApi: TouchingPointApi,
    private val reportAdApi: AdReportApi,
    private val carCheckApi: CarCheckApi
) : AdViewRepository {

    private val cachedAdDetailsMap = mutableMapOf<String, CachedAdDetails>()


    override suspend fun getAdDetails(listId: String): Resource<AdDetails, NetworkAndBackendErrors> {
        return wrapWithErrorHandling(
            call = {
                apolloClient.query(GetPublishedAdQuery(listId)).execute()
            },
            toData = { apolloResponse -> apolloResponse.data },
            toDomainModel = { data ->
                val adResponse: GetPublishedAdQuery.Ad? = data.getPublishedAd?.ad

                val similarAdsResponse: List<GetPublishedAdQuery.SimilarAd?> =
                    data.getPublishedAd?.similarAds ?: emptyList()

                val adDetails = AdDetails(
                    ad = adResponse!!.toAdDetails(),
                    similarAds = similarAdsResponse.map { it!!.toSimilarDetails() }
                )

                // If the ad is ecommerce, fetch the stock info
                if (adResponse.isEcommerce) {
                    val ecommerceStock = fetchEcommerceStock(adResponse.adId)
                    adDetails.ad.stock = ecommerceStock
                }
                adDetails

            }
        )
    }

    private suspend fun fetchEcommerceStock(adId: String): Int? {
        return try {
            val ecommerceResponse = apolloClient.query(GetEcommerceDetailsQuery(adId)).execute()
            return ecommerceResponse.data?.getECommerceProductInfo?.itemsLeft
        } catch (e: Exception) {
            e.printStackTrace()
            null // Return null if the stock information cannot be fetched
        }
    }


    override suspend fun getConversationIdWithAd(adId: String): Resource<ChatConversationResult, NetworkAndBackendErrors> {
        return wrapWithErrorHandling(
            call = { apolloClient.query(GetChatConversationWithAdQuery(adId)).execute() },
            toData = { response ->
                response.data?.getMyChatConversationWithAd
            },
            toDomainModel = {
                ChatConversationResult(
                    found = it.found,
                    conversationId = it.conversationId
                )
            }
        )
    }

    override suspend fun sendFirstMessageToSeller(
        adId: String,
        text: String
    ): Resource<FirstChatMessageResult, NetworkAndBackendErrors> {
        val input = SendChatMessageInput(
            adId = Optional.present(adId),
            text = Optional.present(text)
        )

        return wrapWithErrorHandling(
            call = { apolloClient.mutation(SendFirstChatMessageMutation(input)).execute() },
            toData = { response -> response.data?.sendChatMessage },
            toDomainModel = { sendChatMessage ->
                FirstChatMessageResult(
                    success = sendChatMessage.success,
                    conversation = sendChatMessage.conversation?.let { gqlConversation ->
                        val gqlMessage = gqlConversation.message
                        val gqlAttachment = gqlMessage?.attachment

                        ChatConversation(
                            id = gqlConversation.id,
                            isNew = gqlConversation.isNew,
                            message = gqlMessage?.let {
                                ChatMessage(
                                    id = it.id,
                                    text = it.text,
                                    isMine = it.isMine,
                                    time = it.time,
                                    attachment = gqlAttachment?.let { att ->
                                        MessageAttachment(
                                            url = att.url,
                                            type = att.type
                                        )
                                    }
                                )
                            }
                        )
                    }
                )
            }
        )
    }


    override suspend fun getAdTouchingPoint(): Resource<List<AdTouchingPoint>, NetworkAndBackendErrors> {
        return try {
            val response = touchingPointApi.fetchTouchingPoints()

            if (response.isSuccessful) {
                response.body()?.let {
                    Resource.Success(it.ads.map { ad -> ad.toAdTouchingPoint() })
                } ?: Resource.Failure(NetworkAndBackendErrors.Network(NetworkErrors.UNKNOWN))
            } else {
                val errorMessage = response.errorBody()?.string().orEmpty()
                Resource.Failure(NetworkAndBackendErrors.Backend(errorMessage))
            }
        } catch (e: IOException) {
            e.printStackTrace()
            Resource.Failure(NetworkAndBackendErrors.Network(NetworkErrors.NO_INTERNET))
        } catch (e: Exception) {
            e.printStackTrace()
            Resource.Failure(NetworkAndBackendErrors.Backend(e.message.orEmpty()))
        }
    }

    override suspend fun sendCarInspectionLead(
        resellerId: String,
        campaignId: String,
        name: String,
        phone: String,
        option1: String,
        option2: Boolean
    ): Resource<Unit, NetworkAndBackendErrors> {
        return try {
            val lead = LeadRequestDto(
                leads = listOf(
                    LeadDto(
                        platform = "avito",
                        campaign = campaignId,
                        tags = TagsDto(
                            name = name,
                            phone = phone,
                            option1 = option1,
                            option2 = option2,
                            option3 = "android"
                        )
                    )
                )
            )
            val response = carCheckApi.sendLead(reseller = resellerId, leadRequest = lead)
            if (response.isSuccessful) {
                Resource.Success(Unit)
            } else {
                val errorMessage = response.errorBody()?.string().orEmpty()
                Resource.Failure(NetworkAndBackendErrors.Backend(errorMessage))
            }
        } catch (e: IOException) {
            e.printStackTrace()
            Resource.Failure(NetworkAndBackendErrors.Network(NetworkErrors.NO_INTERNET))
        } catch (e: Exception) {
            e.printStackTrace()
            Resource.Failure(NetworkAndBackendErrors.Backend(e.message.orEmpty()))
        }
    }


    override suspend fun getCachedAdDetails(adId: String): CachedAdDetails? {
        return cachedAdDetailsMap[adId]
    }

    override suspend fun saveCachedAdDetails(adId: String, cachedAdDetails: CachedAdDetails) {
        cachedAdDetailsMap[adId] = cachedAdDetails
    }


    override suspend fun recordTouchingPoint(
        campaignId: String,
        clickOrImpression: String
    ): Resource<Boolean, NetworkAndBackendErrors> {
        return try {
            val response = touchingPointApi.recordTouchingPoint(campaignId, clickOrImpression)
            Resource.Success(response.isSuccessful)
        } catch (e: Exception) {
            e.printStackTrace()
            Resource.Failure(NetworkAndBackendErrors.Backend(e.message.orEmpty()))
        }
    }


    override suspend fun reportAd(
        adListId: String,
        email: String,
        reason: String,
        message: String
    ): Resource<Unit, NetworkAndBackendErrors> {
        return try {
            val response = reportAdApi.reportAd(
                AdReportDto(
                    listId = adListId.toLong(),
                    from = email,
                    type = reason,
                    body = message
                )
            )
            if (response.isSuccessful && (response.code() == 200 || response.code() == 204)) {
                Resource.Success(Unit)
            } else {
                val message = response.message().orEmpty()
                Resource.Failure(NetworkAndBackendErrors.Backend(message))
            }
        } catch (e: IOException) {
            e.printStackTrace()
            Resource.Failure(NetworkAndBackendErrors.Network(NetworkErrors.NO_INTERNET))
        } catch (e: Exception) {
            e.printStackTrace()
            Resource.Failure(NetworkAndBackendErrors.Backend(e.message.orEmpty()))
        }
    }

}
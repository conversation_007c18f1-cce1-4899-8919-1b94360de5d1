package se.scmv.morocco.data.repository.account

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.apollographql.apollo3.ApolloClient
import com.apollographql.apollo3.api.Optional
import se.scmv.morocco.GetActiveAndPendingReviewAdsQuery
import se.scmv.morocco.GetMyAdsQuery
import se.scmv.morocco.data.mappers.toAccountAd
import se.scmv.morocco.data.repository.utils.getNextPageNumber
import se.scmv.morocco.data.repository.utils.wrapWithErrorHandling
import se.scmv.morocco.domain.models.AccountAd
import se.scmv.morocco.type.AdStatus
import se.scmv.morocco.type.MyAdsFilterInput
import se.scmv.morocco.type.Page

class MyAdsListingPagingSource(
    private val apolloClient: ApolloClient,
    private val myAdsFilterInput: MyAdsFilterInput
) : PagingSource<Int, AccountAd>() {

    companion object {
        const val START_PAGE = 1
        const val PAGE_SIZE = 35
    }

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, AccountAd> {
        val pageNumber = params.key ?: START_PAGE

        // Wrapper for both queries
        return try {
            if (myAdsFilterInput.status == AdStatus.ACTIVE && myAdsFilterInput.boosted == Optional.Absent) {
                // Use the new combined query for ACTIVE status
                wrapWithErrorHandling(
                    call = {
                        val activeFilter = myAdsFilterInput.copy(status = AdStatus.ACTIVE)
                        val pendingReviewFilter =
                            myAdsFilterInput.copy(status = AdStatus.PENDING_REVIEW)
                        val page = Page(number = pageNumber, size = params.loadSize)

                        apolloClient.query(
                            GetActiveAndPendingReviewAdsQuery(
                                activeFilter = activeFilter,
                                pendingReviewFilter = pendingReviewFilter,
                                page = page
                            )
                        ).execute()
                    },
                    toData = { response -> response.data },
                    toPage = { data ->
                        val activeAds =
                            data?.activeAds?.ads?.map { it: GetActiveAndPendingReviewAdsQuery.Ad -> it.toAccountAd() }
                                ?: emptyList()
                        val pendingReviewAds =
                            data?.pendingReviewAds?.ads?.map { it: GetActiveAndPendingReviewAdsQuery.Ad1 -> it.toAccountAd() }
                                ?: emptyList()
                        val mergedAds =
                            (activeAds + pendingReviewAds).sortedByDescending { it.lastStateTime }

                        LoadResult.Page(
                            data = mergedAds,
                            prevKey = if (pageNumber > AccountOrdersPagingSource.START_PAGE) pageNumber - 1 else null,
                            nextKey = getNextPageNumber(
                                current = pageNumber,
                                pageSize = PAGE_SIZE,
                                total = data.activeAds.count.total + data.pendingReviewAds.count.total
                            )
                        )
                    }
                )
            } else {
                // Fallback to original logic for other statuses
                wrapWithErrorHandling(
                    call = {
                        val filter = myAdsFilterInput
                        apolloClient.query(
                            GetMyAdsQuery(
                                filter = filter,
                                page = Page(
                                    number = pageNumber,
                                    size = params.loadSize
                                )
                            )
                        ).execute()
                    },
                    toData = { response -> response.data?.getMyAds },
                    toPage = { data ->
                        LoadResult.Page(
                            data = data.ads.map { it: GetMyAdsQuery.Ad -> it.toAccountAd() }.sortedByDescending { it.lastStateTime },
                            prevKey = if (pageNumber > AccountOrdersPagingSource.START_PAGE) pageNumber - 1 else null,
                            nextKey = getNextPageNumber(
                                current = pageNumber,
                                pageSize = PAGE_SIZE,
                                total = data.count.total
                            )
                        )
                    }
                )
            }
        } catch (e: Exception) {
            e.printStackTrace()
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, AccountAd>): Int? {
        return state.anchorPosition?.let { anchorPosition ->
            val anchorPage = state.closestPageToPosition(anchorPosition)
            anchorPage?.prevKey?.plus(1) ?: anchorPage?.nextKey?.minus(1)
        }
    }
}


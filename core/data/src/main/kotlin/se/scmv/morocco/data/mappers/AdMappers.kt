package se.scmv.morocco.data.mappers

import kotlinx.datetime.Instant
import kotlinx.datetime.LocalDateTime
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime
import se.scmv.morocco.AutoCompleteSearchQuery
import se.scmv.morocco.GetAccountAdQuery
import se.scmv.morocco.GetAdLimitationsQuery
import se.scmv.morocco.GetListingAdsQuery
import se.scmv.morocco.GetPublishedAdQuery
import se.scmv.morocco.data.repository.utils.buildCategoryIconUrl
import se.scmv.morocco.domain.models.AdDetails
import se.scmv.morocco.domain.models.AdDetails.Details
import se.scmv.morocco.domain.models.AdDetails.Details.Media
import se.scmv.morocco.domain.models.AdDetails.Details.SimilarAdLocation
import se.scmv.morocco.domain.models.AdLimitations
import se.scmv.morocco.domain.models.AdParam
import se.scmv.morocco.domain.models.AdPrice
import se.scmv.morocco.domain.models.AdToBoost
import se.scmv.morocco.domain.models.AdType
import se.scmv.morocco.domain.models.Area
import se.scmv.morocco.domain.models.Category
import se.scmv.morocco.domain.models.City
import se.scmv.morocco.domain.models.ExtendedSearchType
import se.scmv.morocco.domain.models.ListingAd
import se.scmv.morocco.domain.models.PaymentMethodType
import se.scmv.morocco.domain.models.SearchSuggestion
import se.scmv.morocco.domain.models.VasPack
import se.scmv.morocco.domain.models.VasPackage
import se.scmv.morocco.domain.models.VasPackageExecutionSlots
import se.scmv.morocco.domain.models.VasPackageExecutionSlotsTime
import se.scmv.morocco.domain.models.VasPacks
import se.scmv.morocco.domain.models.VasPacksApplication
import se.scmv.morocco.fragment.PublishedAdFragment
import se.scmv.morocco.fragment.VasPackagesFragment
import se.scmv.morocco.type.AdTypeKey
import se.scmv.morocco.type.Application
import se.scmv.morocco.type.ListingAdsSearchExtensionType
import se.scmv.morocco.type.PaymentMethod.CASH
import se.scmv.morocco.type.PaymentMethod.CREDIT_CARD
import se.scmv.morocco.type.PaymentMethod.FATOURATI
import se.scmv.morocco.type.PaymentMethod.WALLET
import se.scmv.morocco.type.PriceUnit
import se.scmv.morocco.type.StoreType
import java.util.UUID
import se.scmv.morocco.domain.models.AdTypeKey as DomainAdTypeKey

// AD INSERT
fun GetAdLimitationsQuery.CheckUserAdLimitationStatus.toAdLimitations() = AdLimitations(
    userCategoryAdCount = userCategoryAdCount,
    configuredCategoryFreeLimit = configuredCategoryFreeLimit,
    configuredCategoryStoreLimit = configuredCategoryStoreLimit,
    categoryFree = categoryFree?.let {
        Category(
            id = it.id,
            name = it.name,
            icon = buildCategoryIconUrl(it.id),
            trackingName = it.trackingValue
        )
    },
    categoryStore = categoryStore?.let {
        Category(
            id = it.id,
            name = it.name,
            icon = buildCategoryIconUrl(it.id),
            trackingName = it.trackingValue
        )
    }
)

// VAS
private const val KEY_SPECIAL = "special"
private const val KEY_GALLERY = "gallery"

fun VasPackagesFragment.toVasPacks() = VasPacks(
    packs = categories.map { it.toVasCategory() },
    executionSlots = availableExecutionSlots?.toVasPackageExecutionSlots()
)

fun VasPackagesFragment.Category.toVasCategory() = VasPack(
    key = key,
    title = title,
    description = description,
    image = image,
    vasLabels = labels.orEmpty(),
    packages = vasPackages.map { it.toVasPackage() },
    // This should be handled in the backend, but it is what it is.
    // Discussion: https://avito-ny73802.slack.com/archives/C01K4FZSP5Z/p1706115699416789
    showBestSellerBadge = key == KEY_SPECIAL,
    requiresImage = key == KEY_GALLERY
)

fun VasPackagesFragment.VasPackage.toVasPackage() = VasPackage(
    id = id,
    durationDays = durationDays,
    price = price,
    priceUnit = when (priceUnit) {
        PriceUnit.POINTS -> se.scmv.morocco.domain.models.PriceUnit.POINTS
        PriceUnit.AVITO_TOKEN -> se.scmv.morocco.domain.models.PriceUnit.AVITO_TOKEN
        else -> se.scmv.morocco.domain.models.PriceUnit.DHS
    }
)

fun VasPackagesFragment.AvailableExecutionSlots.toVasPackageExecutionSlots() =
    VasPackageExecutionSlots(
        days = days,
        times = hours.map {
            VasPackageExecutionSlotsTime(
                id = it.id,
                startAt = it.start,
                endAt = it.end
            )
        }
    )

fun GetAccountAdQuery.GetMyAd.toAdToBoost() = AdToBoost(
    title = title,
    description = description,
    imageUrl = media?.images?.firstOrNull()?.paths?.standard,
    categoryName = category.name,
    city = with(location) {
        City(
            id = city.id,
            name = city.name,
            trackingName = city.trackingValue,
            area = area?.let {
                Area(
                    id = it.id,
                    name = it.name,
                    trackingName = it.trackingValue
                )
            }
        )
    },
    params = params.toParams()
)

// This should be handled better, but it is what it is.
fun GetAccountAdQuery.Params.toParams(): List<AdParam> {
    val primary = primary?.filterNotNull()?.mapNotNull {
        it.adParam.toAdParam()
    }
    val secondary = secondary?.filterNotNull()?.mapNotNull {
        it.adParam.toAdParam()
    }

    return listOfNotNull(primary, secondary).flatten()
}

fun PaymentMethodType.toGraphQlPaymentMethod() = when (this) {
    PaymentMethodType.CREDIT_CARD -> CREDIT_CARD
    PaymentMethodType.CASH_PLUS,
    PaymentMethodType.BANK_APP -> FATOURATI

    PaymentMethodType.TASSHILATE -> CASH
    PaymentMethodType.AVITOKENS -> WALLET
}

internal fun VasPacksApplication.toGraphQlApplication() = when (this) {
    VasPacksApplication.ADLISTING -> Application.ADLISTING
    VasPacksApplication.ADINSERT -> Application.ADINSERT
    VasPacksApplication.PUSH -> Application.PUSH
}

internal fun DomainAdTypeKey.toGraphQlAdTypeKey(): AdTypeKey? = when (this) {
    DomainAdTypeKey.SELL -> AdTypeKey.SELL
    DomainAdTypeKey.BUY -> AdTypeKey.BUY
    DomainAdTypeKey.SWAP -> AdTypeKey.SWAP
    DomainAdTypeKey.LET -> AdTypeKey.LET
    DomainAdTypeKey.RENT -> AdTypeKey.RENT
    DomainAdTypeKey.CO_RENT -> AdTypeKey.CO_RENT
    DomainAdTypeKey.VAC_RENT -> AdTypeKey.VAC_RENT
    else -> null
}

// ADVIEW
fun GetPublishedAdQuery.Ad.toAdDetails(): Details {
    return Details(
        adId = adId,
        listId = listId,
        listTime = listTime.toLocalDateTimeOrNull(),
        listTimeString = listTime,
        title = title,
        description = description,
        reservedDays = reservedDays,
        category = category.toAdDetailsCategory(),
        type = type.toAdDetailsAdType(),
        discount = discount,
        media = media.toAdDetailsMedia(),
        cityArea = City(
            id = location.city.id,
            name = location.city.name,
            trackingName = location.city.trackingValue,
            area = location.area?.let {
                Area(
                    id = it.id,
                    name = it.name,
                    trackingName = it.trackingValue
                )
            }
        ),
        seller = seller.toAdDetailsSeller(),
        isHighlighted = isHighlighted,
        isInMyFavorites = isInMyFavorites,
        offersShipping = offersShipping,
        isEcommerce = isEcommerce,
        isUrgent = isUrgent,
        isHotDeal = isHotDeal,
        price = publishedAdPrice.toAdPrice(),
        params = publishedAdParam.toAdParams()
    )
}

fun String.toLocalDateTimeOrNull(): LocalDateTime? {
    return try {
        val instant = Instant.parse(this)
        instant.toLocalDateTime(TimeZone.UTC)
    } catch (e: Exception) {
        null
    }
}

fun GetPublishedAdQuery.Category.toAdDetailsCategory(): Category {
    return Category(
        id = id,
        name = name,
        icon = buildCategoryIconUrl(id),
        trackingName = trackingValue,
        parent = parent.toAdDetailsCategory()
    )
}

private fun GetPublishedAdQuery.Parent?.toAdDetailsCategory(): Category? {
    return if (this?.id.isNullOrEmpty())
        null
    else
        Category(
            id = this!!.id,
            name = name,
            icon = buildCategoryIconUrl(id),
            trackingName = trackingValue,
            parent = this.parent?.let {
                Category(
                    it.id,
                    it.name,
                    icon = buildCategoryIconUrl(it.id),
                    trackingName = it.trackingValue,
                )
            }
        )
}

fun GetPublishedAdQuery.Type.toAdDetailsAdType(): AdType? {
    return key?.rawValue?.let { DomainAdTypeKey.valueOf(it.uppercase()) }?.let {
        AdType(
            key = it,
            name = name,
            trackingName = ""
        )
    }
}

fun GetPublishedAdQuery.Media.toAdDetailsMedia(): Media {
    return Media(
        mediaCount = mediaCount,
        defaultImage = defaultImage?.toAdDetailsDefaultImage(),
        media = media?.toAdDetailsMediaItem()
    )
}

fun GetPublishedAdQuery.DefaultImage.toAdDetailsDefaultImage(): Details.DefaultImage {
    return Details.DefaultImage(
        paths = paths.toAdDetailsImagePaths()
    )
}

fun GetPublishedAdQuery.Media1.toAdDetailsMediaItem(): Details.MediaItem {
    return Details.MediaItem(
        images = images.map { it?.toAdDetailsImage() },
        videos = videos.map { it?.toAdDetailsVideo() }
    )
}

fun GetPublishedAdQuery.Image.toAdDetailsImage(): Details.Image {
    return Details.Image(
        paths = paths.toAdDetailsImagePaths()
    )
}

fun GetPublishedAdQuery.Video.toAdDetailsVideo(): Details.Video {
    return Details.Video(
        defaultPath = defaultPath
    )
}

fun GetPublishedAdQuery.Paths.toAdDetailsImagePaths(): Details.ImagePaths {
    return Details.ImagePaths(
        smallThumbnail = smallThumbnail,
        largeThumbnail = largeThumbnail,
        standard = standard,
        fullHd = fullHd
    )
}

fun GetPublishedAdQuery.Paths1.toAdDetailsImagePaths(): Details.ImagePaths {
    return Details.ImagePaths(
        smallThumbnail = smallThumbnail,
        largeThumbnail = largeThumbnail,
        standard = standard,
        fullHd = fullHd
    )
}

fun String.parseDateString(timeZone: TimeZone = TimeZone.UTC): LocalDateTime? {
    return try {
        this.let {
            when {
                it.matches(Regex("""\d{4}-\d{2}-\d{2}$""")) -> {
                    // Format: "2025-03-02" → convert to LocalDateTime at 00:00
                    val (y, m, d) = it.split("-").map(String::toInt)
                    LocalDateTime(year = y, monthNumber = m, dayOfMonth = d, hour = 0, minute = 0)
                }

                it.contains("T") || it.endsWith("Z") -> {
                    // ISO 8601 (with or without Z)
                    Instant.parse(it).toLocalDateTime(timeZone)
                }

                it.matches(Regex("""\d{4}-\d{2}-\d{2} \d{2}:\d{2}""")) -> {
                    // Format: "2025-03-02 14:03"
                    val (datePart, timePart) = it.split(" ")
                    val (y, m, d) = datePart.split("-").map(String::toInt)
                    val (h, min) = timePart.split(":").map(String::toInt)
                    LocalDateTime(year = y, monthNumber = m, dayOfMonth = d, hour = h, minute = min)
                }

                else -> null
            }
        }
    } catch (e: Exception) {
        e.printStackTrace()
        null
    }
}

fun GetPublishedAdQuery.Seller.toAdDetailsSeller(): AdDetails.Seller {
    return when {
        onPrivateProfile != null -> {
            // Map PrivateProfile
            AdDetails.Seller.Private(
                accountId = onPrivateProfile.accountId,
                name = onPrivateProfile.name,
                registrationDay = onPrivateProfile.registrationDay.parseDateString(),
                phone = onPrivateProfile.phone.toAdDetailsPhone()
            )
        }

        onStoreProfile != null -> {
            // Map StoreProfile
            AdDetails.Seller.Store(
                storeId = onStoreProfile.storeId,
                name = onStoreProfile.name,
                registrationDay = onStoreProfile.registrationDay.parseDateString(),
                isVerifiedSeller = onStoreProfile.isVerifiedSeller,
                location = onStoreProfile.location.toAdDetailsStoreLocation(),
                phone = onStoreProfile.phone.toAdDetailsPhone(),
                logo = onStoreProfile.logo?.toAdDetailsLogo(),
                website = onStoreProfile.website,
                isEcommerce = onStoreProfile.type == StoreType.ECOMMERCE,
                type = onStoreProfile.type.rawValue,
                numberOfActiveAds = onStoreProfile.numberOfActiveAds
            )
        }

        else -> throw IllegalArgumentException("Unknown seller type")
    }
}

fun GetPublishedAdQuery.Phone.toAdDetailsPhone(): Details.Phone {
    return Details.Phone(
        number = number,
        verified = verified
    )
}

fun GetPublishedAdQuery.Phone1.toAdDetailsPhone(): Details.Phone {
    return Details.Phone(
        number = number,
        verified = verified
    )
}

fun GetPublishedAdQuery.Location1.toAdDetailsStoreLocation(): Details.StoreLocation {
    return Details.StoreLocation(
        address = address
    )
}

fun GetPublishedAdQuery.Logo.toAdDetailsLogo(): Details.Logo {
    return Details.Logo(
        defaultPath = defaultPath
    )
}

fun GetPublishedAdQuery.SimilarAd.toSimilarDetails(): AdDetails.SimilarAd {
    return AdDetails.SimilarAd(
        adId = adId,
        listId = listId,
        title = title,
        media = media.toMediaSimilar(),
        price = publishedAdPrice.toAdPrice(),
        listTime = listTime.toLocalDateTimeOrNull(),
        category = category.toAdDetailsCategory(),
        location = location.toAdDetailsLocation(),
        isUrgent = isUrgent,
        isHotDeal = isHotDeal,
        discount = discount
    )
}

fun GetPublishedAdQuery.Location2.toAdDetailsLocation(): SimilarAdLocation {
    return SimilarAdLocation(
        city = city.toAdDetailsCity(),
        area = area?.toAdDetailsArea()
    )
}

fun GetPublishedAdQuery.City1.toAdDetailsCity(): City {
    return City(
        id = id,
        name = name,
        trackingName = ""
    )
}

fun GetPublishedAdQuery.Area1.toAdDetailsArea(): Area {
    return Area(
        id = id,
        name = name,
        trackingName = ""
    )
}

fun GetPublishedAdQuery.Category1.toAdDetailsCategory(): Category {
    return Category(
        id = id,
        name = name,
        icon = buildCategoryIconUrl(id),
        trackingName = "",
    )
}

private fun GetPublishedAdQuery.Media2.toMediaSimilar(): Media {
    return Media(
        mediaCount = mediaCount,
        defaultImage = defaultImage?.toAdDetailsDefaultImage()
    )
}

private fun GetPublishedAdQuery.DefaultImage1?.toAdDetailsDefaultImage(): Details.DefaultImage {
    return Details.DefaultImage(
        paths = this?.paths?.toAdDetailsImagePaths()
    )
}

private fun GetPublishedAdQuery.Paths2.toAdDetailsImagePaths(): Details.ImagePaths {
    return Details.ImagePaths(
        smallThumbnail = smallThumbnail,
        standard = standard,
        largeThumbnail = largeThumbnail
    )
}

// LISTING
private const val PUBLISH_AD_TYPE = "PublishedAd"
private const val NEW_CONSTRUCTION_AD_TYPE = "NewConstructionAd"

fun GetListingAdsQuery.PublishedAndNCAds.toPublishedOrNewConstructionAdsOrEmpty() =
    publishedAndNCDetails.filterNotNull().mapNotNull { it.toPublishedOrNewConstructionAd() }

fun GetListingAdsQuery.PremiumAds.toPremiumAdsOrEmpty() = premiumDetails
    .filterNotNull()
    .mapNotNull { it.onPublishedAd?.publishedAdFragment?.toPremiumAd() }

fun PublishedAdFragment.toPremiumAd(): ListingAd.Premium = ListingAd.Premium(
    id = adId,
    listId = listId,
    logo = seller.onStoreProfile?.logo?.defaultPath ?: "",
    sellerName = seller.onStoreProfile?.name ?: seller.onPrivateProfile?.name,
    imageCount = media.mediaCount,
    videoCount = media.media?.videos?.size ?: 0,
    videoUrl = media.media?.videos?.firstOrNull()?.defaultPath,
    description = description,
    defaultImage = media.defaultImage?.paths?.standard,
    title = title,
    date = listTime,
    isStore = seller.onStoreProfile != null,
    price = publishedAdPrice.toAdPrice(),
    offersShipping = offersShipping,
    isVerifiedSeller = seller.onStoreProfile?.isVerifiedSeller
        ?: seller.onStoreProfile?.isVerifiedSeller ?: false,
    isEcommerce = isEcommerce,
    discount = discount,
    location = City(
        id = location.city.id,
        name = location.city.name,
        trackingName = location.city.trackingValue,
        area = location.area?.let {
            Area(
                id = it.id,
                name = it.name,
                trackingName = it.trackingValue
            )
        }
    ),
    category = Category(
        id = category.id,
        name = category.name,
        trackingName = category.trackingValue,
        icon = category.id
    ),
    params = publishedAdParam.toAdParams(),
    adType = type.name,
    isUrgent = isUrgent,
    isHotDeal = isHotDeal,
    phoneNumber = seller.onStoreProfile?.phone?.number ?: seller.onPrivateProfile?.phone?.number
)

fun GetListingAdsQuery.SearchExtension.toExtendedSearchOrNull(): ListingAd.ExtendedSearch? {
    val types = extensionType.mapNotNull {
        when (it) {
            ListingAdsSearchExtensionType.TO_WHOLE_CITY -> ExtendedSearchType.TO_WHOLE_CITY
            ListingAdsSearchExtensionType.TO_NEARBY_CITIES -> ExtendedSearchType.TO_NEARBY_CITIES
            ListingAdsSearchExtensionType.TO_WHOLE_COUNTRY -> ExtendedSearchType.TO_WHOLE_COUNTRY
            ListingAdsSearchExtensionType.TO_BIGGER_PRICE_RANGE -> ExtendedSearchType.TO_BIGGER_PRICE_RANGE
            else -> null
        }
    }
    return if (types.isNotEmpty()) ListingAd.ExtendedSearch(types) else null
}

fun GetListingAdsQuery.OnNewConstructionAd.toNewConstructionAd(): ListingAd.NewConstruction =
    ListingAd.NewConstruction(
        title = title,
        location = City(
            id = location.city.id,
            name = location.city.name,
            trackingName = location.city.trackingValue,
            area = location.area?.let {
                Area(
                    id = it.id,
                    name = it.name,
                    trackingName = it.trackingValue
                )
            }
        ),
        price = price?.let {
            AdPrice.Available(
                current = it.withoutCurrency,
                currentWithCurrency = it.withCurrency,
                old = null,
                oldWithCurrency = null,
                changeType = null
            )
        } ?: AdPrice.Unavailable,
        defaultImage = media.defaultImage?.defaultPath,
        imagesCount = media.mediaCount,
        externalLink = externalLink,
        rooms = rooms ?: 0,
        bathrooms = bathrooms ?: 0,
        size = size ?: 0,
        phoneNumber = null // NewConstruction ads don't have direct phone numbers
    )

fun GetListingAdsQuery.PublishedAndNCDetail.toPublishedOrNewConstructionAd(): ListingAd? =
    when (this.__typename) {
        PUBLISH_AD_TYPE -> this.onPublishedAd?.publishedAdFragment?.toPublishedAd()

        NEW_CONSTRUCTION_AD_TYPE -> this.onNewConstructionAd?.toNewConstructionAd()

        else -> null
    }

// AUTO COMPLETE SUGGESTION
fun AutoCompleteSearchQuery.Suggestion.toSearchSuggestion(): SearchSuggestion {
    val categoryId = category?.id?.takeIf { it.isNotBlank() }
    val categoryName = category?.name?.takeIf { it.isNotBlank() }
    val categoryTrackingName = category?.trackingValue?.takeIf { it.isNotBlank() }
    val category = if (categoryId != null && categoryName != null && categoryTrackingName != null) {
        Category(
            id = categoryId,
            name = categoryName,
            trackingName = categoryTrackingName,
            icon = buildCategoryIconUrl(categoryId)
        )
    } else null

    val adTypeKey = adType?.key?.rawValue?.takeIf { it.isNotBlank() }
    val adTypeName = adType?.name?.takeIf { it.isNotBlank() }
    val adType = if (adTypeKey != null && adTypeName != null) {
        AdType(
            key = DomainAdTypeKey.valueOf(adTypeKey.uppercase()),
            name = adTypeName,
            trackingName = ""
        )
    } else null

    val cityId = city?.id?.takeIf { it.isNotBlank() }
    val cityName = city?.name?.takeIf { it.isNotBlank() }
    val cityTrackingName = city?.trackingValue?.takeIf { it.isNotBlank() }
    val city = if (cityId != null && cityName != null && cityTrackingName != null) {
        City(
            id = cityId,
            name = cityName,
            trackingName = cityTrackingName
        )
    } else null

    return SearchSuggestion(
        uuid = UUID.randomUUID().toString(),
        keyword = suggestion.takeIf { it.isNotBlank() },
        category = category,
        adType = adType,
        city = city,
        modelKey = when {
            model?.onTextAdParam?.paramRawValue.isNullOrEmpty().not() -> model.onTextAdParam.id
            phoneModel?.onTextAdParam?.paramRawValue.isNullOrEmpty()
                .not() -> phoneModel.onTextAdParam.id

            else -> null
        },
        modelId = model?.onTextAdParam?.paramRawValue.takeIf { it.isNullOrEmpty().not() }
            ?: phoneModel?.onTextAdParam?.paramRawValue.takeIf { it.isNullOrEmpty().not() },
        modelName = model?.onTextAdParam?.textValue.takeIf { it.isNullOrEmpty().not() }
            ?: phoneModel?.onTextAdParam?.textValue.takeIf { it.isNullOrEmpty().not() },
        brandKey = when {
            brand?.onTextAdParam?.paramRawValue.isNullOrEmpty().not() -> brand.onTextAdParam.id
            phoneBrand?.onTextAdParam?.paramRawValue.isNullOrEmpty()
                .not() -> phoneBrand.onTextAdParam.id

            else -> null
        },
        brandId = brand?.onTextAdParam?.paramRawValue.takeIf { it.isNullOrEmpty().not() }
            ?: phoneBrand?.onTextAdParam?.paramRawValue.takeIf { it.isNullOrEmpty().not() },
        brandName = brand?.onTextAdParam?.textValue.takeIf { it.isNullOrEmpty().not() }
            ?: phoneBrand?.onTextAdParam?.textValue.takeIf { it.isNullOrEmpty().not() },
        isHistory = false,
    )
}


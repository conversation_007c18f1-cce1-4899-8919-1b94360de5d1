package se.scmv.morocco.data.repository.messaging

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.apollographql.apollo3.ApolloClient
import com.apollographql.apollo3.api.Optional
import se.scmv.morocco.GetConversationsListQuery
import se.scmv.morocco.data.mappers.toConversation
import se.scmv.morocco.data.mappers.toISOString
import se.scmv.morocco.data.repository.utils.wrapWithErrorHandling
import se.scmv.morocco.domain.Conversation
import java.time.Instant
import java.util.Date

class ConversationPagingSource(
    private val apolloClient: ApolloClient
) : PagingSource<Long, Conversation>() {

    companion object {
        const val PAGE_SIZE = 20
    }

    override fun getRefreshKey(state: PagingState<Long, Conversation>): Long? {
        // Try to find the timestamp key of the closest page to anchorPosition from
        return state.anchorPosition?.let { anchorPosition ->
            val anchorPage = state.closestPageToPosition(anchorPosition)
            anchorPage?.prevKey ?: anchorPage?.nextKey
        }
    }

    override suspend fun load(params: LoadParams<Long>): LoadResult<Long, Conversation> {
        val beforeTime = params.key?.let { Date(it) }
        val pageSize = params.loadSize.coerceAtMost(PAGE_SIZE)

        return wrapWithErrorHandling(
            call = {
                apolloClient.query(
                    GetConversationsListQuery(
                        size = pageSize,
                        beforeTime = beforeTime?.let { Optional.present(it.toISOString()) } ?: Optional.absent(),
                        afterTime = Optional.absent()
                    )
                ).execute()
            },
            toData = { response -> response.data?.getMyChat },
            toPage = { data ->
                val conversations = data.conversations?.mapNotNull { it?.toConversation() }
                    ?: throw Exception("No data received")

                // Get the timestamp of the last conversation for next page
                val nextKey = if (conversations.isEmpty()) {
                    null
                } else {
                    conversations.lastOrNull()?.lastMessage?.time?.time
                }

                LoadResult.Page(
                    data = conversations,
                    prevKey = null, // We don't support backward pagination for now
                    nextKey = nextKey
                )
            }
        )
    }

}

package se.scmv.morocco.data.repository.utils

import androidx.paging.PagingSource.LoadResult
import com.apollographql.apollo3.api.ApolloResponse
import com.apollographql.apollo3.api.Operation
import com.apollographql.apollo3.exception.ApolloHttpException
import com.apollographql.apollo3.exception.ApolloNetworkException
import se.scmv.morocco.domain.models.NetworkAndBackendErrors
import se.scmv.morocco.domain.models.NetworkErrors
import se.scmv.morocco.domain.models.NetworkExceptions
import se.scmv.morocco.domain.models.Resource

inline fun <OperationData : Operation.Data, Data : Any, DomainModel : Any> wrapWithErrorHandling(
    call: () -> ApolloResponse<OperationData>,
    toData: (ApolloResponse<OperationData>) -> Data?,
    toDomainModel: (Data) -> DomainModel
): Resource<DomainModel, NetworkAndBackendErrors> {
    return try {
        val response = call()
        val data = toData(response)
        if (data == null) {
            if (response.hasErrors()) {
                val message = response.errors?.firstOrNull()?.message
                Resource.Failure(NetworkAndBackendErrors.Backend(message.orEmpty()))
            } else {
                Resource.Failure(NetworkAndBackendErrors.Network(NetworkErrors.UNKNOWN))
            }
        } else {
            Resource.Success(toDomainModel(data))
        }
    } catch (e: ApolloNetworkException) {
        e.printStackTrace()
        Resource.Failure(NetworkAndBackendErrors.Network(NetworkErrors.NO_INTERNET))
    } catch (e: ApolloHttpException) {
        e.printStackTrace()
        Resource.Failure(NetworkAndBackendErrors.Network(NetworkErrors.NO_INTERNET))
    } catch (e: Exception) {
        e.printStackTrace()
        Resource.Failure(NetworkAndBackendErrors.Network(NetworkErrors.UNKNOWN))
    }
}

// PAGINATION
/**
 * Calculates the number of the next page given the current page number,
 * page size, and total number of items.
 *
 * @param current The current page number.
 * @param pageSize The number of items per page.
 * @param total The total number of items.
 * @return The number of the next page if available, null otherwise.
 */
fun getNextPageNumber(current: Int, pageSize: Int, total: Int): Int? {
    if (pageSize <= 0) return null

    val availablePages = if (total % pageSize == 0) {
        total / pageSize
    } else (total / pageSize).inc()
    val nextPageNumber = if (current < availablePages) {
        current.inc()
    } else null
    return nextPageNumber
}

inline fun <Key : Any, OperationData : Operation.Data, Data : Any, DomainModel : Any> wrapWithErrorHandling(
    call: () -> ApolloResponse<OperationData>,
    toData: (ApolloResponse<OperationData>) -> Data?,
    toPage: (Data) -> LoadResult.Page<Key, DomainModel>
): LoadResult<Key, DomainModel> {
    return try {
        val response = call()
        val data = toData(response)
        if (data == null) {
            val message = response.errors?.firstOrNull()?.message
            if (response.hasErrors() && message != null) {
                LoadResult.Error(NetworkExceptions.Backend(message = message))
            } else {
                LoadResult.Error(NetworkExceptions.Unknown())
            }
        } else {
            toPage(data)
        }
    } catch (e: ApolloNetworkException) {
        e.printStackTrace()
        LoadResult.Error(NetworkExceptions.NoInternet())
    } catch (e: ApolloHttpException) {
        e.printStackTrace()
        LoadResult.Error(NetworkExceptions.NoInternet())
    } catch (e: Exception) {
        e.printStackTrace()
        LoadResult.Error(NetworkExceptions.Unknown())
    }
}

fun <OperationData : Operation.Data> ApolloResponse<OperationData>.firstErrorKeyOrNull(): String? =
    errors?.firstOrNull()?.extensions?.get("code")?.toString()

fun <OperationData : Operation.Data> ApolloResponse<OperationData>.firstErrorMessageOrNull(): String? =
    errors?.firstOrNull()?.message?.toString()

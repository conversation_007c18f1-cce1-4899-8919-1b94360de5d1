package se.scmv.morocco.data.repository.utils


// TODO this should be stored in firebase remote config.
object Constants {
    const val KEY_LANGUAGE = "lang"
    const val KEY_CATEGORY = "category"
    const val KEY_AD_TYPE = "adType"
    const val KEY_KEYWORD = "keyword"
    const val KEY_HAS_PRICE = "has_price"
    const val KEY_HAS_IMAGE = "has_image"
    const val KEY_CITY = "city"
    const val KEY_AREA = "area"
    const val KEY_BRAND = "brand"
    const val KEY_MODEL = "model"
    const val KEY_DELIVERY = "delivery"
    const val KEY_CITY_DELIVERY = "offers_shipping_within_city"
    const val KEY_HOT_DEAL = "is_hotDeal"
    const val KEY_URGENT = "is_urgent"
    const val KEY_ECOMMERCE = "is_ecommerce"
    const val KEY_NEW_CONSTRUCTION = "is_immoneuf"
    const val KEY_VERIFIED_SELLER = "verified_seller"
    const val KEY_PRICE = "price"
    const val KEY_PRICE_SORTING = "AdSortProperty"
    const val KEY_SELLER_TYPE = "seller_type"
    const val VALUE_SELLER_TYPE_STORE = "1"
    const val VALUE_SELLER_TYPE_PRIVATE = "0"
    const val KEY_PHONE = "phone"
    const val KEY_TITLE = "title"
    const val KEY_DESCRIPTION = "description"
    const val KEY_NAME = "name"
    const val KEY_PHONE_HIDDEN = "phone_hidden"
    const val KEY_IMAGE_UPLOAD = "images"
    const val KEY_VIDEO_UPLOAD = "videos"
    const val KEY_ADDRESS = "address"

    const val CONSTRUCTIONS_CATEGORY_ID_1010 = 1010
    const val CONSTRUCTIONS_CATEGORY_ID_1020 = 1020
    const val CONSTRUCTIONS_CATEGORY_ID_1040 = 1040
    const val CONSTRUCTIONS_CATEGORY_ID_1200 = 1200
    const val CONSTRUCTIONS_CATEGORY_ID_1050 = 1050
    const val CONSTRUCTIONS_CATEGORY_ID_1060 = 1060
    const val CONSTRUCTIONS_CATEGORY_ID_1080 = 1080
}
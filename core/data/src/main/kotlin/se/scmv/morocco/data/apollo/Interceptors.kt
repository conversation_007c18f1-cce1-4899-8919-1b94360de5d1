package se.scmv.morocco.data.apollo

import com.apollographql.apollo3.api.ApolloRequest
import com.apollographql.apollo3.api.ApolloResponse
import com.apollographql.apollo3.api.Operation
import com.apollographql.apollo3.api.http.HttpRequest
import com.apollographql.apollo3.api.http.HttpResponse
import com.apollographql.apollo3.interceptor.ApolloInterceptor
import com.apollographql.apollo3.interceptor.ApolloInterceptorChain
import com.apollographql.apollo3.network.http.HttpInterceptor
import com.apollographql.apollo3.network.http.HttpInterceptorChain
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import se.scmv.morocco.common.lang.LocaleManager
import se.scmv.morocco.data.session.SessionManager

/**
 * An implementation of [HttpInterceptor] that adds an `Authorization` header to HTTP requests.
 * The header includes a Bearer token obtained from the [SessionManager].
 *
 * This class uses a [Mutex] to ensure thread-safe access to the access token.
 *
 * @property sessionManager The [SessionManager] that provides access to the current access token.
 */
class AuthorizationInterceptor(
    private val sessionManager: SessionManager
) : HttpInterceptor {
    private val mutex = Mutex()

    override suspend fun intercept(
        request: HttpRequest,
        chain: HttpInterceptorChain
    ): HttpResponse {
        val accessToken = mutex.withLock {
            sessionManager.getAccessToken()
        }
        // If the token is null this means that the user isn't connected but as security we call endSession().
        if (accessToken == null) {
            sessionManager.endSession()
            return chain.proceed(request)
        }
        return chain.proceed(
            request.newBuilder()
                .addHeader("Authorization", "Bearer $accessToken")
                .build()
        )
    }
}

/**
 * An implementation of [ApolloInterceptor] that handles refreshing the access token when
 * a GraphQL operation returns an authentication error (`AUTH_REQUIRED`).
 *
 * The interceptor checks the response for authentication errors, attempts to refresh the token
 * using the current refresh token, and updates the session with the new access token. If the
 * refresh token is unavailable, the session is ended.
 *
 * @property sessionManager The [SessionManager] that manages access tokens, refresh tokens, and session state.
 */
class RefreshTokenInterceptor(
    private val sessionManager: SessionManager
) : ApolloInterceptor {

    override fun <D : Operation.Data> intercept(
        request: ApolloRequest<D>,
        chain: ApolloInterceptorChain
    ): Flow<ApolloResponse<D>> {
        return chain.proceed(request).onEach {
            val isAuthRequired = it.errors?.getOrNull(0)?.extensions?.get("code") == "AUTH_REQUIRED"
            if (isAuthRequired) {
                sessionManager.endSession()
            }
            // TODO Uncomment this code when the backend implement the refreshToken logic
            //  for know we end the session.
            /*if (isAuthRequired) {
                val refreshToken = sessionManager.getRefreshToken() ?: run{
                    sessionManager.endSession()
                    return@onEach
                }
                val accessToken = refreshToken(refreshToken)
                sessionManager.createSession(Token(accessToken, accessToken))
            }*/
        }
    }

    /**
     * Refreshes the access token by calling the backend service. This function is currently a stub
     * and should be implemented to return a valid access token.
     *
     * @param token The refresh token used to obtain a new access token.
     * @return The new access token as a [String].
     */
    private suspend fun refreshToken(token: String): String {
        return ""
    }
}

class LanguageHeaderInterceptor() : HttpInterceptor {

    override suspend fun intercept(
        request: HttpRequest,
        chain: HttpInterceptorChain
    ): HttpResponse {
        val newRequest = request.newBuilder()
            .addHeader("Accept-Language", LocaleManager.getCurrentLanguage())
            .build()
        return chain.proceed(newRequest)
    }
}
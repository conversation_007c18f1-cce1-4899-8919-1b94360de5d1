package se.scmv.morocco.data.di

import android.content.Context
import android.util.Log
import com.apollographql.apollo3.ApolloClient
import com.apollographql.apollo3.network.okHttpClient
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.adapter.rxjava2.RxJava2CallAdapterFactory
import retrofit2.converter.gson.GsonConverterFactory
import se.scmv.morocco.data.BuildConfig
import se.scmv.morocco.data.apollo.AuthorizationInterceptor
import se.scmv.morocco.data.apollo.LanguageHeaderInterceptor
import se.scmv.morocco.data.apollo.RefreshTokenInterceptor
import se.scmv.morocco.data.rest.ad.AdReportApi
import se.scmv.morocco.data.rest.car.CarCheckApi
import se.scmv.morocco.data.rest.config.ConfigApi
import se.scmv.morocco.data.rest.hermes.HermesApi
import se.scmv.morocco.data.rest.tp.TouchingPointApi
import se.scmv.morocco.data.session.SessionManager
import java.util.concurrent.TimeUnit
import javax.inject.Qualifier
import javax.inject.Singleton

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class CarCheckOkHttpClient

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class WebSocketApolloClient

@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {

    @Provides
    @Singleton
    fun provideApolloClient(
        @ApplicationContext context: Context,
        sessionManager: SessionManager
    ): ApolloClient {
        return ApolloClient.Builder()
            .serverUrl(getGraphQlEndpointUrl(context))
            .addHttpInterceptor(AuthorizationInterceptor(sessionManager))
            .addHttpInterceptor(LanguageHeaderInterceptor())
            .addInterceptor(RefreshTokenInterceptor(sessionManager))
            .okHttpClient(provideOkHttpClient())
            .build()
    }

    @WebSocketApolloClient
    @Provides
    @Singleton
    fun provideWebSocketApolloClient(@ApplicationContext context: Context): ApolloClient {
        return ApolloClient.Builder()
            .serverUrl(getGraphQlEndpointUrl(context))
            .webSocketServerUrl("wss://gateway.avito.ma/websocket")
            .build()
    }

    @Singleton
    @Provides
    fun provideConfigApi(@ApplicationContext context: Context): ConfigApi {
        return provideRetrofit(
            okHttpClient = provideOkHttpClient(),
            baseUrl = getConfigApiUrl(context)
        ).create(ConfigApi::class.java)
    }

    @Singleton
    @Provides
    fun provideHermesApi(): HermesApi {
        return provideRetrofit(
            okHttpClient = provideOkHttpClient(),
            baseUrl = "https://81.192.111.246:9988/"
        ).create(HermesApi::class.java)
    }

    // TODO Provide this as Singleton once we move all the network apis to this module
    private fun provideRetrofit(okHttpClient: OkHttpClient, baseUrl: String) = Retrofit.Builder()
        .baseUrl(baseUrl)
        .client(okHttpClient)
        .addConverterFactory(GsonConverterFactory.create())
        .addCallAdapterFactory(RxJava2CallAdapterFactory.create())
        .build()

    // TODO Provide this as Singleton once we move all the network apis to this module
    private fun provideOkHttpClient(): OkHttpClient {
        return OkHttpClient.Builder()
            .addNetworkInterceptor(
                HttpLoggingInterceptor().apply {
                    if (BuildConfig.DEBUG) {
                        level = HttpLoggingInterceptor.Level.BODY
                    }
                }
            )
            .connectTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(2, TimeUnit.MINUTES)
            .readTimeout(2, TimeUnit.MINUTES)
            .build()
    }

    @Singleton
    @Provides
    fun provideTouchingPointApi(@ApplicationContext context: Context): TouchingPointApi {
        return provideRetrofit(
            okHttpClient = provideOkHttpClient(),
            baseUrl = getMediaServerUrl(context = context)
        ).create(TouchingPointApi::class.java)
    }

    
    @Singleton
    @Provides
    fun provideCarCheckApi(okHttpClient: OkHttpClient): CarCheckApi {
        return provideRetrofit(
            okHttpClient = okHttpClient
                .newBuilder()
                .addInterceptor(Interceptor { chain ->
                    val originalRequest = chain.request()
                    val requestBuilder = originalRequest.newBuilder()
                        .header("Content-Type", "application/json")
                        .header("x-api-token", "d6b4ac2a5e7dd8b17bf1035753795d6d79926ea8")
                    Log.d("CarCheckApi_Header", "Added headers: ${requestBuilder.build().toString()}")
                    val request = requestBuilder.build()
                    chain.proceed(request)
                })
                .build(),
            baseUrl = "https://api.leadsforce.ma/"
        ).create(CarCheckApi::class.java)
    }

    @Singleton
    @Provides
    fun provideAdReportApi(): AdReportApi {
        return provideRetrofit(
            okHttpClient = provideOkHttpClient(),
            baseUrl = "https://services.avito.ma/api/v1/"
        ).create(AdReportApi::class.java)
    }
}

// TODO These are temporary functions for configuring build parameters in your project.
/**
 * Replace these with proper usage of `buildConfigField`, `productFlavors`, and `flavorDimensions` in your Gradle configuration.
 *
 * Example:
 * ```groovy
 * android {
 *     flavorDimensions += "environment"
 *     productFlavors {
 *         create("pre") {
 *             dimension = "environment"
 *             buildConfigField "String", "GRAPHQL_ENDPOINT_BASE_URL", '"https://gateway-pre.avito.ma/graphql"'
 *         }
 *         create("prod") {
 *             dimension = "environment"
 *             buildConfigField "String", "GRAPHQL_ENDPOINT_BASE_URL", '"https://gateway.avito.ma/graphql"'
 *         }
 *     }
 * }
 * ```
 *
 * Once the TODO is completed, these temporary functions will no longer be necessary.
 */
fun getGraphQlEndpointUrl(context: Context): String {
    val prefs = context.getSharedPreferences("avito_prefs", Context.MODE_PRIVATE)
    val env = prefs.getString("app_environment", "pro")

    return when (env) {
        "pre" -> "https://gateway-pre.avito.ma/graphql"
        else -> "https://gateway.avito.ma/graphql"
    }
}

fun getConfigApiUrl(context: Context): String {
    val prefs = context.getSharedPreferences("avito_prefs", Context.MODE_PRIVATE)
    val env = prefs.getString("app_environment", "pro")

    return when (env) {
        "pre" -> "https://services-pre.avito.ma/api/v2/config/"
        else -> "https://services.avito.ma/api/v2/config/"
    }
}

fun getLeadsForceApiUrl(context: Context): String {
    val prefs = context.getSharedPreferences("avito_prefs", Context.MODE_PRIVATE)
    val env = prefs.getString("app_environment", "pro")

    return when (env) {
        "pre" -> "https://api-pre.leadsforce.ma/"
        else -> "https://api.leadsforce.ma/"
    }
}

fun getMediaServerUrl(context: Context): String {
    val prefs = context.getSharedPreferences("avito_prefs", Context.MODE_PRIVATE)
    val env = prefs.getString("app_environment", "pro")

    return when (env) {
        "pre" -> "https://media-server-pre.avito.ma/"
        else -> "https://media-server.avito.ma/"
    }
}
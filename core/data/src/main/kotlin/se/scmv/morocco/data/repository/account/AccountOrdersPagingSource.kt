package se.scmv.morocco.data.repository.account

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.apollographql.apollo3.ApolloClient
import se.scmv.morocco.GetAccountOrdersQuery
import se.scmv.morocco.data.mappers.toAccountOrder
import se.scmv.morocco.data.repository.utils.getNextPageNumber
import se.scmv.morocco.data.repository.utils.wrapWithErrorHandling
import se.scmv.morocco.domain.models.AccountOrder
import se.scmv.morocco.type.MyPurchaseOrdersFilterInput
import se.scmv.morocco.type.MyPurchaseOrdersQuery
import se.scmv.morocco.type.Page
import se.scmv.morocco.type.PurchaseOrderStatus

class AccountOrdersPagingSource(
    private val apolloClient: ApolloClient,
    private val status: PurchaseOrderStatus
) : PagingSource<Int, AccountOrder>() {

    companion object {
        const val START_PAGE = 1
        const val PAGE_SIZE = 35
    }

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, AccountOrder> {
        // Start refresh at page 1 if undefined.
        val pageNumber = params.key ?: START_PAGE
        return wrapWithErrorHandling(
            call = {
                val query = GetAccountOrdersQuery(
                    query = MyPurchaseOrdersQuery(
                        filters = MyPurchaseOrdersFilterInput(status = status),
                        page = Page(number = pageNumber, size = params.loadSize)
                    )
                )
                apolloClient.query(query).execute()
            },
            toData = { apolloResponse -> apolloResponse.data?.getMyPurchaseOrders },
            toPage = { data ->
                LoadResult.Page(
                    data = data.orders.map { it.toAccountOrder() },
                    prevKey = null,
                    nextKey = getNextPageNumber(
                        current = pageNumber,
                        pageSize = PAGE_SIZE,
                        total = data.count.total
                    ),
                )
            }
        )
    }

    override fun getRefreshKey(state: PagingState<Int, AccountOrder>): Int? {
        // Try to find the page key of the closest page to anchorPosition from
        // either the prevKey or the nextKey; you need to handle nullability
        // here.
        //  * prevKey == null -> anchorPage is the first page.
        //  * nextKey == null -> anchorPage is the last page.
        //  * both prevKey and nextKey are null -> anchorPage is the
        //    initial page, so return null.
        return state.anchorPosition?.let { anchorPosition ->
            val anchorPage = state.closestPageToPosition(anchorPosition)
            anchorPage?.prevKey?.plus(1) ?: anchorPage?.nextKey?.minus(1)
        }
    }
}
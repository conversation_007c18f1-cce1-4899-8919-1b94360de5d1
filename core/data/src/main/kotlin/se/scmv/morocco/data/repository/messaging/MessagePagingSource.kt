package se.scmv.morocco.data.repository.messaging

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.apollographql.apollo3.ApolloClient
import com.apollographql.apollo3.api.Optional
import se.scmv.morocco.GetChatConversationByIdQuery
import se.scmv.morocco.data.mappers.toMessage
import se.scmv.morocco.data.mappers.toISOString
import se.scmv.morocco.data.repository.utils.wrapWithErrorHandling
import se.scmv.morocco.domain.Message
import java.time.Instant
import java.util.Date

class MessagePagingSource(
    private val apolloClient: ApolloClient,
    private val conversationId: String
) : PagingSource<Long, Message>() {

    companion object {
        const val PAGE_SIZE = 10 // Smaller page size for faster refreshes
    }

    override fun getRefreshKey(state: PagingState<Long, Message>): Long? {
        // Try to find the timestamp key of the closest page to anchorPosition from
        return state.anchorPosition?.let { anchorPosition ->
            val anchorPage = state.closestPageToPosition(anchorPosition)
            anchorPage?.prevKey ?: anchorPage?.nextKey
        }
    }

    override suspend fun load(params: LoadParams<Long>): LoadResult<Long, Message> {
        val beforeTime = params.key?.let { Date(it) }
        val pageSize = params.loadSize.coerceAtMost(PAGE_SIZE)

        return wrapWithErrorHandling(
            call = {
                apolloClient.query(
                    GetChatConversationByIdQuery(
                        id = conversationId,
                        size = pageSize,
                        beforeTime = beforeTime?.let { Optional.present(it.toISOString()) } ?: Optional.absent(),
                        afterTime = Optional.absent()
                    )
                ).execute()
            },
            toData = { response -> response.data?.getMyChatConversation },
            toPage = { data ->
                val messages = data.messages?.mapNotNull { it?.toMessage() }
                    ?: throw Exception("No messages received")

                // Get the timestamp of the last message for next page
                val nextKey = if (messages.isEmpty()) {
                    null
                } else {
                    messages.lastOrNull()?.time?.time
                }

                LoadResult.Page(
                    data = messages,
                    prevKey = null, // We don't support backward pagination for now
                    nextKey = nextKey
                )
            }
        )
    }

}

package se.scmv.morocco.data.repository.account

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.apollographql.apollo3.ApolloClient
import se.scmv.morocco.GetAccountSavedAdsQuery
import se.scmv.morocco.data.mappers.toPublishedAd
import se.scmv.morocco.data.repository.utils.getNextPageNumber
import se.scmv.morocco.data.repository.utils.wrapWithErrorHandling
import se.scmv.morocco.domain.models.ListingAd
import se.scmv.morocco.type.Page

class AccountBookmarkedAdsNetworkPagingSource(
    private val apolloClient: ApolloClient
) : PagingSource<Int, ListingAd.Published>() {

    companion object {
        const val START_PAGE = 1
        const val PAGE_SIZE = 12
    }

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, ListingAd.Published> {
        // Start refresh at page 1 if undefined.
        val pageNumber = params.key ?: START_PAGE
        return wrapWithErrorHandling(
            call = {
                apolloClient.query(
                    GetAccountSavedAdsQuery(Page(number = pageNumber, size = params.loadSize))
                ).execute()
            },
            toData = { apolloResponse ->
                apolloResponse.data?.getMySavedAds
            },
            toPage = { data ->
                LoadResult.Page(
                    data = data.ads?.map { it.publishedAdFragment.toPublishedAd() }.orEmpty(),
                    prevKey = null, // Only paging forward.
                    nextKey = getNextPageNumber(
                        current = pageNumber,
                        pageSize = PAGE_SIZE,
                        total = data.count.total
                    )
                )
            }
        )
    }

    override fun getRefreshKey(state: PagingState<Int, ListingAd.Published>): Int? {
        // Try to find the page key of the closest page to anchorPosition from
        // either the prevKey or the nextKey; you need to handle nullability
        // here.
        //  * prevKey == null -> anchorPage is the first page.
        //  * nextKey == null -> anchorPage is the last page.
        //  * both prevKey and nextKey are null -> anchorPage is the
        //    initial page, so return null.
        return state.anchorPosition?.let { anchorPosition ->
            val anchorPage = state.closestPageToPosition(anchorPosition)
            anchorPage?.prevKey?.plus(1) ?: anchorPage?.nextKey?.minus(1)
        }
    }
}
package se.scmv.morocco.data.repository.utils

fun buildCategoryIconUrl(iconId: String): String {
    return "https://assets.avito.ma/icons/svg/category_$iconId.svg"
}

fun buildAdParamIconUrl(iconId: String): String {
    return "https://assets.avito.ma/icons/svg/adparam_$iconId.svg"
}

fun buildOrionComponentIconUrl(iconId: String): String {
    return "https://assets.avito.ma/icons/svg/$iconId.svg"
}

fun buildAdImageUrl(defaultPath: String): String {
    return "https://content.avito.ma/images/${defaultPath}"
}

fun buildAdVideoUrl(defaultPath: String): String {
    return "https://content.avito.ma/ad-videos/objects/${defaultPath}"
}
package se.scmv.morocco.data.database.entities

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "recent_searches")
data class RecentSearchEntity(
    @PrimaryKey @ColumnInfo("uuid") val uuid: String,
    @ColumnInfo("created_at") val createdAt: Long,
    @ColumnInfo("keyword") val keyword: String?,
    @ColumnInfo("category_id") val categoryId: String?,
    @ColumnInfo("category_name") val categoryName: String?,
    @ColumnInfo("category_tracking_name") val categoryTrackingName: String?,
    @ColumnInfo("ad_type_key") val adTypeKey: String?,
    @ColumnInfo("ad_type_name") val adTypeName: String?,
    @ColumnInfo("city_id") val cityId: String?,
    @ColumnInfo("city_name") val cityName: String?,
    @ColumnInfo("city_tracking_name") val cityTrackingName: String?,
    @ColumnInfo("model_key") val modelKey: String?,
    @ColumnInfo("model_id") val modelId: String?,
    @ColumnInfo("model_name") val modelName: String?,
    @ColumnInfo("brand_key") val brandKey: String?,
    @ColumnInfo("brand_id") val brandId: String?,
    @ColumnInfo("brand_name") val brandName: String?,
)
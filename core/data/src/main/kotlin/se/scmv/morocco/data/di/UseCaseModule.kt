package se.scmv.morocco.data.di

import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.ViewModelComponent
import se.scmv.morocco.domain.repositories.AdViewRepository
import se.scmv.morocco.domain.usecases.GetAndFilterTouchingPointsUseCase

@Module
@InstallIn(ViewModelComponent::class)
object UseCaseModule {

    @Provides
    fun provideGetAndFilterTouchingPointsUseCase(
        adViewRepository: AdViewRepository
    ): GetAndFilterTouchingPointsUseCase {
        return GetAndFilterTouchingPointsUseCase(adViewRepository)
    }
}

package se.scmv.morocco.data.repository

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.apollographql.apollo3.ApolloClient
import com.apollographql.apollo3.api.Optional
import se.scmv.morocco.GetListingAdByStoreIdQuery
import se.scmv.morocco.data.mappers.toAdParamsListMatchFilters
import se.scmv.morocco.data.mappers.toAdParamsRangeFilterList
import se.scmv.morocco.data.mappers.toAdParamsSingleMatchFilters
import se.scmv.morocco.data.mappers.toPublishedAd
import se.scmv.morocco.data.mappers.toRangeFilter
import se.scmv.morocco.domain.models.ListingAd
import se.scmv.morocco.domain.models.filter.BooleanParam
import se.scmv.morocco.domain.models.filter.ListMatch
import se.scmv.morocco.domain.models.filter.NumericParam
import se.scmv.morocco.domain.models.filter.ParamsFilter
import se.scmv.morocco.domain.models.filter.RangeParam
import se.scmv.morocco.domain.models.filter.SingleMatch
import se.scmv.morocco.domain.models.filter.SingleTextParam
import se.scmv.morocco.domain.models.filter.TextParam
import se.scmv.morocco.type.AdSortProperty
import se.scmv.morocco.type.ListingAdFilter
import se.scmv.morocco.type.ListingAdParamsFilters
import se.scmv.morocco.type.ListingAdsSearchFilters
import se.scmv.morocco.type.ListingSellerFilter
import se.scmv.morocco.type.Page
import se.scmv.morocco.type.SortOrder

class ListingPagingSource(
    private val apolloClient: ApolloClient,
    private val storeId: String,
    private val text: String?,
    private val categoryId: Int,
    private val hasImage: Boolean?,
    private val hasPrice: Boolean?,
    private val priceRange: Pair<Double, Double>?,
    // FIXME the following parameters are not used in the query !!
    private val cityIds: List<Int>?,
    private val offersShipping: Boolean?,
    private val isHotDeal: Boolean?,
    private val isUrgent: Boolean?,
    private val numericParams: List<Pair<String, Double>>?,
    private val singleTextParam: List<Pair<String, String>>?,
    private val singleBooleanParam: List<Pair<String, Boolean>>?,
    private val textParams: List<Pair<String, List<String>>>?,
    private val rangeParams: List<RangeParam>?
): PagingSource<Int, ListingAd.Published>() {

    companion object{
        const val STARTING_PAGE = 1
        const val PAGE_SIZE = 35
    }

    override fun getRefreshKey(state: PagingState<Int, ListingAd.Published>): Int? {
        return state.anchorPosition?.let { anchorPosition ->
            state.closestPageToPosition(anchorPosition)?.prevKey?.plus(1)
                ?: state.closestPageToPosition(anchorPosition)?.nextKey?.minus(1)
        }
    }

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, ListingAd.Published> {
        val pageNumber = params.key ?: STARTING_PAGE

        val paramsFilter = ParamsFilter(
            singleMatch = SingleMatch(
                text = singleTextParam?.map { param -> SingleTextParam(param.first, param.second) },
                numeric = numericParams?.map { param -> NumericParam(param.first, param.second) },
                boolean = singleBooleanParam?.map { param ->
                    BooleanParam(
                        param.first,
                        param.second
                    )
                }
            ),
            listMatch = textParams?.let {
                ListMatch(it.map { param -> TextParam(param.first, param.second) })
            },
            rangeMatch = rangeParams
        )

        try {
            val response = apolloClient.query(
                GetListingAdByStoreIdQuery(
                    filters = Optional.Present(
                        ListingAdsSearchFilters(
                            seller = Optional.present(
                                ListingSellerFilter(
                                    storeId = Optional.present(storeId)
                                )
                            ),
                            ad = Optional.presentIfNotNull(
                                ListingAdFilter(
                                    text = Optional.presentIfNotNull(text),
                                    categoryId = Optional.present(categoryId),
                                    hasImage = Optional.presentIfNotNull(hasImage),
                                    hasPrice = Optional.presentIfNotNull(hasPrice),
                                    type = Optional.absent(),
                                    price = Optional.present(priceRange.toRangeFilter()),
                                    location = Optional.absent(),
                                    offersShipping = Optional.presentIfNotNull(offersShipping),
                                    isHotDeal = Optional.presentIfNotNull(isHotDeal),
                                    isUrgent = Optional.presentIfNotNull(isUrgent),
                                    isVerifiedSeller = Optional.absent(),
                                    isEcommerce = Optional.absent(),
                                    isImmoneuf = Optional.absent(),
                                    isPremium = Optional.absent(),
                                    params = Optional.presentIfNotNull(
                                        ListingAdParamsFilters(
                                            listMatch = Optional.presentIfNotNull(paramsFilter.listMatch.toAdParamsListMatchFilters()),
                                            singleMatch = Optional.presentIfNotNull(paramsFilter.singleMatch.toAdParamsSingleMatchFilters()),
                                            rangeMatch = Optional.presentIfNotNull(paramsFilter.rangeMatch.toAdParamsRangeFilterList())
                                        )
                                    )
                                )
                            )
                        )
                    ),
                    sortOrder = SortOrder.DESC,
                    adProperty = AdSortProperty.LIST_TIME,
                    page = Page(number = pageNumber, size = PAGE_SIZE)
                )
            ).execute()
            val adsResponse = response.data?.getListingAds?.ads?.details
                ?.mapNotNull { it?.onPublishedAd?.publishedAdFragment?.toPublishedAd() }
            // FIXME the pagination should be handled using nextScrollId !!
            return if (adsResponse != null) {
                LoadResult.Page(
                    data = adsResponse,
                    prevKey = null,
                    nextKey = if (adsResponse.isEmpty()) null else if (adsResponse.size < PAGE_SIZE) {
                        null
                    } else {
                        pageNumber + 1
                    }
                )
            } else {
                LoadResult.Error(Throwable())
            }
        } catch (e: Exception) {
            e.printStackTrace()
            return LoadResult.Error(e)
        }
    }
}
package se.scmv.morocco.data.repository

import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import com.apollographql.apollo3.ApolloClient
import com.apollographql.apollo3.api.Optional
import kotlinx.coroutines.flow.Flow
import se.scmv.morocco.GetListingAdByStoreIdCountQuery
import se.scmv.morocco.GetStoreDetailsQuery
import se.scmv.morocco.data.mappers.toAdParamsListMatchFilters
import se.scmv.morocco.data.mappers.toAdParamsRangeFilterList
import se.scmv.morocco.data.mappers.toAdParamsSingleMatchFilters
import se.scmv.morocco.data.mappers.toRangeFilter
import se.scmv.morocco.data.mappers.toStoreProfile
import se.scmv.morocco.data.repository.utils.wrapWithErrorHandling
import se.scmv.morocco.domain.models.ListingAd
import se.scmv.morocco.domain.models.NetworkAndBackendErrors
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.models.StoreProfileInfo
import se.scmv.morocco.domain.models.filter.BooleanParam
import se.scmv.morocco.domain.models.filter.ListMatch
import se.scmv.morocco.domain.models.filter.NumericParam
import se.scmv.morocco.domain.models.filter.ParamsFilter
import se.scmv.morocco.domain.models.filter.RangeParam
import se.scmv.morocco.domain.models.filter.SingleMatch
import se.scmv.morocco.domain.models.filter.SingleTextParam
import se.scmv.morocco.domain.models.filter.TextParam
import se.scmv.morocco.domain.repositories.ListingAdRepository
import se.scmv.morocco.type.AdSortProperty
import se.scmv.morocco.type.AdTypeKey
import se.scmv.morocco.type.ListingAdFilter
import se.scmv.morocco.type.ListingAdParamsFilters
import se.scmv.morocco.type.ListingAdsSearchFilters
import se.scmv.morocco.type.ListingSellerFilter
import se.scmv.morocco.type.Page
import se.scmv.morocco.type.SortOrder
import javax.inject.Inject

class ListingAdRepositoryImpl @Inject constructor(
    private val apolloClient: ApolloClient
) : ListingAdRepository {
    override fun getListingStoreAdByStoreIDForPagination(
        storeId: String,
        text: String?,
        categoryId: Int,
        hasImage: Boolean?,
        hasPrice: Boolean?,
        priceRange: Pair<Double, Double>?,
        cityIds: List<Int>?,
        offersShipping: Boolean?,
        isHotDeal: Boolean?,
        isUrgent: Boolean?,
        numericParams: List<Pair<String, Double>>?,
        singleTextParam: List<Pair<String, String>>?,
        singleBooleanParam: List<Pair<String, Boolean>>?,
        textParams: List<Pair<String, List<String>>>?,
        rangeParams: List<RangeParam>?,
    ): Flow<PagingData<ListingAd.Published>> {
        return Pager(PagingConfig(pageSize = 35)) {
            ListingPagingSource(
                apolloClient = apolloClient,
                storeId = storeId,
                text = text,
                categoryId = categoryId,
                hasImage = hasImage,
                hasPrice = hasPrice,
                priceRange = priceRange,
                cityIds = cityIds,
                offersShipping = offersShipping,
                isHotDeal = isHotDeal,
                isUrgent = isUrgent,
                numericParams = numericParams,
                singleTextParam = singleTextParam,
                singleBooleanParam = singleBooleanParam,
                textParams = textParams,
                rangeParams = rangeParams
            )
        }.flow
    }

    override suspend fun getListingStoreAdByStoreID(
        storeId: String,
        text: String?,
        categoryId: Int,
        hasImage: Boolean?,
        hasPrice: Boolean?,
        priceRange: Pair<Double, Double>?,
        cityIds: List<Int>?,
        offersShipping: Boolean?,
        isHotDeal: Boolean?,
        isUrgent: Boolean?,
        numericParams: List<Pair<String, Double>>?,
        singleTextParam: List<Pair<String, String>>?,
        singleBooleanParam: List<Pair<String, Boolean>>?,
        textParams: List<Pair<String, List<String>>>?,
        rangeParams: List<RangeParam>?
    ): Resource<Int, NetworkAndBackendErrors> {
        val paramsFilter = ParamsFilter(
            singleMatch = SingleMatch(
                text = singleTextParam?.map { param -> SingleTextParam(param.first, param.second) },
                numeric = numericParams?.map { param -> NumericParam(param.first, param.second) },
                boolean = singleBooleanParam?.map { param ->
                    BooleanParam(
                        param.first,
                        param.second
                    )
                }
            ),
            listMatch = textParams?.let {
                ListMatch(it.map { param -> TextParam(param.first, param.second) })
            },
            rangeMatch = rangeParams
        )
        val filters = ListingAdsSearchFilters(
            seller = Optional.present(
                ListingSellerFilter(
                    storeId = Optional.present(storeId)
                )
            ),
            ad = Optional.presentIfNotNull(
                ListingAdFilter(
                    text = Optional.presentIfNotNull(text),
                    categoryId = Optional.present(categoryId),
                    hasImage = Optional.presentIfNotNull(hasImage),
                    hasPrice = Optional.presentIfNotNull(hasPrice),
                    type = Optional.absent(),
                    price = Optional.presentIfNotNull(priceRange.toRangeFilter()),
                    location = Optional.absent(),
                    offersShipping = Optional.presentIfNotNull(offersShipping),
                    isHotDeal = Optional.presentIfNotNull(isHotDeal),
                    isUrgent = Optional.presentIfNotNull(isUrgent),
                    isVerifiedSeller = Optional.absent(),
                    isEcommerce = Optional.absent(),
                    isImmoneuf = Optional.absent(),
                    isPremium = Optional.absent(),
                    params = Optional.presentIfNotNull(
                        ListingAdParamsFilters(
                            listMatch = Optional.presentIfNotNull(paramsFilter.listMatch.toAdParamsListMatchFilters()),
                            singleMatch = Optional.presentIfNotNull(paramsFilter.singleMatch.toAdParamsSingleMatchFilters()),
                            rangeMatch = Optional.presentIfNotNull(paramsFilter.rangeMatch.toAdParamsRangeFilterList())
                        )
                    )
                )
            )
        )
        return wrapWithErrorHandling(
            call = {
                apolloClient.query(
                    GetListingAdByStoreIdCountQuery(
                        filters = Optional.present(filters),
                        sortOrder = SortOrder.DESC,
                        adProperty = AdSortProperty.PRICE,
                        page = Page(number = 1, size = 35)
                    )
                ).execute()
            },
            toData = { apolloResponse -> apolloResponse.data?.getListingAds },
            toDomainModel = { data -> data.count.total },
        )
    }

    override suspend fun getStoreDetails(storeId: String): Resource<StoreProfileInfo, NetworkAndBackendErrors> {
        return wrapWithErrorHandling(
            call = {
                apolloClient.query(GetStoreDetailsQuery(storeId = storeId)).execute()
            },
            toData = { apolloResponse -> apolloResponse.data?.getStoreDetails },
            toDomainModel = { data -> data.onStoreProfile.toStoreProfile() }
        )
    }
}

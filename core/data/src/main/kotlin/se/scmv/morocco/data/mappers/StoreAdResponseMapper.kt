package se.scmv.morocco.data.mappers

import se.scmv.morocco.GetStoreDetailsQuery
import se.scmv.morocco.domain.models.StoreProfileInfo

fun GetStoreDetailsQuery.OnStoreProfile.toStoreProfile(): StoreProfileInfo {
    return StoreProfileInfo(
        id = storeId,
        name = name,
        isVerifiedSeller = isVerifiedSeller,
        storeLogo = logo?.defaultPath ?: "",
        lastActiveAdsImages = latestActiveAdsImages.map {
            it?.paths?.standard ?: ""
        },
        adFiltersAllowed = adFiltersAllowed,
        displayLabel = displayLabel,
        shortDescription = description.short ?: "",
        longDescription = description.long ?: "",
        registrationDate = registrationDay,
        cityName = location.city?.name ?: "",
        address = location.address ?: "",
        storeWebsite = website ?: "",
        phoneNumber = phone.number ?: "",
        categoryName = category.name,
        categoryId = category.id,
        categoryTracking = category.trackingValue,
        numberOfActiveAds = this.numberOfActiveAds
    )
}



package se.scmv.morocco.data.database.daos

import com.google.gson.annotations.SerializedName

data class CarCheckConfigDto(
    @SerializedName("car_check_config")
    val carCheckConfig: CarCheckConfigDetails
)

data class CarCheckConfigDetails(
    @SerializedName("is_visible")
    val isVisible: <PERSON>olean,

    @SerializedName("category_type_conditions")
    val categoryTypeConditions: List<CategoryTypeConditionDto>,

    @SerializedName("service_available_cities_keys")
    val serviceAvailableCitiesKeys: List<String>,

    @SerializedName("car_to_check")
    val carToCheck: CarToCheckDto,

    @SerializedName("car_checked")
    val carChecked: CarCheckedDto
)

data class CategoryTypeConditionDto(
    val category: String,
    val type: String
)

data class CarToCheckDto(
    val locales: Map<String, LocaleContentDto>,
    @SerializedName("inspection_request_form")
    val inspectionRequestForm: InspectionRequestFormDto
)

data class CarCheckedDto(
    val locales: Map<String, CarCheckedLocaleContentDto>
)

data class CarCheckedLocaleContentDto(
    @SerializedName("title")
    val title: String,

    @SerializedName("description")
    val description: String,

    @SerializedName("tags")
    val tags: List<String>,

    @SerializedName("button_text")
    val buttonText: String,

    @SerializedName("date_label")
    val dateLabel: String? = null,

    @SerializedName("badge_text")
    val badgeText: String? = null,

    @SerializedName("report_button_text")
    val reportButtonText: String? = null,

    @SerializedName("download_button_text")
    val downloadButtonText: String? = null,

    @SerializedName("redirect_url")
    val redirectUrl: String? = null
)


data class LocaleContentDto(
    @SerializedName("title")
    val title: String,

    @SerializedName("description")
    val description: String,

    @SerializedName("tags")
    val tags: List<String>,

    @SerializedName("button_text")
    val buttonText: String
)

data class InspectionRequestFormDto(
    val locales: Map<String, LocaleFormContentDto>
)

data class LocaleFormContentDto(
    val title: String,

    @SerializedName("service_available_cities")
    val serviceAvailableCities: String,


    @SerializedName("cta_button")
    val ctaButton: CtaButtonDto
)


data class CtaButtonDto(
    val text: String,
    @SerializedName("redirect_url")
    val redirectUrl: String
)

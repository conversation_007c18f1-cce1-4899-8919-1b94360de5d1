package se.scmv.morocco.data.mappers

import se.scmv.morocco.GetChatConversationByIdQuery
import se.scmv.morocco.GetConversationsListQuery
import se.scmv.morocco.domain.Conversation
import se.scmv.morocco.domain.Message
import java.time.Instant
import java.util.Date

/**
 * Mappers for messaging-related GraphQL responses to domain models
 */

fun GetConversationsListQuery.Conversation?.toConversation(): Conversation? {
    if (this == null) return null
    
    return Conversation(
        id = this.id.orEmpty(),
        partner = se.scmv.morocco.domain.PartnerProfile(
            name = if (this.partner?.__typename == "StoreProfile") {
                this.partner!!.onStoreProfile?.name.toString()
            } else {
                this.partner?.onPrivateProfile?.name.toString()
            },
            logo = if (this.partner?.__typename == "StoreProfile") {
                this.partner!!.onStoreProfile?.logo?.defaultPath.orEmpty()
            } else null
        ),
        ad = this.ad?.toAd(),
        lastMessage = this.lastMessage?.toMessage(),
        isBlockedByMe = this.isBlockedByMe ?: false,
        unreadCount = this.unreadCount ?: 0,
        messages = emptyList()
    )
}

fun GetConversationsListQuery.Ad?.toAd(): se.scmv.morocco.domain.Ad? {
    if (this == null) return null
    
    return se.scmv.morocco.domain.Ad(
        adId = this.adId,
        listId = this.listId,
        title = this.title,
        price = se.scmv.morocco.domain.Price(
            withCurrency = this.price?.withCurrency,
            withoutCurrency = this.price?.withoutCurrency
        ),
        media = this.media?.let {
            se.scmv.morocco.domain.Media(
                defaultImage = it.defaultImage?.let { image ->
                    se.scmv.morocco.domain.DefaultImage(
                        paths = se.scmv.morocco.domain.ImagePaths(
                            smallThumbnail = image.paths.smallThumbnail
                        )
                    )
                }
            )
        }
    )
}

fun GetConversationsListQuery.LastMessage?.toMessage(): se.scmv.morocco.domain.Message? {
    if (this == null) return null
    
    return se.scmv.morocco.domain.Message(
        id = this.id,
        text = this.text.orEmpty(),
        attachment = this.attachment?.let {
            se.scmv.morocco.domain.Attachment(
                filePath = it.url,
                previewUri = it.url,
                type = it.type
            )
        },
        isUnread = this.isUnread,
        isMine = this.isMine,
        time = parseDate(this.time),
    )
}

fun parseDate(dateString: String): Date {
    return try {
        Date.from(Instant.parse(dateString))
    } catch (e: Exception) {
        Date()
    }
}

fun GetChatConversationByIdQuery.Message?.toMessage(): Message? {
    if (this == null) return null
    
    return Message(
        id = this.id,
        text = this.text.orEmpty(),
        attachment = this.attachment?.let {
            se.scmv.morocco.domain.Attachment(
                filePath = it.url,
                previewUri = it.url,
                type = it.type
            )
        },
        isUnread = this.isUnread,
        isMine = this.isMine,
        time = parseDate(this.time),
    )
}

fun Date.toISOString(): String {
    return this.toInstant().toString()
}

query getStoreDetails(
    $storeId: String!
){
    getStoreDetails(
        query: {
            storeID: $storeId
        }
    ){
        ... on StoreProfile {
            storeId
            isVerifiedSeller
            numberOfActiveAds
            type
            name
            registrationDay
            logo {
                defaultPath
            }
            latestActiveAdsImages{
                paths{
                    standard
                }
            }
            website
            phone {
                number
                verified
            }
            description{
                long
                short
            }
            adFiltersAllowed
            displayLabel
            location {
                city{
                    id
                    name
                    trackingValue
                }
                address
            }
            category{
                id
                name
                trackingValue
                parent{
                    id
                    name
                    trackingValue
                }
            }
        }
    }
}
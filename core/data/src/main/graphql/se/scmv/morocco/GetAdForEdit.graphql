query getAdForEdit($adId: ID!) {
    getAdForEdit(adId: $adId) {
        category
        price
        type
        title
        description
        name
        phone
        phoneHidden
        location {
            city
            area
            address
        }
        params {
            __typename
            ... on AdEditNumericParam {
                id
                numericValue:numericValue
            }
            ... on AdEditBooleanParam {
                id
                booleanValue:booleanValue
            }
            ... on AdEditListKeyParam {
                id
                keyValue:keyValue
            }
        }
        isSifm
        media {
            images {
                id
                defaultPath
            }
            videos {
                id
                defaultPath
            }
        }
    }
}

query getMyStoreInfo($storeId: ID!) {
    getMyStoreInfo(storeId: $storeId){
        name
        email
        category {
            id
            name
            trackingValue
        }
        phones {
            number
        }
        description {
            short
            long
        }
        locations {
            city {
                id
                name
                trackingValue
            }
            address
        }
        startDate
        expiryDate
        logo {
            defaultPath
        }
        points {
            count
            expiryDate
        }
        website
        category {
            id
            name
        }
        offersDelivery
        extraMediaAllowed
        membership {
            id
            name
        }
        isVerifiedSeller
    }
}
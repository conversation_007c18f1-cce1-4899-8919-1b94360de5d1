# Write your query or mutation here
query AutoCompleteSearch($query: String!) {
    autoCompleteSearch( query: { text:$query } ){
        suggestions {
            suggestion
            category {
                id
                name
                trackingValue
            }
            city {
                id
                name
                trackingValue
            }
            adType {
                key
                name
            }
            model {
                ... on TextAdParam {
                    id
                    name
                    textValue
                    trackingValue
                    paramRawValue
                }
            }
            brand {
                ... on TextAdParam {
                    id
                    name
                    textValue
                    trackingValue
                    paramRawValue
                }
            }
            phoneModel {
                ... on TextAdParam {
                    id
                    name
                    textValue
                    trackingValue
                    paramRawValue
                }
            }
            phoneBrand {
                ... on TextAdParam {
                    id
                    name
                    textValue
                    trackingValue
                    paramRawValue
                }
            }
        }
    }
}
query GetListingAdsCount(
    $adFilter: ListingAdFilter,
    $includeNewConstructionAds: <PERSON><PERSON><PERSON>,
    $extendPublishedAdsSearchIfNeeded: Boolean,
    $isStore:Boolean
) {
    getListingAds(
        query: {
            filters: {
                ad: $adFilter
                seller: {
                    isStore: $isStore
                }
                extension : {
                    includeNewConstructionAds : $includeNewConstructionAds
                    extendPublishedAdsSearchIfNeeded: $extendPublishedAdsSearchIfNeeded
                }
            }
            page: {
                size: 1
                number: 1
            }
        }
    ) {
        count {
            total
        }
    }
}
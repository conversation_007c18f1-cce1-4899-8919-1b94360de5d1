query GetConversationsList($size: Int!, $beforeTime : String = null, $afterTime : String = null){
    getMyChat(
        query: {size: $size, beforeTime: $beforeTime, afterTime: $afterTime}
    ){
        conversations {
            id
            partner{
                __typename
                ... on PrivateProfile{
                    name
                }
                ... on StoreProfile{
                    name
                    logo{
                        defaultPath
                    }
                }
            }
            ad {
                adId
                price {
                    withCurrency
                    withoutCurrency
                }
                listId
                title
                media{
                    defaultImage{
                        paths{
                            smallThumbnail
                        }
                    }
                }
            }
            lastMessage {
                id
                text
                attachment {
                    type
                    url
                }
                isUnread
                isMine
                time
            }
            isBlockedByMe
            unreadCount
        }
        unreadCount
    }
}
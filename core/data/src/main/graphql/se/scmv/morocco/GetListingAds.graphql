query GetListingAds(
    $text: String,
    $categoryId: Int,
    $adType: AdType<PERSON><PERSON>,
    $hasImage: Boolean,
    $hasPrice: <PERSON><PERSON>an,
    $price: RangeFilter,
    $location: AdLocationFilter,
    $offersShipping: <PERSON>olean,
    $offersShippingWithinCity : Boolean
    $isHotDeal: <PERSON><PERSON><PERSON>,
    $isUrgent: <PERSON>olean,
    $isEcommerce: <PERSON>olean,
    $isImmoneuf: Boolean,
    $isVerifiedSeller: Boolean,
    $params: ListingAdParamsFilters,
    $includeNewConstructionAds: <PERSON><PERSON><PERSON>,
    $extendPublishedAdsSearchIfNeeded: <PERSON>olean,
    $startNewScroll: Boolean!,
    $publishedAndNCAdsNextScrollId: String,
    $premiumAdsLatestScrollId: String,
    $sort: [AdsSearchResultSort],
    $isStore: Boolean
){
    publishedAndNC: getListingAds(
        query: {
            filters: {
                ad: {
                    text: $text
                    categoryId: $categoryId
                    type: $adType
                    hasImage: $hasImage
                    hasPrice: $hasPrice
                    price: $price
                    location: $location
                    offersShipping: $offersShipping
                    offersShippingWithinCity: $offersShippingWithinCity
                    isHotDeal: $isHotDeal
                    isUrgent: $isUrgent
                    isEcommerce: $isEcommerce
                    isImmoneuf: $isImmoneuf
                    isVerifiedSeller: $isVerifiedSeller
                    params: $params
                    isPremium: false
                }
                seller: {
                    isStore: $isStore
                }
                extension : {
                    includeNewConstructionAds : $includeNewConstructionAds
                    extendPublishedAdsSearchIfNeeded: $extendPublishedAdsSearchIfNeeded
                }
            }
            sort: $sort
            page: {
                size: 35
                number: 0
            }
            metadata: {
                startNewScroll: $startNewScroll
                latestScrollID: $publishedAndNCAdsNextScrollId
            }
        }
    ){
        publishedAndNCAds: ads {
            publishedAndNCDetails: details {
                ... on PublishedAd {
                    ...PublishedAdFragment
                }
                ... on NewConstructionAd{
                    title
                    price {
                        withCurrency
                        withoutCurrency
                    }
                    location {
                        city {
                            id
                            name
                            trackingValue
                        }
                        area {
                            id
                            name
                            trackingValue
                        }
                        address
                    }
                    media {
                        defaultImage {
                            defaultPath
                        }
                        mediaCount
                    }
                    externalLink
                    rooms
                    bathrooms
                    size
                }
            }
            searchExtension {
                extensionType
            }
            publishedAndNCMetadata: metadata {
                nextScrollId
            }
        }
        publishedAndNCAdsCount: count {
            total
        }
    }
    premium: getListingAds(
        query: {
            filters: {
                ad: {
                    text: $text
                    categoryId: $categoryId
                    type: $adType
                    hasImage: $hasImage
                    hasPrice: $hasPrice
                    price: $price
                    location: $location
                    offersShipping: $offersShipping
                    offersShippingWithinCity: $offersShippingWithinCity
                    isHotDeal: $isHotDeal
                    isUrgent: $isUrgent
                    isEcommerce: $isEcommerce
                    isImmoneuf: $isImmoneuf
                    isVerifiedSeller: $isVerifiedSeller
                    params: $params
                    isPremium: true
                }
                seller: {
                    isStore: $isStore
                }
                extension : {
                    extendPublishedAdsSearchIfNeeded: $extendPublishedAdsSearchIfNeeded
                }
            }
            sort: $sort
            page: {
                size: 3
                number: 0
            }
            metadata: {
                startNewScroll: $startNewScroll
                latestScrollID: $premiumAdsLatestScrollId
            }
        }
    ){
        premiumAds: ads {
            premiumDetails: details {
                ... on PublishedAd{
                    ...PublishedAdFragment
                }
            }
            premiumMetadata: metadata {
                nextScrollId
            }
        }
    }
}
query GetListingAdsCountTwo(
    $categoryId: Int!,
    $hasImage: Boolean!,
    $hasPrice: Boolean!,
    $text: String,
    $type: AdTypeKey,
    $price: RangeFilter,
    $location: AdLocationFilter,
    $offersShipping: <PERSON><PERSON>an,
    $offersShippingWithinCity : Boolean
    $isHotDeal: <PERSON>olean,
    $isUrgent: Boolean,
    $isEcommerce: Boolean,
    $isImmoneuf: Boolean,
    $isPremium: Boolean,
    $isVerifiedSeller: Boolean,
    $isStore: Boolean,
    $params: ListingAdParamsFilters,
) {
    getListingAds(
        query: {
            filters: {
                ad: {
                    text: $text
                    categoryId: $categoryId
                    hasImage: $hasImage
                    hasPrice: $hasPrice
                    type: $type
                    price: $price
                    location: $location
                    offersShipping: $offersShipping
                    offersShippingWithinCity: $offersShippingWithinCity
                    isHotDeal: $isHotDeal
                    isUrgent: $isUrgent
                    isEcommerce: $isEcommerce
                    isImmoneuf: $isImmoneuf
                    isPremium: $isPremium
                    isVerifiedSeller: $isVerifiedSeller
                    params: $params
                }
                seller: {
                    isStore: $isStore
                }
            }
            page: {
                size: 1
                number: 1
            }
        }
    ) {
        count {
            total
        }
    }
}
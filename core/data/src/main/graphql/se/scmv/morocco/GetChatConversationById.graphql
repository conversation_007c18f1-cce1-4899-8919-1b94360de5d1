query GetChatConversationById($id: ID!,$size: Int!, $beforeTime : String = null, $afterTime : String = null){
    getMyChatConversation(query:{id : $id, size: $size, beforeTime: $beforeTime,afterTime: $afterTime}){
        id
        partner{
            __typename
            ... on PrivateProfile{
                name
            }
            ... on StoreProfile{
                name
                logo{
                    defaultPath
                }
            }
        }
        ad {
            adId
            title
            price {
                withoutCurrency
                withCurrency
            }
            listId
            price {
                withCurrency
            }
            media{
                defaultImage{
                    paths{
                        smallThumbnail
                    }
                }
            }
        }
        messages {
            id
            text
            attachment {
                url
                type
            }
            isUnread
            isMine
            time
        }
        isBlockedByMe
        unreadCount
    }
}
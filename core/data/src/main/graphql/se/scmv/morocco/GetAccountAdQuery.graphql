query GetAccountAdQuery($adId: ID!) {
    getMyAd(adId: $adId){
        media {
            images {
                paths {
                    standard
                }
            }
        }
        title
        location {
            city {
                id
                name
                trackingValue
            }
            area {
                id
                name
                trackingValue
            }
        }
        description
        category {
            id
            name
            trackingValue
        }
        params {
            primary {
                ... adParam
            }
            secondary {
                ... adParam
            }
        }
    }
}
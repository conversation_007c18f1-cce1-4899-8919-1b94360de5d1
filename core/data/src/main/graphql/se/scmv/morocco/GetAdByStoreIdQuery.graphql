query getListingAdByStoreId(
    $filters: ListingAdsSearchFilters,
    $sortOrder: SortOrder!,
    $adProperty: AdSortProperty!,
    $page: Page!
){
    getListingAds(
        query: {
            filters: $filters
            sort: {
                adProperty: $adProperty
                sortOrder: $sortOrder
            }
            page: $page
        }
    ){
        # FIXME We don't need to query the count !!
        count {
            total
        }
        ads {
            details {
                ... on PublishedAd {
                    ...PublishedAdFragment
                }
            }
            searchExtension {
                extensionType
            }
            metadata {
                nextScrollId
            }
        }
    }}
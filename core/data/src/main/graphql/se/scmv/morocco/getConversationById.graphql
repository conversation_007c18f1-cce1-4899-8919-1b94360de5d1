query GetConversationById($id: ID!){
    getMyChatConversation(query:{id : $id, size: 1}){
        id
        partner{
            __typename
            ... on PrivateProfile{
                name
            }
            ... on StoreProfile{
                name
                logo{
                    defaultPath
                }
            }
        }
        ad {
            adId
            listId
            title
            price {
                withCurrency
            }
            media{
                defaultImage{
                    paths{
                        smallThumbnail
                    }
                }
            }
        }
        lastMessage {
            id
            text
            attachment {
                url
                type
            }
            isUnread
            isMine
            time
        }
        isBlockedByMe
        unreadCount
    }
}
query GetAdLimitationsQuery($request: CheckAdLimitationRepo!) {
    checkUserAdLimitationStatus(ad: $request) {
        userCategoryAdCount,
        configuredCategoryFreeLimit,
        configuredCategoryStoreLimit,
        listingFee,
        localizedListingFee,
        categoryFree {
            id
            name
            trackingValue
        },
        categoryStore {
            id
            name
            trackingValue
        }
    }
}
query GetAccountOrdersQuery ($query: MyPurchaseOrdersQuery!){
    getMyPurchaseOrders(query: $query) {
        orders {
            id
            product {
                listId
                sku
                name
                thumbnail
            }
            unitsPurchased
            status
            date
            total
            paymentMethod
        }
        count {
            total
        }
    }
}
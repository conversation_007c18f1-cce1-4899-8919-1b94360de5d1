query GetPublishedAd($listID: String!){
    getPublishedAd(
        query: {listId: $listID}
    ) {
        ad {
            adId
            listId
            listTime
            sellerType
            title
            description
            reservedDays
            category {
                id
                name
                trackingValue
                parent {
                    id
                    name
                    trackingValue
                    parent {
                        id
                        name
                        trackingValue
                    }
                }
            }
            type {
                name
                key
            }
            ...PublishedAdPrice
            discount
            media {
                mediaCount
                defaultImage {
                    paths {
                        smallThumbnail
                        largeThumbnail
                        standard
                        fullHd
                    }
                }
                media {
                    images {
                        paths {
                            smallThumbnail
                            largeThumbnail
                            standard
                            fullHd
                        }
                    }
                    videos {
                        defaultPath
                    }
                }
            }
            location {
                city {
                    id
                    name
                    trackingValue
                }
                area {
                    id
                    name
                    trackingValue
                }
            }
            seller {
                __typename
                ... on PrivateProfile {
                    accountId
                    name
                    registrationDay
                    phone{
                        number
                        verified
                    }
                }
                ... on StoreProfile {
                    storeId
                    name
                    registrationDay
                    isVerifiedSeller
                    location{
                        address
                    }
                    phone{
                        number
                        verified
                    }
                    logo {
                        defaultPath
                    }
                    website
                    type
                    numberOfActiveAds
                }
            }
            ...PublishedAdPrice
            ...PublishedAdParam
            isHighlighted
            isInMyFavorites
            offersShipping
            isEcommerce
            isUrgent
            isHotDeal
            price {
                withCurrency
                withoutCurrency
            }
            oldPrice {
                withCurrency
                withoutCurrency
            }
        }
        similarAds{
            adId
            listId
            title
            listTime
            sellerType
            isInMyFavorites
            isUrgent
            isHotDeal
            discount
            price{
                withCurrency
                withoutCurrency
            }
            oldPrice {
                withoutCurrency
                withCurrency
            }
            category{
                id
                name
            }
            location{
                city{
                    id
                    name
                }
                area {
                    id
                    name
                }
            }
            media{
                defaultImage{
                    paths{
                        largeThumbnail
                        smallThumbnail
                        standard
                    }
                }
                mediaCount
            }
            ...PublishedAdPrice
            ...PublishedAdParam
        }
    }
}
package se.scmv.morocco.common.lang

import androidx.appcompat.app.AppCompatDelegate
import androidx.core.os.LocaleListCompat
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow

// TODO Rename isAr and isFr to isCurrentLanguageFrench and isCurrentLanguageFrench when working on language switching
object LocaleManager {

    private const val LANG_AR = "ar"
    private const val LANG_FR = "fr"

    private val _languageChangeEvent = MutableSharedFlow<Unit>()
    val languageChangeEvent = _languageChangeEvent.asSharedFlow()

    @JvmStatic
    suspend fun changeToArabic() {
        updateResources(LANG_AR)
    }

    // In our case the default lang is en but it contains fr strings.
    @JvmStatic
    suspend fun changeToFrench() {
        updateResources(LANG_FR)
    }

    @JvmStatic
    suspend fun switchLanguage() {
        if (isFr()) {
            changeToArabic()
        } else {
            changeToFrench()
        }
    }

    @JvmStatic
    private suspend fun updateResources(tag: String) {
        val appLocale: LocaleListCompat = LocaleListCompat.forLanguageTags(tag)
        // Call this on the main thread as it may require Activity.restart()
        AppCompatDelegate.setApplicationLocales(appLocale)
        _languageChangeEvent.emit(Unit)
    }

    @JvmStatic
    fun getCurrentLanguage(): String {
        return AppCompatDelegate.getApplicationLocales().get(0)?.language ?: LANG_FR
    }

    @JvmStatic
    fun getInvertedCurrentLanguage(): String {
        val currentLanguage = AppCompatDelegate.getApplicationLocales().get(0)?.language
        return if (currentLanguage == LANG_FR) LANG_AR else LANG_FR
    }

    @JvmStatic
    fun isFr() = getCurrentLanguage() == LANG_FR

    @JvmStatic
    fun isAr() = getCurrentLanguage() == LANG_AR
}
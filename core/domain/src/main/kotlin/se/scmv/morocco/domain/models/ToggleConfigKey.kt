package se.scmv.morocco.domain.models

sealed class ToggleConfig<PERSON>ey{
    data class HasPrice(val key: String = "hasPrice", val value: Boolean = false): ToggleConfigKey()
    data class HasImage(val key: String = "hasImage", val value: <PERSON>olean = false): ToggleConfigKey()
    data class IsHotDeal(val key: String = "isHotDeal", val value: <PERSON>olean = false): ToggleConfigKey()
    data class IsUrgent (val key: String = "isUrgent", val value: Boolean = false): ToggleConfigKey()
    data class HasDelivery (val key: String = "delivery", val value: Boolean = false): ToggleConfigKey()
    data class OfferShippingWithInCity(val key: String = "offers_shipping_within_city", val value: Boolean = false, val defaultValue: Boolean = false): ToggleConfigKey()

    companion object {
        fun getFromString(key: String): ToggleConfigKey?{
            return when(key){
                "has_price" -> HasPrice()
                "has_image" -> HasImage()
                "is_hotDeal" -> IsHotDeal()
                "is_urgent" -> IsUrgent()
                "delivery" -> HasDelivery()
                "offers_shipping_within_city" -> OfferShippingWithInCity()
                else -> {
                    null
                }
            }
        }
    }
}
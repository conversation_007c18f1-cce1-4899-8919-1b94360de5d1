package se.scmv.morocco.domain.repositories

import androidx.paging.PagingData
import kotlinx.coroutines.flow.Flow
import se.scmv.morocco.domain.models.Account
import se.scmv.morocco.domain.models.AccountOrder
import se.scmv.morocco.domain.models.AccountOrderStatus
import se.scmv.morocco.domain.models.AccountStatisticsMetric
import se.scmv.morocco.domain.models.AccountStatisticsRange
import se.scmv.morocco.domain.models.BookmarkedSearch
import se.scmv.morocco.domain.models.BookmarkedSearchQuery
import se.scmv.morocco.domain.models.ListingAd
import se.scmv.morocco.domain.models.NetworkAndBackendErrors
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.models.SearchSuggestion

interface AccountRepository {
    val currentAccount: Flow<Account>

    suspend fun updatePrivateAccount(
        name: String,
        email: String?,
        phone: String,
        phoneVisibility: Boolean,
        cityId: String
    ): Resource<Unit, NetworkAndBackendErrors>

    suspend fun updateShopAccount(
        accountId: String,
        name: String,
        website: String,
        shortDescription: String,
        longDescription: String,
        address: String,
        phones: List<String>
    ): Resource<Unit, NetworkAndBackendErrors>

    fun getAccountOrders(orderStatus: AccountOrderStatus): Flow<PagingData<AccountOrder>>

    suspend fun cancelOrder(id: String): Resource<Unit, NetworkAndBackendErrors>

    suspend fun getStatistics(
        range: AccountStatisticsRange
    ): Resource<List<AccountStatisticsMetric>, NetworkAndBackendErrors>

    fun getBookmarkedSearches(): Flow<Resource<List<BookmarkedSearch>, NetworkAndBackendErrors>>

    suspend fun bookmarkSearch(query: String): Resource<BookmarkedSearchQuery, NetworkAndBackendErrors>

    suspend fun unBookmarkSearch(savedSearch: BookmarkedSearchQuery): Resource<Unit, NetworkAndBackendErrors>

    fun getBookmarkedAds(): Flow<PagingData<ListingAd.Published>>

    suspend fun bookmarkAd(listId: String): Resource<Unit, NetworkAndBackendErrors>

    suspend fun unBookmarkAd(listId: String): Resource<Unit, NetworkAndBackendErrors>

    suspend fun getBookmarkedSearchSuggestions(): Flow<List<SearchSuggestion>>

    suspend fun bookmarkSearchSuggestion(suggestion: SearchSuggestion): Boolean

    suspend fun unbookmarkSearchSuggestion(suggestion: SearchSuggestion): Boolean

    suspend fun refreshAccountFromRemote(): Resource<Unit, NetworkAndBackendErrors>
}
package se.scmv.morocco.domain.models


data class CarCheckConfig(
    val carToCheck: CarToCheck,
    val carChecked: CarChecked
)

data class CarToCheck(
    val locales: Map<String, LocaleContent>,
    val inspectionRequestForm: InspectionRequestForm
)

data class CarChecked(
    val locales: Map<String, CarCheckedLocaleContent>
)

data class LocaleContent(
    val title: String,
    val description: String,
    val tags: List<String>,
    val buttonText: String
)

data class CarCheckedLocaleContent(
    val title: String,
    val description: String,
    val tags: List<String>,
    val buttonText: String,
    val dateLabel: String?,
    val badgeText: String?,
    val reportButtonText: String?,
    val downloadButtonText: String?,
    val redirectUrl: String?
)

data class InspectionRequestForm(
    val locales: Map<String, LocaleFormContent>
)

data class LocaleFormContent(
    val title: String,
    val serviceAvailableCities: String,
    val ctaButton: CtaButton
)


data class CtaButton(
    val text: String,
    val redirectUrl: String
)

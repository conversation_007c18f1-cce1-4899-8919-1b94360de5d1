//package se.scmv.morocco.domain.models
//
//sealed interface AdViewUiEvents {
//    data class OnRefreshBtnClicked(val listId: String) : AdViewUiEvents
//    data class OnTouchingPointClicked(val touchingPoint: AdTouchingPoint) : AdViewUiEvents
//    data class OnSimilarAdFavoriteBtnClicked(val similarAd: AdDetails.SimilarAd) : AdViewUiEvents
//    data object OnFavoriteBtnClicked : AdViewUiEvents
//    data object OnMessagingBtnClicked : AdViewUiEvents
//    data object OnShopBtnClicked : AdViewUiEvents
//    data class UserConnected(val loginType: LoginType, val token: String) : AdViewUiEvents
//}
//enum class LoginType {
//    EMAIL,
//    PHONE,
//    FACEBOOK,
//    GMAIL
//}
package se.scmv.morocco.domain.repositories

import se.scmv.morocco.domain.models.AdDetails
import se.scmv.morocco.domain.models.AdTouchingPoint
import se.scmv.morocco.domain.models.CachedAdDetails
import se.scmv.morocco.domain.models.ChatConversationResult
import se.scmv.morocco.domain.models.FirstChatMessageResult
import se.scmv.morocco.domain.models.NetworkAndBackendErrors
import se.scmv.morocco.domain.models.Resource

interface AdViewRepository {
    suspend fun getAdDetails(listId: String): Resource<AdDetails, NetworkAndBackendErrors>

    suspend fun getAdTouchingPoint(): Resource<List<AdTouchingPoint>, NetworkAndBackendErrors>
    suspend fun sendCarInspectionLead(
        resellerId: String,
        campaignId: String,
        name: String,
        phone: String,
        option1: String,
        option2: Boolean
    ): Resource<Unit, NetworkAndBackendErrors>

    suspend fun getCachedAdDetails(adId: String): CachedAdDetails?
    suspend fun saveCachedAdDetails(adId: String, cachedAdDetails: CachedAdDetails)

    suspend fun recordTouchingPoint(
        campaignId: String,
        clickOrImpression: String
    ): Resource<Boolean, NetworkAndBackendErrors>

    suspend fun getConversationIdWithAd(adId: String): Resource<ChatConversationResult, NetworkAndBackendErrors>

    suspend fun sendFirstMessageToSeller(
        adId: String,
        text: String
    ): Resource<FirstChatMessageResult, NetworkAndBackendErrors>


    suspend fun reportAd(
        adListId: String,
        email: String,
        reason: String,
        message: String
    ): Resource<Unit, NetworkAndBackendErrors>
}

package se.scmv.morocco.domain.models.orion

import androidx.compose.runtime.Stable

// COMPONENTS
@Stable
data class OrionVas(
    override val baseData: OrionBaseComponentData,
) : OrionBaseComponent

// VALUES
@Stable
data class OrionVasValue(
    override val id: String,
    val packageId: String,
    val exeDay: String?,
    val exeTimeId: String?,
) : OrionBaseComponentValue {
    override fun stringValue(): String = toString()
}
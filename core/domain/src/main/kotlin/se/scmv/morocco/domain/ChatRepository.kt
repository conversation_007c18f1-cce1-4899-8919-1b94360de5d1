package se.scmv.morocco.domain

import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import kotlinx.coroutines.flow.Flow
import java.util.Date

interface ChatRepository {
    // Paging3 methods
    fun getConversationsPaging(): Flow<PagingData<Conversation>>
    
    fun getMessagesPaging(conversationId: String): Flow<PagingData<Message>>

    // Legacy methods for backward compatibility
    suspend fun getUnreadCount(): Flow<Result<Int>>

    suspend fun getChatConversation(
        id: String,
        pageSize: Int,
        beforeTime: Date? = null,
        afterTime: Date? = null
    ): Flow<Result<Conversation>>

    suspend fun sendMessage(
        conversationId: String,
        text: String,
        attachment: Attachment? = null
    ): Flow<Result<Message>>

    suspend fun getMessages(
        conversationId: String,
        pageSize: Int = 20,
        beforeTime: Date? = null,
        afterTime: Date? = null
    ): Flow<Result<List<Message>>>

    suspend fun blockConversation(id: String): Flow<Result<Boolean>>

    suspend fun unblockConversation(id: String): Flow<Result<Boolean>>

    suspend fun clearConversation(id: String): Flow<Result<Boolean>>

    suspend fun markConversationAsRead(id: String): Flow<Result<Boolean>>

    suspend fun subscribeToChat(): Flow<RealtimeChatEvent>
}
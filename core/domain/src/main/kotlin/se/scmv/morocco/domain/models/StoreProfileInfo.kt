package se.scmv.morocco.domain.models


data class StoreProfileInfo(
    val name: String = "",
    val id: String = "",
    val isVerifiedSeller: Boolean = false,
    val storeLogo: String? = null,
    val lastActiveAdsImages: List<String?> = emptyList(),
    val adFiltersAllowed: Boolean = false,
    val displayLabel: Boolean = false,
    val shortDescription: String = "",
    val longDescription: String = "",
    val registrationDate: String = "",
    val cityName: String = "",
    val cityId: String = "",
    val cityTrackingValue: String = "",
    val address: String = "",
    val storeWebsite: String = "",
    val phoneNumber: String = "",
    val categoryId: String = "",
    val categoryName: String = "",
    val categoryTracking: String = "",
    val adType: String = "",
    val count: Int = 0,
    val numberOfActiveAds: Int = 0
)


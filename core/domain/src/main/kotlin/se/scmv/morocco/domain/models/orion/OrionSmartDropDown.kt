package se.scmv.morocco.domain.models.orion

import androidx.compose.runtime.Stable

// COMPONENTS
@Stable
data class OrionSmartDropDown(
    override val baseData: OrionBaseComponentData,
    val childBaseData: OrionSmartDropDownChildData,
    val items: List<OrionSmartDropDownItem>,
    val vasAction: VasAction? = null
): OrionBaseComponent {

    @Stable
    enum class VasAction {
        REFRESH_VAS_PACKAGES_BY_CITY_AREA, REFRESH_VAS_PACKAGES_BY_BRAND
    }
}

@Stable
data class OrionMultipleSelectSmartDropDown(
    override val baseData: OrionBaseComponentData,
    val childBaseData: OrionSmartDropDownChildData,
    val items: List<OrionSmartDropDownItem>,
): OrionBaseComponent

@Stable
data class OrionSmartDropDownItem(
    val id: String,
    val name: String,
    val trackingName: String,
    val children: List<OrionSmartDropDownItem>
)

@Stable
data class OrionSmartDropDownChildData(
    val id: String,
    val title: String,
    val required: Boolean
)

// VALUES
@Stable
data class OrionSmartDropDownValue(
    override val id: String,
    val childId: String,
    val parent: OrionSmartDropDownItem,
    val child: OrionSmartDropDownItem?
) : OrionBaseComponentValue {
    override fun stringValue(): String = this.toString()
}

@Stable
data class OrionMultipleSelectSmartDropDownValue(
    override val id: String,
    val childId: String,
    val parents: List<OrionSmartDropDownItem>,
    val children: List<OrionSmartDropDownItem>,
) : OrionBaseComponentValue {
    override fun stringValue(): String = this.toString()
}


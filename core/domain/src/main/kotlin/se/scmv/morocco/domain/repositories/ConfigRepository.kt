package se.scmv.morocco.domain.repositories

import se.scmv.morocco.domain.models.AdInsertStep
import se.scmv.morocco.domain.models.AdTypeKey
import se.scmv.morocco.domain.models.BankApp
import se.scmv.morocco.domain.models.CarCheckConfig
import se.scmv.morocco.domain.models.Category
import se.scmv.morocco.domain.models.CategoryTree
import se.scmv.morocco.domain.models.City
import se.scmv.morocco.domain.models.LoanSimulatorConfig
import se.scmv.morocco.domain.models.NetworkAndBackendErrors
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.models.SearchSuggestion
import se.scmv.morocco.domain.models.filter.ListingCategoryFilters
import se.scmv.morocco.domain.models.filter.ListingFilterCategories
import se.scmv.morocco.domain.models.orion.OrionBaseComponent
import se.scmv.morocco.domain.models.orion.OrionBaseComponentValue

/**
 * Interface for retrieving the config/static data we need in the app.
 * Provides methods for retrieving cities, ad insertion steps, categories, and filters for listings.
 */
interface ConfigRepository {

    suspend fun getCities(): List<City>

    suspend fun getCityWithArea(cityId: String, areaId: String?): City?

    suspend fun getFilters(
        categoryId: String,
        type: String
    ): ListingCategoryFilters.Filters

    suspend fun getFiltersCategories(): Resource<List<CategoryTree>, NetworkAndBackendErrors>

    suspend fun getFiltersCategories(categoryId: String): ListingFilterCategories?

    suspend fun getFilters(
        categoryId: String,
        adTypeKey: AdTypeKey
    ): Resource<List<OrionBaseComponent>, NetworkAndBackendErrors>

    suspend fun getAdInsertCategories(): Resource<List<CategoryTree>, NetworkAndBackendErrors>

    suspend fun getAdInsertSteps(
        categoryId: String,
        adTypeKey: AdTypeKey
    ): Resource<List<AdInsertStep>, NetworkAndBackendErrors>

    suspend fun refresh()

    suspend fun getCategory(id: String): Resource<Category, NetworkAndBackendErrors>

    suspend fun getSupportedBankApps(): Resource<List<BankApp>, NetworkAndBackendErrors>

    suspend fun getListingCategories(): Resource<List<String>, NetworkAndBackendErrors>

    suspend fun getLoanSimulatorConfig(adCategoryId: String, type: String?): LoanSimulatorConfig?

    suspend fun getCarCheckConfig(
        adCategoryId: String,
        type: String?,
        cityId: String?
    ): CarCheckConfig?

    suspend fun getSearchSuggestions(
        query: String
    ): Resource<List<SearchSuggestion>, NetworkAndBackendErrors>

    suspend fun buildFiltersValuesFor(
        searchSuggestion: SearchSuggestion
    ): List<OrionBaseComponentValue>

    suspend fun buildBookmarkSearchQuery(
        filtersValues: List<OrionBaseComponentValue>
    ): String

    suspend fun buildFiltersValuesFor(query: String): List<OrionBaseComponentValue>
}
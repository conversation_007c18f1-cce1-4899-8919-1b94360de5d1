package se.scmv.morocco.domain.models

import se.scmv.morocco.analytics.models.AnalyticsEvent
import se.scmv.morocco.analytics.models.Param

data class City(
    val id: String,
    val name: String,
    val trackingName: String,
    val area: Area? = null,
    val address: String? = null,
) {
    fun getAnalyticsParams(): Set<Param> {
        val params = mutableSetOf(
            Param(key = AnalyticsEvent.ParamKeys.CITY_ID, value = id),
            Param(key = AnalyticsEvent.ParamKeys.CITY, value = trackingName)
        )

        if (area != null) {
            params.add(Param(key = AnalyticsEvent.ParamKeys.AREA_ID, value = area.id))
            params.add(Param(key = AnalyticsEvent.ParamKeys.AREA, value = area.trackingName))
        }
        return params
    }
}

data class Area(
    val id: String,
    val name: String,
    val trackingName: String,
)
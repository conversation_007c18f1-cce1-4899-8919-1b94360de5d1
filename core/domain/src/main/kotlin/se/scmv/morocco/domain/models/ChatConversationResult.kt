package se.scmv.morocco.domain.models

data class ChatConversationResult(
    val found: Boolean,
    val conversationId: String?
)

data class ChatMessage(
    val id: String,
    val text: String?,
    val isMine: Boolean,
    val time: String?,
    val attachment: MessageAttachment?
)

data class MessageAttachment(
    val url: String,
    val type: String
)

data class ChatConversation(
    val id: String,
    val message: ChatMessage?,
    val isNew: Boolean
)

data class FirstChatMessageResult(
    val success: Boolean,
    val conversation: ChatConversation?
)


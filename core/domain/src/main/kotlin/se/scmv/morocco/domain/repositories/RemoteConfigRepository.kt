package se.scmv.morocco.domain.repositories

interface RemoteConfigRepository {
    fun getString(key: String): String?
    fun getBoolean(key: String): Boolean

    fun getLong(key: String): Long

    companion object {
        const val TASSHILAT_AGENCIES_URL_KEY = "tasshilat_agencies_url"
        const val CASH_PLUS_AGENCIES_URL_KEY = "cash_plus_agencies_url"
        const val SUPPORTED_BANK_APPS_ANDROID_KEY = "supported_bank_apps_android"
        const val LOAN_SIMULATOR_CONFIG_KEY = "new_loan_simulator_android_config"
        const val FILTERS_WORKER_REPEAT_INTERVAL_BY_HOUR = "filters_worker_repeat_interval_by_hour"
        const val LISTING_CATEGORIES_IDS_CONFIG_KEY = "listing_categories_ids"
        const val CAR_CHECK_CONFIG_KEY = "car_check_config"
    }
}
package se.scmv.morocco.domain.usecases

import se.scmv.morocco.domain.models.AdTouchingPoint
import se.scmv.morocco.domain.models.Category
import se.scmv.morocco.domain.models.City
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.repositories.AdViewRepository
import javax.inject.Inject

class GetAndFilterTouchingPointsUseCase @Inject constructor(
    private val adViewRepository: AdViewRepository
) {

    /**
     * Fetch and filter Touching points.lòà
     *
     * After we get the list of touching points from the repo, we filter them in the following way.
     *
     * get the tp with 100% share of voice if exist
     * get the other available tps: Available for android platform, targeting ad category
     * or ad parent category and targeting ad city.
     * if tp with 100% sov exist than return it with a random one from the available tps
     * else get 2 random tps from the available tps if exist
     *
     * @param adCategory The category of the ad.
     * @param adCity The city of the ad.
     * @return List of 2 AdTouchingPoint based on the adCategory and adCity,
     * or an empty list if fetching fails.
     */

    suspend operator fun invoke(
        adCategory: Category,
        adCity: City?,
    ): List<AdTouchingPoint> {
        return when (val result = adViewRepository.getAdTouchingPoint()) {
            is Resource.Success -> {
                val fullShareOfVoice = result.data.filter { touchingPoint ->
                    val isAndroid = isAvailableForAndroid(touchingPoint)
                    val isCategoryMatch = isAvailableForAdCategory(adCategory, touchingPoint)
                    val isCityMatch = isAvailableForAdCity(adCity, touchingPoint)

                    isAndroid && isCategoryMatch && isCityMatch
                }.firstOrNull { tp ->
                    val isFullSOV = isFullShareOfVoice(tp)
                    isFullSOV
                }

                val otherAvailableTps = result.data.filter { touchingPoint ->
                    val isFullSOV = isFullShareOfVoice(touchingPoint)
                    val isAndroid = isAvailableForAndroid(touchingPoint)
                    val isCategoryMatch = isAvailableForAdCategory(adCategory, touchingPoint)
                    val isCityMatch = isAvailableForAdCity(adCity, touchingPoint)

                    val isValid = !isFullSOV && isAndroid && isCategoryMatch && isCityMatch
                    isValid
                }

                return when {
                    fullShareOfVoice != null && otherAvailableTps.isNotEmpty() -> {
                        val randomTp = otherAvailableTps
                            .filterNot { it.id == fullShareOfVoice.id }
                            .randomOrNull()

                        val list = buildList {
                            add(fullShareOfVoice)
                            if (randomTp != null) add(randomTp)
                        }

                        list
                    }

                    fullShareOfVoice != null -> {
                        listOf(fullShareOfVoice)
                    }

                    otherAvailableTps.size >= 2 -> {
                        val shuffled = otherAvailableTps.shuffled().distinctBy { it.id }.take(2)
                        shuffled
                    }

                    otherAvailableTps.isNotEmpty() -> {
                        listOf(otherAvailableTps.first())
                    }

                    else -> {
                        emptyList()
                    }
                }
            }

            is Resource.Failure -> {
                emptyList()
            }
        }
    }


    private fun isFullShareOfVoice(touchingPoint: AdTouchingPoint): Boolean {
        return touchingPoint.campaignData.shareOfVoice.contains(FULL_SHARE_OF_VOICE)
    }

    private fun isAvailableForAndroid(touchingPoint: AdTouchingPoint): Boolean {
        return touchingPoint.platforms.contains(ANDROID_PLATFORM)
    }

    private fun isAvailableForAdCategory(
        adCategory: Category,
        touchingPoint: AdTouchingPoint
    ): Boolean {
        return touchingPoint.targeting.categories.any {
            var match = false
            var category: Category? = adCategory
            while (category != null) {
                if (category.id == it.id) {
                    match = true
                    break
                } else {
                    category = category.parent
                }
            }
            match
        }
    }

    private fun isAvailableForAdCity(
        adCity: City?,
        touchingPoint: AdTouchingPoint
    ): Boolean {
        return touchingPoint.targeting.cities.isEmpty() ||
                touchingPoint.targeting.cities.any { it.id == adCity?.id } ||
                touchingPoint.targeting.cities.any {
                    // TODO: Remove this workaround after the backend fix is deployed
                    it.label.equals(
                        "Tout le Maroc",
                        ignoreCase = true
                    )
                }
    }

    companion object {
        private const val FULL_SHARE_OF_VOICE = "100%"
        private const val ANDROID_PLATFORM = "android"
    }
}
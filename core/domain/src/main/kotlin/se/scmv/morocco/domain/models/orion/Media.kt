package se.scmv.morocco.domain.models.orion

import androidx.compose.runtime.Stable

// COMPONENTS
@Stable
data class OrionImageUploader(
    override val baseData: OrionBaseComponentData,
    val maximum: Int
) : OrionBaseComponent

@Stable
data class OrionVideoUploader(
    override val baseData: OrionBaseComponentData,
    val maximum: Int
) : OrionBaseComponent

// VALUES
@Stable
data class OrionMediaUploaderValue(
    override val id: String,
    val medias: List<OrionKeyStringItem>
) : OrionBaseComponentValue {

    override fun stringValue(): String = medias.toString()
}
package se.scmv.morocco.domain.models

import kotlinx.datetime.LocalDateTime
import se.scmv.morocco.domain.models.AdDetails.Details.Media
import se.scmv.morocco.domain.models.AdDetails.Details.SimilarAdLocation


data class AdDetails(
    val ad: Details,
    val similarAds: List<SimilarAd>,
) {
    data class Details(
        val adId: String? = null,
        val listId: String,
        val listTime: LocalDateTime? = null,
        val listTimeString: String? = null,
        val title: String? = null,
        val description: String? = null,
        val reservedDays: List<String>? = null,
        val category: Category? = null,
        val type: AdType? = null,
        val discount: Int? = null,
        val media: Media? = null,
        val cityArea: City? = null,
        val seller: Seller? = null,
        val isHighlighted: Boolean? = null,
        val isInMyFavorites: Boolean? = null,
        val offersShipping: Boolean? = null,
        val isEcommerce: Boolean? = null,
        val isUrgent: Boolean? = null,
        val isHotDeal: Boolean? = null,
        val price: AdPrice,
        val params: AdParams? = null,
        var stock: Int? = null
    ) {


        data class Media(
            val mediaCount: Int? = null,
            val defaultImage: DefaultImage? = null,
            val media: MediaItem? = null
        )

        data class DefaultImage(
            val paths: ImagePaths? = null
        )

        data class MediaItem(
            val images: List<Image?>? = null,
            val videos: List<Video?>? = null
        )

        data class Image(
            val paths: ImagePaths? = null
        )

        data class ImagePaths(
            val smallThumbnail: String? = null,
            val largeThumbnail: String? = null,
            val standard: String? = null,
            val fullHd: String? = null
        )

        data class Video(
            val defaultPath: String? = null
        )

        data class Phone(
            val number: String? = null,
            val verified: Boolean? = null
        )

        data class StoreLocation(
            val address: String? = null
        )

        data class Logo(
            val defaultPath: String? = null
        )

        data class SimilarAdLocation(
            val city: City? = null,
            val area: Area? = null
        )

    }

    data class SimilarAd(
        val adId: String? = null,
        val listId: String? = null,
        val title: String? = null,
        val listTime: LocalDateTime? = null,
        val isInMyFavorites: Boolean? = null,
        val price: AdPrice,
        val category: Category? = null,
        val location: SimilarAdLocation? = null,
        val media: Media? = null,
        val discount: Int? = null,
        val isUrgent: Boolean? = null,
        val isHotDeal: Boolean? = null
    )

    sealed interface Seller {
        data class Private(
            val accountId: String? = null,
            val name: String? = null,
            val registrationDay: LocalDateTime? = null,
            val phone: Details.Phone? = null
        ) : Seller

        data class Store(
            val storeId: String? = null,
            val name: String? = null,
            val registrationDay: LocalDateTime? = null,
            val isVerifiedSeller: Boolean? = null,
            val location: Details.StoreLocation? = null,
            val phone: Details.Phone? = null,
            val logo: Details.Logo? = null,
            val website: String? = null,
            val type: String? = null,
            val isEcommerce: Boolean = false,
            val numberOfActiveAds: Int? = null
        ) : Seller


        fun getNullableName() = when (this) {
            is Private -> name
            is Store -> name
        }

        fun getSellerRegistrationDay() = when (this) {
            is Private -> registrationDay
            is Store -> registrationDay
        }

        fun getSellerLogo() = when (this) {
            is Private -> null
            is Store -> logo?.defaultPath
        }

        fun getSellerWebsite() = when (this) {
            is Private -> null
            is Store -> website
        }

        fun getSellerLocation() = when (this) {
            is Private -> null
            is Store -> location?.address
        }

        fun getSellerTrackingType() = when (this) {
            is Private -> "private"
            is Store -> {
                if (this.isEcommerce)
                    "ecommerce"
                else
                    "pro"
            }
        }

        fun isVerified() = when (this) {
            is Private -> false
            is Store -> isVerifiedSeller
        }

        fun isShop() = when (this) {
            is Private -> false
            is Store -> true
        }

        fun getNullablePhone() = when (this) {
            is Private -> phone
            is Store -> phone
        }

        fun phoneStartWith05(): Boolean {
            return when (this) {
                is Private -> !phone?.number.isNullOrEmpty() && !phone?.number?.startsWith("05")!!
                is Store -> !phone?.number.isNullOrEmpty() && !phone?.number?.startsWith("05")!!
            }
        }


        fun hasPhone() = when (this) {
            is Private -> !phone?.number.isNullOrEmpty()
            is Store -> !phone?.number.isNullOrEmpty()
        }

        fun phoneVerified() = when (this) {
            is Private -> phone?.verified
            is Store -> phone?.verified
        }

        fun getSellerAccountId() = when (this) {
            is Private -> "${accountId}"
            is Store -> "${storeId}"
        }
    }

}

data class CachedAdDetails(
    val imageUrl: String?,
    val title: String?,
    val adId: String,
    val date: LocalDateTime?,
    val imageCount: Int,
    val videoCount: Int,
    val videoUrl: String?,
    val isStore: Boolean,
    val price: AdPrice,
    val location: String?,
    val category: String?,
    val categoryIcon: String?,
    val isUrgent: Boolean?,
    val isHotDeal: Boolean?,
    val discount: Int?,
    val isInMyFavorites: Boolean = false
)





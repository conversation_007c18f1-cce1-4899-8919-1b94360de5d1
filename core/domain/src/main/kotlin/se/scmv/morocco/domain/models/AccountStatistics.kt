package se.scmv.morocco.domain.models

import kotlinx.datetime.LocalDate

data class AccountStatisticsMetric(
    val name: AccountStatisticsMetricName,
    val data: List<AccountStatisticsPoint>,
    val total: Int
)

enum class AccountStatisticsMetricName {
    PUBLISH, VIEW, BUMP, CONVERSATION, DELETE, GALLERY, HIG<PERSON><PERSON>HT, PHONEVIEW
}

data class AccountStatisticsPoint(
    val date: LocalDate,
    val value: Int
)

enum class AccountStatisticsRange {
    ALL_TIME,
    TODAY,
    LAST_7_DAYS,
    LAST_15_DAYS,
    LAST_30_DAYS
}
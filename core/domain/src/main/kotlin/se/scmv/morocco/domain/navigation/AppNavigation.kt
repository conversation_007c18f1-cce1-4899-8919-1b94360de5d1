package se.scmv.morocco.domain.navigation

import android.content.Context
import se.scmv.morocco.domain.models.AdDetails

interface AppNavigation {
    fun navigateToAdView(listingId: String)
}
interface ShopNavigator {
    fun openShop(shopId: String)
}

interface EcommerceNavigator {
    fun buyEcommerceProduct(context: Context, ad: AdDetails.Details, adUuid: String)
}

//interface MessagingNavigation {
//    fun openConversation(conversationId: String)
//}

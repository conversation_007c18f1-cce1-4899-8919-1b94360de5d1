package se.scmv.morocco.domain.usecases

import se.scmv.morocco.domain.models.ListingAd
import kotlin.math.max


private const val ADS_CHUNK_SIZE = 10
private const val PREMIUM_ADS_SIZE_1 = 1
private const val PREMIUM_ADS_SIZE_2 = 2
private const val PREMIUM_ADS_SIZE_3 = 3

object MixListingAdsUseCase {
    fun invoke(
        publishedAndNewConstructionAds: List<ListingAd>,
        premiumAds: List<ListingAd.Premium>,
        dfpBannerAds: List<ListingAd.DfpBanner>
    ): List<ListingAd> {
        if (publishedAndNewConstructionAds.isEmpty() && premiumAds.isEmpty()) return emptyList()

        val premiumAdsToInject = getPremiumAdsToInject(premiumAds)
        val normalAdsChunked = publishedAndNewConstructionAds.chunked(ADS_CHUNK_SIZE)
        val result = mutableListOf<ListingAd>()
        for (i in 0..max(normalAdsChunked.size, premiumAdsToInject.size)) {
            premiumAdsToInject.getOrNull(i)?.let {
                result.add(it)
            }
            normalAdsChunked.getOrNull(i)?.let {
                result.addAll(it)
            }
        }
        if (dfpBannerAds.isEmpty()) return result
        val indexOfFirstPremium = result.indexOfLast { it is ListingAd.Premium }
        if (indexOfFirstPremium != -1) {
            result.add(indexOfFirstPremium + 1, dfpBannerAds.first())
            result.add(result.lastIndex + 1, dfpBannerAds.last())
        } else {
            if (result.size > ADS_CHUNK_SIZE * 2) {
                result.add(ADS_CHUNK_SIZE * 2, dfpBannerAds.first())
                result.add(result.lastIndex + 1, dfpBannerAds.last())
            } else {
                result.addAll(dfpBannerAds)
            }
        }

        return result
    }

    private fun getPremiumAdsToInject(premiumAds: List<ListingAd.Premium>): List<ListingAd.Premium> {
        return when (premiumAds.size) {
            PREMIUM_ADS_SIZE_1 -> {
                List(PREMIUM_ADS_SIZE_3) { premiumAds.first() }
            }

            PREMIUM_ADS_SIZE_2 -> {
                premiumAds + premiumAds.first()
            }

            else -> premiumAds
        }
    }
}
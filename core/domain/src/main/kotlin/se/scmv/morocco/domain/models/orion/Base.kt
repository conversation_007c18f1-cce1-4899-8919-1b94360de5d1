package se.scmv.morocco.domain.models.orion

import androidx.compose.runtime.Stable
import androidx.compose.ui.util.fastJoinToString

// COMPONENTS

@Stable
sealed interface OrionBaseComponent {
    val baseData: OrionBaseComponentData
}

@Stable
data class OrionBaseComponentData(
    val id: String,
    val title: String,
    val required: Boolean,
    val iconUrl: String,
    val validations: List<OrionComponentValidation>,
    val dependencies: List<OrionComponentDependency> = emptyList()
)

@Stable
data class OrionComponentValidation(
    val errorMessage: String,
    val regex: String
)

@Stable
data class OrionComponentDependency(
    val dependsOn: String,
    val condition: Type
) {
    enum class Type { NOT_EMPTY, EQUALS }
}

@Stable
data class OrionKeyStringItem(
    val id: String,
    val name: String
)

// VALUES
const val ORION_CATEGORY_KEY = "category"
const val ORION_KEYWORD_KEY = "keyword"
@Stable
interface OrionBaseComponentValue {
    val id: String

    fun stringValue(): String
}

@Stable
data class OrionKeyStringValue(
    override val id: String,
    val value: String
) : OrionBaseComponentValue {
    override fun stringValue(): String = value
}

@Stable
data class OrionKeyBooleanValue(
    override val id: String,
    val value: Boolean
) : OrionBaseComponentValue {
    override fun stringValue(): String = value.toString()
}

@Stable
data class OrionKeyRangeValue(
    override val id: String,
    val min: Int,
    val max: Int,
) : OrionBaseComponentValue {
    override fun stringValue(): String = "$min-$max"
}
@Stable
data class OrionSelectedKeysValue(
    override val id: String,
    val keys: List<String>
) : OrionBaseComponentValue {
    override fun stringValue(): String = keys.fastJoinToString()
}

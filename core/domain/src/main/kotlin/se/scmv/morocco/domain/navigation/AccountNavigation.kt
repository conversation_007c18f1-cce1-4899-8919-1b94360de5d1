package se.scmv.morocco.domain.navigation

import se.scmv.morocco.domain.models.VasPacksApplication

interface AccountNavigation {
    fun navigateToAdInsert(
        adId: String?,
        adCategoryKey: String?,
        adType: String?,
        toImageStep: Boolean = false
    )

    fun navigateToVasActivity(
        adId: String,
        adCategoryKey: String,
        adType: String,
        application: VasPacksApplication
    )

    fun navigateToAdView(
        adListId: String,
        imageUrl: String?,
        title: String?,
        date: String?,
        imageCount: Int?,
        videoCount: Int?,
        videoUrl: String?,
        isStore: Boolean?,
        price: String?,
        oldPrice: String?,
        location: String?,
        category: String?,
        categoryIcon: String?,
        isUrgent: Boolean?,
        isHotDeal: Boolean?,
        discount: Int?
    )
}
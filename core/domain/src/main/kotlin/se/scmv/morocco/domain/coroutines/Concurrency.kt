package se.scmv.morocco.domain.coroutines

import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import se.scmv.morocco.domain.models.NetworkAndBackendErrors
import se.scmv.morocco.domain.models.NetworkErrors
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.models.failure
import se.scmv.morocco.domain.models.success


/**
 * Executes two suspend functions concurrently and returns their combined results.
 * If both succeed, returns a Pair of their results.
 * If either fails, returns the error from the failing function.
 *
 * @param firstCall The first suspend function to execute
 * @param secondCall The second suspend function to execute
 * @return A Resource containing either a Pair of results or the first encountered error
 */
suspend fun <T1, T2> executeInParallel(
    firstCall: suspend () -> Resource<T1, NetworkAndBackendErrors>,
    secondCall: suspend () -> Resource<T2, NetworkAndBackendErrors>
): Resource<Pair<T1, T2>, NetworkAndBackendErrors> {
    return coroutineScope {
        val firstDeferred = async { firstCall() }
        val secondDeferred = async { secondCall() }

        // Wait for both to complete
        val firstResult = firstDeferred.await()
        val secondResult = secondDeferred.await()

        // Check results and combine them appropriately
        when {
            firstResult is Resource.Success && secondResult is Resource.Success -> {
                // Both succeeded - return paired results
                success(Pair(firstResult.data, secondResult.data))
            }

            firstResult is Resource.Failure -> {
                // First call failed
                failure(firstResult.error)
            }

            secondResult is Resource.Failure -> {
                // Second call failed
                failure(secondResult.error)
            }

            else -> failure(NetworkAndBackendErrors.Network(NetworkErrors.UNKNOWN))
        }
    }
}

/**
 * Executes two suspend functions sequentially and returns their combined results.
 * The second function is only executed if the first one succeeds, and it receives the result of the first function as input.
 * If both succeed, returns a Pair of their results.
 * If the first call fails, its error is returned and the second call is not executed.
 * If the second call (after a successful first call) fails, its error is returned.
 *
 * @param firstCall The first suspend function to execute.
 * @param secondCall The second suspend function to execute, which takes the successful result of [firstCall] as its input.
 * @return A Resource containing either a Pair of results (if both calls succeed) or the encountered error.
 */
suspend fun <T1, T2> executeSequentially(
    firstCall: suspend () -> Resource<T1, NetworkAndBackendErrors>,
    secondCall: suspend (T1) -> Resource<T2, NetworkAndBackendErrors>
): Resource<Pair<T1, T2>, NetworkAndBackendErrors> {
    return when (val firstResult = firstCall()) {
        is Resource.Success -> {
            when (val secondResult = secondCall(firstResult.data)) {
                is Resource.Success -> success(Pair(firstResult.data, secondResult.data))

                is Resource.Failure -> secondResult
            }
        }

        is Resource.Failure -> firstResult
    }
}
package se.scmv.morocco.domain.repositories

import androidx.paging.PagingData
import kotlinx.coroutines.flow.Flow
import se.scmv.morocco.domain.models.ListingAd
import se.scmv.morocco.domain.models.NetworkAndBackendErrors
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.models.StoreProfileInfo
import se.scmv.morocco.domain.models.filter.RangeParam

interface ListingAdRepository {
    fun getListingStoreAdByStoreIDForPagination(
        storeId: String,
        text: String?,
        categoryId: Int,
        hasImage: Boolean?,
        hasPrice: Boolean?,
        priceRange: Pair<Double, Double>?,
        cityIds: List<Int>?,
        offersShipping: Boolean?,
        isHotDeal: Boolean?,
        isUrgent: Boolean?,
        numericParams: List<Pair<String, Double>>?,
        singleTextParam: List<Pair<String, String>>?,
        singleBooleanParam: List<Pair<String, Boolean>>?,
        textParams: List<Pair<String, List<String>>>?,
        rangeParams: List<RangeParam>?,
    ): Flow<PagingData<ListingAd.Published>>

    suspend fun getListingStoreAdByStoreID(
        storeId: String,
        text: String?,
        categoryId: Int,
        hasImage: Boolean?,
        hasPrice: Boolean?,
        priceRange: Pair<Double, Double>?,
        cityIds: List<Int>?,
        offersShipping: Boolean?,
        isHotDeal: Boolean?,
        isUrgent: Boolean?,
        numericParams: List<Pair<String, Double>>?,
        singleTextParam: List<Pair<String, String>>?,
        singleBooleanParam: List<Pair<String, Boolean>>?,
        textParams: List<Pair<String, List<String>>>?,
        rangeParams: List<RangeParam>?
    ): Resource<Int, NetworkAndBackendErrors>

    suspend fun getStoreDetails(storeId: String): Resource<StoreProfileInfo, NetworkAndBackendErrors>
}
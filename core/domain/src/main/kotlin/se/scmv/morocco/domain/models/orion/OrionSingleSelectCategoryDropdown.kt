package se.scmv.morocco.domain.models.orion

import androidx.compose.runtime.Stable
import se.scmv.morocco.domain.models.AdTypeKey
import se.scmv.morocco.domain.models.Category

// COMPONENTS
@Stable
data class OrionSingleSelectCategoryDropdown(
    override val baseData: OrionBaseComponentData,
    val allowParentSelection: Boolean,
    val enabled: Boolean = true,
) : OrionBaseComponent

// VALUES
@Stable
data class OrionSingleSelectCategoryDropdownValue(
    override val id: String,
    val category: Category,
    val adTypeKey: AdTypeKey?
) : OrionBaseComponentValue {
    override fun stringValue(): String = toString()
}
package se.scmv.morocco.domain.models

data class AdTouchingPoint(
    val id: String,
    val campaignData: CampaignData,
    val platforms: List<String>,
    val targeting: Targeting
)

data class CampaignData(
    val shareOfVoice: List<String>,
    val title: String,
    val description: String,
    var clientLogo: String,
    val redirectLink: String
)

data class Targeting(
    val categories: List<LabelValue>,
    val cities: List<LabelValue>
)

data class LabelValue(
    val id: String,
    var label: String
)

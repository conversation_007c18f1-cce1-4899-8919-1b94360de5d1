package se.scmv.morocco.domain.models

import se.scmv.morocco.domain.models.filter.RangeParam

sealed interface FilterValue {
    data class ToggleButtonValue(
        val toggle: ToggleConfigKey
    ): FilterValue
     data class SingleTextParam(
         val params: List<Pair<String, String>> = emptyList()
     ): FilterValue
    data class SingleBooleanParam(
        val params: List<Pair<String, Boolean>> = emptyList()
    ): FilterValue
    data class SmartDropDownIcon(
        val items: List<Pair<String, List<String>>> = emptyList()
    ): FilterValue
    data class SingleMatchNumeric(
       val  numericParams: List<Pair<String, Double>>? = emptyList()
    ): FilterValue
    data class RangeParams(
        val rangeParams: List<RangeParam>?
    ): FilterValue
}
package se.scmv.morocco.domain.models.filter

import android.os.Parcel
import android.os.Parcelable


data class ListingFilterCategories(
    val category: Category,
    val children: List<ListingFilterCategories>? = emptyList(),
    val icon: String,
    val index: Int,
    val name: String,
    val trackingName: String?,
    val urlSlug: String?
) {
    data class Category(
        val adType: String,
        val id: String
    )
}


data class ListingCategoryFilters(
    var category: Category,
    var filters: Filters
) {
    data class Category(
        var id: String? = null,
        var adType: String? = null
    )

    data class Filters(
        var navBarFilters: List<BaseFilters> = emptyList(),
        var primaryFilters: List<BaseFilters> = emptyList(),
        var secondaryFilters: List<BaseFilters> = emptyList()
    )

    data class BaseFilters(
        var icon: String? = null,
        var id: String,
        var isParam: Boolean? = null,
        var filterItems: List<FilterItems> = emptyList(),
        val range: List<Int>?,
        val step: Int?,
        val suffix: String?,
        var name: String? = null,
        var type: String? = null,
        val child: String? = null,
        val fields: List<Field>? = null,
        val childParam: ChildParam? = null,
        val description: String? = null,
        val defaultValue: Boolean = false
    )


    data class Field(
        val id: String,
        val isParam: String,
        val name: String,
        var isSelected: Boolean = false
    ) : Parcelable {

        constructor(parcel: Parcel) : this(
            parcel.readString()!!,
            parcel.readString()!!,
            parcel.readString()!!,
            parcel.readByte() != 0.toByte()
        )

        override fun writeToParcel(parcel: Parcel, flags: Int) {
            parcel.writeString(id)
            parcel.writeString(isParam)
            parcel.writeString(name)
            parcel.writeByte(if (isSelected) 1 else 0)
        }

        override fun describeContents(): Int {
            return 0
        }

        companion object CREATOR : Parcelable.Creator<Field> {
            override fun createFromParcel(parcel: Parcel): Field {
                return Field(parcel)
            }

            override fun newArray(size: Int): Array<Field?> {
                return arrayOfNulls(size)
            }
        }
    }

    data class ChildParam(
        val id: String,
        val name: String
    )

    data class FilterItems(
        var key: String? = null,
        var name: String? = null,
        var label: String? = null,
        var short: String? = null,
        var trackingName: String? = null,
        val children: List<Child>? = null,
        var isSelected: Boolean = false,
        var selectedChildCount: Int = 0
    )

    data class Child(
        val key: String,
        val label: String,
        var isSelected:Boolean = false
    )
}


val mockChildren = listOf(
    ListingCategoryFilters.Child(key = "child1", label = "Child 1", isSelected = false),
    ListingCategoryFilters.Child(key = "child2", label = "Child 2", isSelected = true),
    ListingCategoryFilters.Child(key = "child3", label = "Child 3", isSelected = false)
)


val mockFilterItems = listOf(
    ListingCategoryFilters.FilterItems(
        key = "1",
        name = "Price",
        label = "Under $50",
        short = "Low",
        trackingName = "price_under_50",
        children = mockChildren, // No children for this item
        isSelected = true,
        selectedChildCount = 0
    ),
    ListingCategoryFilters.FilterItems(
        key = "2",
        name = "Category",
        label = "Electronics",
        short = "Elec",
        trackingName = "category_electronics",
        children = mockChildren, // Children items for this filter
        isSelected = false,
        selectedChildCount = 1 // 1 child is selected in the mock data
    ),
    ListingCategoryFilters.FilterItems(
        key = "3",
        name = "Brand",
        label = "Apple",
        short = "Apple",
        trackingName = "brand_apple",
        children = null, // No children for this item
        isSelected = false,
        selectedChildCount = 0
    ),
    ListingCategoryFilters.FilterItems(
        key = "4",
        name = "Color",
        label = "Red",
        short = "Red",
        trackingName = "color_red",
        children = mockChildren, // Another set with children
        isSelected = true,
        selectedChildCount = 2 // 2 children are selected in the mock data
    )
)
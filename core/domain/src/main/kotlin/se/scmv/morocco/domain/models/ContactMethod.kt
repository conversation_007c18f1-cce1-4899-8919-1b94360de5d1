package se.scmv.morocco.domain.models

/**
 * Represents different contact methods for price inquiries
 */
sealed class ContactMethod {
    /**
     * WhatsApp contact method with phone number
     */
    data class WhatsApp(val phoneNumber: String) : ContactMethod()
    
    /**
     * Phone call contact method with phone number
     */
    data class PhoneCall(val phoneNumber: String) : ContactMethod()
    
    /**
     * In-app chat contact method with ad ID
     */
    data class Chat(val adId: String) : ContactMethod()
}

/**
 * Utility functions for contact method detection
 */
object ContactMethodUtils {
    /**
     * Determines if a phone number can use WhatsApp
     * WhatsApp is available for Moroccan numbers starting with 06, 07
     */
    fun canUseWhatsApp(phoneNumber: String?): <PERSON><PERSON><PERSON> {
        if (phoneNumber.isNullOrBlank()) return false
        
        // Remove any non-digit characters
        val cleanNumber = phoneNumber.replace(Regex("[^0-9]"), "")
        
        // Check if it's a valid mobile number
        return cleanNumber.matches(Regex("^(06|07)[0-9]{8}$"))
    }
    
    /**
     * Determines the best contact method based on available data
     * Priority: WhatsApp > Phone Call > Chat
     */
    fun determineContactMethod(phoneNumber: String?, adId: String): ContactMethod {
        return when {
            // Priority 1: WhatsApp if phone number supports it
            !phoneNumber.isNullOrBlank() && canUseWhatsApp(phoneNumber) ->
                ContactMethod.WhatsApp(phoneNumber)

            // Priority 2: Phone call if phone exists but no WhatsApp
            !phoneNumber.isNullOrBlank() ->
                ContactMethod.PhoneCall(phoneNumber)

            // Priority 3: Chat as final fallback
            else ->
                ContactMethod.Chat(adId)
        }
    }
}

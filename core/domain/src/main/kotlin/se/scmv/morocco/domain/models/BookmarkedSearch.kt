package se.scmv.morocco.domain.models

import kotlinx.datetime.LocalDateTime
import se.scmv.morocco.domain.models.orion.OrionBaseComponentValue

data class BookmarkedSearch(
    val id: Int,
    val label: String,
    val imageUrL: String?,
    val date: LocalDateTime,
    val searchQuery: String,
    val filtersValues: List<OrionBaseComponentValue>
)

data class BookmarkedSearchQuery(
    val id: Int,
    val query: String
)

package se.scmv.morocco.domain.shared

import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import javax.inject.Inject
import javax.inject.Singleton

data class BookmarkAdEvent(
    val listId: String,
    val isFavorite: <PERSON>olean,
)

@Singleton
class BookmarkAdsEventManager @Inject constructor() {

    private val _events = MutableSharedFlow<BookmarkAdEvent>()
    val events = _events.asSharedFlow()

    suspend fun sendEvent(event: BookmarkAdEvent) {
        _events.emit(event)
    }
}
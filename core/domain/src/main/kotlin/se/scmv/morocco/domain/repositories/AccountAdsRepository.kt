package se.scmv.morocco.domain.repositories

import androidx.paging.PagingData
import kotlinx.coroutines.flow.Flow
import se.scmv.morocco.domain.models.AccountAd
import se.scmv.morocco.domain.models.AccountDeactivationReasonInput
import se.scmv.morocco.domain.models.CountStatus
import se.scmv.morocco.domain.models.MyAccountAdsFilterInput
import se.scmv.morocco.domain.models.NetworkAndBackendErrors
import se.scmv.morocco.domain.models.Resource

interface AccountAdsRepository {

    fun getMyAds(
        myAdsFilterInput: MyAccountAdsFilterInput,
    ): Flow<PagingData<AccountAd>>

    suspend fun getAdsCount(): Resource<List<CountStatus>, NetworkAndBackendErrors>

    suspend fun patchAd(
        adId: String,
        isUrgent: Boolean,
        isHotDeal: Boolean,
        discount: Int
    ): Resource<Unit, NetworkAndBackendErrors>

    suspend fun bulkDeactivateAds(
        adIds: List<String>,
        reason: AccountDeactivationReasonInput
    ): Resource<Unit, NetworkAndBackendErrors>

    suspend fun bulkDeleteAds(
        adIds: List<String>,
        reason: AccountDeactivationReasonInput
    ): Resource<Unit, NetworkAndBackendErrors>

    suspend fun activateAd(adIds: String): Resource<Unit, NetworkAndBackendErrors>
}
package se.scmv.morocco.domain.usecases

import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.models.SignInType
import se.scmv.morocco.domain.models.UpdatePasswordErrors
import se.scmv.morocco.domain.repositories.AuthenticationRepository
import javax.inject.Inject

class UpdatePasswordAndSignInUseCase @Inject constructor(
    private val authenticationRepository: AuthenticationRepository,
    private val logoutUseCase: LogoutUseCase
) {
    suspend operator fun invoke(
        email: String,
        currentPassword: String,
        newPassword: String
    ): Resource<Unit, UpdatePasswordErrors> {
        val updatePasswordResult = authenticationRepository.updatePassword(
            currentPassword = currentPassword,
            newPassword = newPassword
        )
        if (updatePasswordResult is Resource.Failure) return Resource.Failure(
            UpdatePasswordErrors.NetworkOrBackend(error = updatePasswordResult.error)
        )
        // The backend logout all the devices if password is changed. So the user must login after password change
        return when (
            authenticationRepository.signIn(
                emailOrPhone = email,
                password = newPassword,
                type = SignInType.EMAIL
            )
        ) {
            is Resource.Success -> Resource.Success(Unit)
            is Resource.Failure -> {
                // Logout the user so he'll be redirected to login.
                logoutUseCase()
                Resource.Failure(UpdatePasswordErrors.NeedToReLogin)
            }
        }
    }
}
package se.scmv.morocco.domain.models.orion

import androidx.compose.runtime.Stable

@Stable
data class OrionTextField(
    override val baseData: OrionBaseComponentData,
    val enabled: Boolean,
    val suffix: String?,
    val isLarge: Boolean,
    val inputType: InputType,
    val potentialValue: String? = null,
    val notifyLapTitle: Boolean = false,
    val notifyLapDescription: Boolean = false,
) : OrionBaseComponent {

    @Stable
    enum class InputType {
        TEXT, NUMBER, PHONE
    }
}
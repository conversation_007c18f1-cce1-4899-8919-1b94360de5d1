package se.scmv.morocco.domain.models

import kotlinx.datetime.LocalDateTime

data class AccountOrder(
    val id: String,
    val product: AccountOrderProduct,
    val unitsPurchased: Int,
    val status: AccountOrderStatus,
    val date: LocalDateTime,
    val total: Int,
) {
    fun cancellable(): Boolean {
        return status == AccountOrderStatus.INITIATED || status == AccountOrderStatus.PREPARING
    }
}

data class AccountOrderProduct(
    val listId: String,
    val name: String,
    val imageUrl: String?,
)

enum class AccountOrderStatus {
    INITIATED, PREPARING, DELIVERING, DELIVERED, CANCELLED;

    fun getAnalyticsName() = when(this) {
        INITIATED -> "initiated_orders"
        PREPARING -> "orders_ready"
        DELIVERING -> "order_ondelivery"
        DELIVERED -> "deliveredorder"
        CANCELLED -> "canceled_order"
    }
}
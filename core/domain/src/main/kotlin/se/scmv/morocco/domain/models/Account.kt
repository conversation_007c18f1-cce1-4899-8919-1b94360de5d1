package se.scmv.morocco.domain.models

import kotlinx.datetime.LocalDate
import se.scmv.morocco.analytics.models.AnalyticsEvent

sealed interface Account {
    sealed interface Connected : Account {
        data class Private(val contact: AccountInfo, val isPhoneHidden: Boolean) : Connected
        data class Shop(val contact: AccountInfo, val store: StoreInfo) : Connected

        fun connectedContact(): AccountInfo = when(this) {
            is Private -> contact
            is Shop -> contact
        }

        fun analyticsAccountType() = when(this) {
            is Private -> AnalyticsEvent.ParamValues.ACCOUNT_TYPE_PRIVATE
            is Shop -> AnalyticsEvent.ParamValues.ACCOUNT_TYPE_SHOP
        }
    }

    data object NotConnected : Account

    fun isLogged(): Boolean {
        return this is Connected
    }

    fun isShop(): Boolean {
        return this is Connected.Shop
    }

    fun isPrivate(): Boolean {
        return this is Connected.Private
    }
}

data class AccountInfo(
    val accountId: String,
    val name: String,
    val email: String,
    val phone: String?,
    val location: City?,
    val creationDate: String
)

data class StoreInfo(
    val logoUrl: String?,
    val points: Int,
    val pointsExpirationDate: LocalDate?,
    val membership: String,
    val category: Category,
    val website: String?,
    val verified: Boolean,
    val shortDescription: String?,
    val longDescription: String?,
    val cities: List<City>,
    val phones: List<String>,
    val startDate: LocalDate?,
    val expirationDate: LocalDate?,
    val allowedAccess: AllowedAccess
)

data class AllowedAccess(
    val adHotdealAllowed: Boolean,
    val adMaxImages: Int,
    val adMaxVideos: Int,
    val adUrgentAllowed: Boolean,
    val adsBoostedFilterAllowed: Boolean,
    val adsBulkDeleteAllowed: Boolean,
    val avitoTokenAllowed: Boolean,
    val deliveryAllowed: Boolean,
    val statsPerAdAllowed: Boolean,
    val supportViaWhatsappAllowed: Boolean,
    val vasConfigureExecTimeAllowed: Boolean,
) {
    companion object {
        val default = AllowedAccess(
            adHotdealAllowed = false,
            adMaxImages = 10,
            adMaxVideos = 1,
            adUrgentAllowed = false,
            adsBoostedFilterAllowed = false,
            adsBulkDeleteAllowed = false,
            avitoTokenAllowed = false,
            deliveryAllowed = false,
            statsPerAdAllowed = false,
            supportViaWhatsappAllowed = false,
            vasConfigureExecTimeAllowed = false
        )
    }
}
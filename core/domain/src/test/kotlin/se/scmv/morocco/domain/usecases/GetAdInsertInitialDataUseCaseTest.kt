package se.scmv.morocco.domain.usecases

import io.mockk.MockKAnnotations
import io.mockk.coEvery
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import kotlinx.datetime.LocalDate
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import se.scmv.morocco.domain.models.Account
import se.scmv.morocco.domain.models.AccountInfo
import se.scmv.morocco.domain.models.AdType
import se.scmv.morocco.domain.models.AdTypeKey
import se.scmv.morocco.domain.models.AllowedAccess
import se.scmv.morocco.domain.models.Category
import se.scmv.morocco.domain.models.CategoryTree
import se.scmv.morocco.domain.models.City
import se.scmv.morocco.domain.models.NetworkAndBackendErrors
import se.scmv.morocco.domain.models.NetworkErrors
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.models.StoreInfo
import se.scmv.morocco.domain.models.VasPacksApplication
import se.scmv.morocco.domain.models.failure
import se.scmv.morocco.domain.models.success
import se.scmv.morocco.domain.repositories.AccountRepository
import se.scmv.morocco.domain.repositories.AdRepository
import se.scmv.morocco.domain.repositories.ConfigRepository
import kotlin.test.BeforeTest
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@RunWith(JUnit4::class)
class GetAdInsertInitialDataUseCaseTest {

    @MockK
    private lateinit var accountRepository: AccountRepository

    @MockK
    private lateinit var configRepository: ConfigRepository

    @MockK
    private lateinit var adRepository: AdRepository

    @InjectMockKs
    private lateinit var useCase: GetAdInsertInitialDataUseCase

    @BeforeTest
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `when account is not connected, return network error`() = runTest {
        // Given
        coEvery { accountRepository.currentAccount } returns flowOf(Account.NotConnected)

        // When
        val result = useCase(null, "1010", AdTypeKey.SELL)

        // Then
        assertTrue(result is Resource.Failure)
        assertEquals(
            NetworkAndBackendErrors.Network(NetworkErrors.UNKNOWN),
            result.error
        )
    }

    @Test
    fun `when config repository returns failure, return the same failure`() = runTest {
        // Given
        val privateAccount = createPrivateAccount()
        val error = NetworkAndBackendErrors.Network(NetworkErrors.NO_INTERNET)

        coEvery { accountRepository.currentAccount } returns flowOf(privateAccount)
        coEvery { configRepository.getAdInsertCategories() } returns failure(error)

        // When
        val result = useCase(null, "1010", AdTypeKey.SELL)

        // Then
        assertTrue(result is Resource.Failure)
        assertEquals(error, result.error)
    }

    @Test
    fun `when adRepository returns failure, return the same failure`() = runTest {
        // Given
        val privateAccount = createPrivateAccount()
        val categories = createSampleCategories()
        val error = NetworkAndBackendErrors.Network(NetworkErrors.NO_INTERNET)

        coEvery { accountRepository.currentAccount } returns flowOf(privateAccount)
        coEvery { configRepository.getAdInsertCategories() } returns success(categories)
        coEvery { adRepository.getVasPacks(VasPacksApplication.ADINSERT) } returns failure(error)

        // When
        val result = useCase(null, "1010", AdTypeKey.SELL)

        // Then
        assertTrue(result is Resource.Failure)
        assertEquals(error, result.error)
    }

    @Test
    fun `when account is private, return all categories with default selection`() = runTest {
        // Given
        val privateAccount = createPrivateAccount()
        val categories = createSampleCategories()

        coEvery { accountRepository.currentAccount } returns flowOf(privateAccount)
        coEvery { configRepository.getAdInsertCategories() } returns success(categories)

        // When
        val result = useCase(null, "1010", AdTypeKey.SELL)

        // Then
        assertTrue(result is Resource.Success)
        with(result.data) {
            assertEquals(categories, this.categories)
            assertEquals("1010", this.selectedCategoryId)
            assertEquals(AdTypeKey.SELL, this.selectedAdTypeKey)
        }
    }

    @Test
    fun `when account is shop and category exists, return filtered category with selection`() =
        runTest {
            // Given
            val shopCategory = Category("2020", "Electronics", "icon", "Electronics")
            val shopAccount = createShopAccount(shopCategory)

            val categories = createSampleCategories()
            val electronicsTree =
                categories[1] // This corresponds to the electronics category in our sample

            coEvery { accountRepository.currentAccount } returns flowOf(shopAccount)
            coEvery { configRepository.getAdInsertCategories() } returns success(categories)

            // When
            val result = useCase(null, "1010", AdTypeKey.SELL)

            // Then
            assertTrue(result is Resource.Success)
            with(result.data) {
                assertEquals(listOf(electronicsTree), this.categories)
                assertEquals("2021", this.selectedCategoryId) // The first child category ID
                assertEquals(AdTypeKey.SELL, this.selectedAdTypeKey)
            }
        }

    @Test
    fun `when account is shop but category doesn't exist, return default selection`() = runTest {
        // Given
        val nonExistentCategory = Category("9999", "NonExistent", "icon", "NonExistent")
        val shopAccount = createShopAccount(nonExistentCategory)
        val categories = createSampleCategories()

        coEvery { accountRepository.currentAccount } returns flowOf(shopAccount)
        coEvery { configRepository.getAdInsertCategories() } returns success(categories)

        // When
        val result = useCase(null, "1010", AdTypeKey.SELL)

        // Then
        assertTrue(result is Resource.Success)
        with(result.data) {
            assertEquals(categories, this.categories)
            assertEquals("1010", this.selectedCategoryId)
            assertEquals(AdTypeKey.SELL, this.selectedAdTypeKey)
        }
    }

    @Test
    fun `when shop category exists but has no adTypes, return default selection`() = runTest {
        // Given
        val shopCategory = Category("3030", "NoAdTypes", "icon", "NoAdTypes")
        val shopAccount = createShopAccount(shopCategory)

        val categoryWithoutAdTypes = CategoryTree(
            category = shopCategory,
            adTypes = emptyList(),
            children = emptyList()
        )

        val categories = createSampleCategories() + categoryWithoutAdTypes

        coEvery { accountRepository.currentAccount } returns flowOf(shopAccount)
        coEvery { configRepository.getAdInsertCategories() } returns success(categories)

        // When
        val result = useCase(null, "1010", AdTypeKey.SELL)

        // Then
        assertTrue(result is Resource.Success)
        with(result.data) {
            assertEquals(categories, this.categories)
            assertEquals("1010", this.selectedCategoryId)
            assertEquals(AdTypeKey.SELL, this.selectedAdTypeKey)
        }
    }

    // Helper methods to create test data
    private fun createPrivateAccount(): Account.Connected.Private {
        return Account.Connected.Private(
            contact = AccountInfo(
                accountId = "1234",
                name = "Test User",
                email = "<EMAIL>",
                phone = "**********",
                location = City("1", "Casablanca", "Casablanca"),
                creationDate = "2023-01-01"
            ),
            isPhoneHidden = false
        )
    }

    private fun createShopAccount(category: Category): Account.Connected.Shop {
        return Account.Connected.Shop(
            contact = AccountInfo(
                accountId = "5678",
                name = "Test Shop",
                email = "<EMAIL>",
                phone = "**********",
                location = City("2", "Rabat", "Rabat"),
                creationDate = "2023-01-01"
            ),
            store = StoreInfo(
                logoUrl = "https://example.com/logo.png",
                points = 100,
                membership = "Premium",
                category = category,
                website = "https://example.com",
                verified = true,
                shortDescription = "Short description",
                longDescription = "Long description",
                cities = listOf(City("2", "Rabat", "Rabat")),
                phones = listOf("**********", "**********", "**********"),
                allowedAccess = AllowedAccess.default,
                pointsExpirationDate = LocalDate.parse("2025-12-12"),
                startDate = LocalDate.parse("2025-12-12"),
                expirationDate = LocalDate.parse("2025-12-12"),
            )
        )
    }

    private fun createSampleCategories(): List<CategoryTree> {
        val sellAdType = AdType(AdTypeKey.SELL, "Sell", trackingName = "Sell")
        val rentAdType = AdType(AdTypeKey.RENT, "Rent", trackingName = "Rent")

        // Real estate category with children
        val realEstateCategory = Category("1010", "RealEstate", "house", "RealEstate")
        val apartmentsCategory =
            Category("1011", "Apartments", "apartment", "Apartments", realEstateCategory)
        val housesCategory = Category("1012", "Houses", "house", "Houses", realEstateCategory)

        val apartmentsCategoryTree = CategoryTree(
            category = apartmentsCategory,
            adTypes = listOf(sellAdType, rentAdType),
            children = emptyList()
        )

        val housesCategoryTree = CategoryTree(
            category = housesCategory,
            adTypes = listOf(sellAdType, rentAdType),
            children = emptyList()
        )

        val realEstateCategoryTree = CategoryTree(
            category = realEstateCategory,
            adTypes = listOf(sellAdType, rentAdType),
            children = listOf(apartmentsCategoryTree, housesCategoryTree)
        )

        // Electronics category with children
        val electronicsCategory = Category("2020", "Electronics", "electronics", "Electronics")
        val phonesCategory = Category("2021", "Phones", "phone", "Phones", electronicsCategory)
        val computersCategory =
            Category("2022", "Computers", "computer", "Computers", electronicsCategory)

        val phonesCategoryTree = CategoryTree(
            category = phonesCategory,
            adTypes = listOf(sellAdType),
            children = emptyList()
        )

        val computersCategoryTree = CategoryTree(
            category = computersCategory,
            adTypes = listOf(sellAdType),
            children = emptyList()
        )

        val electronicsCategoryTree = CategoryTree(
            category = electronicsCategory,
            adTypes = listOf(sellAdType),
            children = listOf(phonesCategoryTree, computersCategoryTree)
        )

        return listOf(realEstateCategoryTree, electronicsCategoryTree)
    }
}
package se.scmv.morocco.ui

import androidx.compose.runtime.Composable
import se.scmv.morocco.designsystem.components.NotConnectedScreen
import se.scmv.morocco.domain.models.Account

@Composable
fun AuthProtectedContent(
    account: Account,
    onLoginClicked: () -> Unit,
    authRequiredContent: @Composable (account: Account.Connected) -> Unit
) {
    when (account) {
        is Account.NotConnected -> NotConnectedScreen(onLoginClicked = onLoginClicked)
        is Account.Connected -> authRequiredContent(account)
    }
}
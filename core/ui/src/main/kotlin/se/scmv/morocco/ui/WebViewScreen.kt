package se.scmv.morocco.ui

import android.util.Log
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.multiplatform.webview.jsbridge.IJsMessageHandler
import com.multiplatform.webview.jsbridge.JsMessage
import com.multiplatform.webview.jsbridge.rememberWebViewJsBridge
import com.multiplatform.webview.web.WebView
import com.multiplatform.webview.web.WebViewNavigator
import com.multiplatform.webview.web.rememberWebViewState
import se.scmv.morocco.designsystem.components.AvTopAppBar

@Composable
fun WebViewScreen(
    url: String,
    title: String?,
    navigateBack: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Scaffold(
        modifier = modifier,
        topBar = {
            AvTopAppBar(title = title, onNavigationIconClicked = navigateBack)
        }
    ) {
        Box(modifier = Modifier.padding(it).fillMaxSize()) {
            val state = rememberWebViewState(url = url)
            val jsBridge = rememberWebViewJsBridge()
            WebView(
                modifier = Modifier.fillMaxSize(),
                state = state,
                captureBackPresses = false,
                webViewJsBridge = jsBridge
            )
            if (state.isLoading) {
                CircularProgressIndicator(modifier = Modifier.align(Alignment.Center))
            }
            LaunchedEffect(state.errorsForCurrentRequest) {
                state.errorsForCurrentRequest.forEach { error ->
                    Log.e(
                        "WebView: ",
                        "Error loading page: ${error.code} ${error.description}"
                    )
                }
            }
            DisposableEffect(Unit) {
                state.webSettings.apply {
                    isJavaScriptEnabled = true
                    supportZoom = true
                    androidWebSettings.apply {
                        useWideViewPort = true
                        domStorageEnabled = true
                    }
                }
                onDispose {}
            }
        }
    }
}

// TODO We need to check with the credit card payment provider to get the name of the function called when we click on "Retour au Marchand" to register a callback that closes the payment.
class BackTopAppJsMessageHandler(val onClick: () -> Unit) : IJsMessageHandler {

    override fun handle(
        message: JsMessage,
        navigator: WebViewNavigator?,
        callback: (String) -> Unit
    ) {
        onClick()
    }

    override fun methodName(): String {
        return "Retour au Marchand"
    }
}
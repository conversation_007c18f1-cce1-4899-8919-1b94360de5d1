package se.scmv.morocco.ui

object AppDeepLinks {
    private const val HOST = "app.avito"
    const val LISTING = "scm://$HOST/listing"
    const val AD_VIEW = "scm://$HOST/adview"
    const val AD_INSERT = "scm://$HOST/insertad"
    const val AD_BUMP = "scm://$HOST/adbump"
    const val ACCOUNT_ADS = "scm://$HOST/myads"
    const val MESSAGING = "scm://$HOST/messaging"
    const val CHAT = "scm://$HOST/chat"
    const val BOOKMARKS = "scm://$HOST/myfavorites"
}

object WebDeepLinks {
    private const val HOST = "www.avito.ma"
    const val LISTING_HTTP = "http://$HOST/list"
    const val LISTING_HTTPS = "https://$HOST/list"
    const val AD_VIEW_HTTP = "http://$HOST/ad"
    const val AD_VIEW_HTTPS = "https://$HOST/ad"
    const val AD_INSERT_HTTP = "http://$HOST/newad"
    const val AD_INSERT_HTTPS = "https://$HOST/newad"
    const val AD_BUMP_HTTP = "http://$HOST/adbump"
    const val AD_BUMP_HTTPS = "https://$HOST/adbump"
    const val MESSAGING_HTTP = "http://$HOST/messaging"
    const val MESSAGING_HTTPS = "https://$HOST/messaging"
}
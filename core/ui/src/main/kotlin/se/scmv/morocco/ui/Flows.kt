package se.scmv.morocco.ui

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.repeatOnLifecycle
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.withContext

/**
 * Observes a [Flow] as a series of events and triggers a callback each time a new event is emitted.
 * The events are only collected when the composable is in a `STARTED` lifecycle state, ensuring
 * that the UI is actively observing the flow only when in the foreground.
 *
 * @param flow The [Flow] of events to be observed.
 * @param key1 An optional key used to control recomposition of this effect. When [key1] changes, the effect will restart.
 * @param key2 An additional optional key to control recomposition alongside [key1].
 * @param onEvent A callback function that is triggered with each new event emitted by the [flow].
 *
 * The function uses [repeatOnLifecycle] with the [Lifecycle.State.STARTED] state to automatically stop
 * collecting events when the lifecycle moves to a `STOPPED` state, saving resources when the composable is
 * not visible. The flow is collected on the `Main` dispatcher for immediate UI updates.
 *
 * @param T The type of data emitted by the [flow].
 */
@Composable
fun <T> ObserveAsEvents(
    flow: Flow<T>,
    key1: Any? = null,
    key2: Any? = null,
    onEvent: (T) -> Unit
) {
    val lifecycleOwner = LocalLifecycleOwner.current
    LaunchedEffect(lifecycleOwner.lifecycle, key1, key2, flow) {
        lifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
            withContext(Dispatchers.Main.immediate) {
                flow.collect(onEvent)
            }
        }
    }
}
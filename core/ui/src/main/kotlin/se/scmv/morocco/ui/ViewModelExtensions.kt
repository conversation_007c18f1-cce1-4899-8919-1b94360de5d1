package se.scmv.morocco.ui

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.launch
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.domain.models.NetworkAndBackendErrors
import se.scmv.morocco.domain.models.NetworkErrors

/**
 * Displays a success message using a SnackBar.
 *
 * This function uses [SnackBarController] to emit a [SnackBarEvent] that displays the provided
 * success message. The SnackBar is configured to include a dismiss action for user convenience.
 *
 * @param message The message to be displayed, represented as a [UiText].
 *
 * @see SnackBarController
 * @see SnackBarEvent
 */
fun ViewModel.renderSuccess(message: UiText) {
    viewModelScope.launch {
        SnackBarController.showSnackBar(
            event = SnackBarEvent(
                message = message,
                type = SnackBarType.SUCCESS,
                withDismissAction = true
            )
        )
    }
}

/**
 * Displays a success message using a SnackBar.
 *
 * This function uses [SnackBarController] to emit a [SnackBarEvent] that displays the provided
 * success message. The SnackBar is configured to include a dismiss action for user convenience.
 *
 * @param message The message to be displayed, represented as a [UiText].
 * @property action A [SnackBarAction] that provides an action button on the snackbar, such as an "Undo" button.
 *
 * @see SnackBarController
 * @see SnackBarEvent
 */
fun ViewModel.renderSuccess(message: UiText, action: SnackBarAction) {
    viewModelScope.launch {
        SnackBarController.showSnackBar(
            event = SnackBarEvent(
                message = message,
                type = SnackBarType.SUCCESS,
                withDismissAction = false,
                action = action
            )
        )
    }
}

/**
 * Displays an error message using a SnackBar.
 *
 * This function uses [SnackBarController] to emit a [SnackBarEvent] that displays an appropriate
 * error message.
 * The SnackBar is configured to include a dismiss action for user convenience.
 *
 * @param message The error to be handled and displayed.
 *
 * @see SnackBarController
 * @see SnackBarEvent
 */
fun ViewModel.renderFailure(message: UiText) {
    viewModelScope.launch {
        SnackBarController.showSnackBar(
            event = SnackBarEvent(
                message = message,
                type = SnackBarType.ERROR,
                withDismissAction = true
            )
        )
    }
}

/**
 * Displays an error message using a SnackBar based on a network or backend error.
 *
 * This function uses [SnackBarController] to emit a [SnackBarEvent] that displays an appropriate
 * error message based on the provided [NetworkAndBackendErrors].
 * The SnackBar is configured to include a dismiss action for user convenience.
 *
 * - For network errors, a predefined message resource is resolved based on the type of error (e.g., no internet or unknown error).
 * - For backend errors, the error message is displayed as plain text.
 *
 * @param error The error to be handled and displayed, represented as a [NetworkAndBackendErrors].
 *
 * @see SnackBarController
 * @see SnackBarEvent
 */
fun ViewModel.renderFailure(error: NetworkAndBackendErrors) {
    renderFailure(error.asUiText())
}

/**
 * Converts a [NetworkAndBackendErrors] instance into a [UiText] representation.
 *
 * This function maps different network and backend error cases to user-facing UI text,
 * either as a string resource ID wrapped in [UiText.FromRes] or as raw text in [UiText.Text].
 *
 * - For network errors ([NetworkAndBackendErrors.Network]), specific error cases are mapped
 *   to string resource IDs.
 * - For backend errors ([NetworkAndBackendErrors.Backend]), the associated error message
 *   is directly wrapped in [UiText.Text].
 *
 * @receiver A [NetworkAndBackendErrors] instance to be converted.
 * @return A [UiText] instance representing the error in a format suitable for the UI.
 */
fun NetworkAndBackendErrors.asUiText(): UiText = when (this) {
    is NetworkAndBackendErrors.Network -> when (error) {
        NetworkErrors.NO_INTERNET -> R.string.common_network_error_verify_and_try_later
        NetworkErrors.UNKNOWN -> R.string.common_unexpected_error_verify_and_try_later
    }.let { UiText.FromRes(it) }

    is NetworkAndBackendErrors.Backend -> UiText.Text(message)
}
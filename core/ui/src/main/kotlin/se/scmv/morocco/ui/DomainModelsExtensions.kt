package se.scmv.morocco.ui

import se.scmv.morocco.domain.models.ShopCategory
import se.scmv.morocco.domain.models.ShopCategory.AUTO
import se.scmv.morocco.domain.models.ShopCategory.IMMO
import se.scmv.morocco.domain.models.ShopCategory.MISC

/**
 * Returns the string resource ID associated with the shop category.
 *
 * This function maps each [ShopCategory] to a specific string resource defined in `R.string`.
 *
 * @return The string resource ID for the corresponding category:
 * - [IMMO] -> `R.string.shop_category_immo`
 * - [AUTO] -> `R.string.shop_category_auto`
 * - [MISC] -> `R.string.shop_category_market`
 */
fun ShopCategory.resId(): Int = when (this) {
    IMMO -> R.string.shop_category_immo
    AUTO -> R.string.shop_category_auto
    MISC -> R.string.shop_category_market
}
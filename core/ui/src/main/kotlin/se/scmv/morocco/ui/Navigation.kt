package se.scmv.morocco.ui

import android.content.Context
import android.content.ContextWrapper
import androidx.activity.ComponentActivity
import androidx.compose.animation.AnimatedContentScope
import androidx.compose.animation.AnimatedContentTransitionScope
import androidx.compose.animation.core.tween
import androidx.compose.runtime.Composable
import androidx.navigation.NamedNavArgument
import androidx.navigation.NavBackStackEntry
import androidx.navigation.NavController
import androidx.navigation.NavDeepLink
import androidx.navigation.NavDestination
import androidx.navigation.NavDestination.Companion.hasRoute
import androidx.navigation.NavDestination.Companion.hierarchy
import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavOptions
import androidx.navigation.NavOptionsBuilder
import androidx.navigation.NavType
import androidx.navigation.Navigator
import androidx.navigation.compose.composable
import kotlin.reflect.KClass
import kotlin.reflect.KType

fun NavGraphBuilder.composableWithAnimation(
    route: String,
    arguments: List<NamedNavArgument> = emptyList(),
    duration: Int = 400,
    content: @Composable AnimatedContentScope.(NavBackStackEntry) -> Unit
) {
    composable(
        route = route,
        arguments = arguments,
        enterTransition = {
            slideIntoContainer(
                AnimatedContentTransitionScope.SlideDirection.Left,
                animationSpec = tween(duration)
            )
        },
        exitTransition = {
            slideOutOfContainer(
                AnimatedContentTransitionScope.SlideDirection.Left,
                animationSpec = tween(duration)
            )
        },
        popEnterTransition = {
            slideIntoContainer(
                AnimatedContentTransitionScope.SlideDirection.Right,
                animationSpec = tween(duration)
            )
        },
        popExitTransition = {
            slideOutOfContainer(
                AnimatedContentTransitionScope.SlideDirection.Right,
                animationSpec = tween(duration)
            )
        }
    ) {
        content(it)
    }
}

inline fun <reified T : Any> NavGraphBuilder.composableWithAnimation(
    typeMap: Map<KType, @JvmSuppressWildcards NavType<*>> = emptyMap(),
    deepLinks: List<NavDeepLink> = emptyList(),
    duration: Int = 400,
    crossinline composable: @Composable AnimatedContentScope.(NavBackStackEntry) -> Unit
) {
    composable<T>(
        typeMap = typeMap,
        deepLinks = deepLinks,
        enterTransition = {
            slideIntoContainer(
                AnimatedContentTransitionScope.SlideDirection.Left,
                animationSpec = tween(duration)
            )
        },
        exitTransition = {
            slideOutOfContainer(
                AnimatedContentTransitionScope.SlideDirection.Left,
                animationSpec = tween(duration)
            )
        },
        popEnterTransition = {
            slideIntoContainer(
                AnimatedContentTransitionScope.SlideDirection.Right,
                animationSpec = tween(duration)
            )
        },
        popExitTransition = {
            slideOutOfContainer(
                AnimatedContentTransitionScope.SlideDirection.Right,
                animationSpec = tween(duration)
            )
        }
    ) {
        composable(it)
    }
}

tailrec fun Context.findActivity(): ComponentActivity? = when (this) {
    is ComponentActivity -> this
    is ContextWrapper -> baseContext.findActivity()
    else -> null
}

fun NavDestination?.isRouteInHierarchy(
    route: KClass<*>
) = this?.hierarchy?.any { it.hasRoute(route) } ?: false

object NavCallbackRegistry {
    private val callbackMap = mutableMapOf<String, Any>()

    fun <T> registerCallback(key: String, callback: NavResultCallback<T>) {
        callbackMap[key] = callback
    }

    @Suppress("UNCHECKED_CAST")
    fun <T> getCallback(key: String): NavResultCallback<T>? {
        val callBack = callbackMap[key] as? NavResultCallback<T>
        unregisterCallback(key)
        return callBack
    }

    private fun unregisterCallback(key: String) {
        callbackMap.remove(key)
    }
}

typealias NavResultCallback<T> = (T) -> Unit

private fun generateNavResultCallbackKey(route: String?): String {
    return "NavResultCallbackKey_$route"
}

fun <T> NavController.setNavResultCallback(callback: NavResultCallback<T>) {
    val currentRouteId = currentBackStackEntry?.destination?.route
    val key = generateNavResultCallbackKey(currentRouteId)
    NavCallbackRegistry.registerCallback(key, callback)
}

fun <T> NavController.getNavResultCallback(): NavResultCallback<T>? {
    val previousRouteId = previousBackStackEntry?.destination?.route
    return NavCallbackRegistry.getCallback(generateNavResultCallbackKey(previousRouteId))
}

fun <T> NavController.popBackStackWithResult(result: T) {
    val callback = getNavResultCallback<T>()
    if (popBackStack()) {
        callback?.invoke(result)
    }
}

fun <T> NavController.navigateForResult(
    route: Any,
    navResultCallback: NavResultCallback<T>,
    navOptions: NavOptions? = null,
    navigatorExtras: Navigator.Extras? = null
) {
    setNavResultCallback(navResultCallback)
    navigate(route, navOptions, navigatorExtras)
}

fun <T> NavController.navigateForResult(
    route: Any,
    navResultCallback: NavResultCallback<T>,
    builder: NavOptionsBuilder.() -> Unit
) {
    setNavResultCallback(navResultCallback)
    navigate(route, builder)
}
package se.scmv.morocco.orion.components

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedIconButton
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import se.scmv.morocco.designsystem.components.avReadOnlyTextFieldColors
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.domain.models.orion.OrionBaseComponentValue
import se.scmv.morocco.domain.models.orion.OrionKeyStringValue
import se.scmv.morocco.domain.models.orion.OrionNumberCounter
import java.util.regex.Pattern

private const val VALUE_PLACEHOLDER = "--"

class OrionUiComponentNumberCounter(
    private val uiConfig: OrionNumberCounter,
    private val listener: OptionalItemsListener,
    initialValue: OrionKeyStringValue? = null,
) : OrionUiComponent(baseData = uiConfig.baseData) {

    // We use toIntOrNull() to make sure that value != VALUE_PLACEHOLDER .
    private var state by mutableStateOf(
        initialValue?.value?.toIntOrNull()?.toString() ?: VALUE_PLACEHOLDER
    )

    @Composable
    override fun Content(modifier: Modifier) {
        Column(
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small)
        ) {
            with(uiConfig.baseData) {
                IconAndTitleRow(title = title, required = required, iconUrl = iconUrl)
            }
            Row(
                modifier = modifier,
                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium),
                verticalAlignment = Alignment.CenterVertically
            ) {
                OutlinedIconButton(
                    onClick = ::decrement,
                    colors = IconButtonDefaults.outlinedIconButtonColors(
                        containerColor = MaterialTheme.colorScheme.background
                    ),
                    border = BorderStroke(0.1.dp, MaterialTheme.colorScheme.outline),
                ) {
                    Text(text = "-", style = MaterialTheme.typography.headlineSmall)
                }
                OutlinedTextField(
                    modifier = Modifier.weight(1f),
                    value = state,
                    onValueChange = {},
                    readOnly = true,
                    enabled = false,
                    colors = avReadOnlyTextFieldColors,
                    shape = MaterialTheme.shapes.medium
                )
                OutlinedIconButton(
                    onClick = ::increment,
                    colors = IconButtonDefaults.outlinedIconButtonColors(
                        containerColor = MaterialTheme.colorScheme.background
                    ),
                    border = BorderStroke(0.1.dp, MaterialTheme.colorScheme.outline),
                ) {
                    Text(text = "+", style = MaterialTheme.typography.headlineSmall)
                }
            }
        }
    }

    override fun validate(): Boolean {
        val valueInt = state.toIntOrNull() ?: -1
        val valueString = if (valueInt > -1) valueInt.toString() else ""
        uiConfig.baseData.validations.forEach {
            if (checkRegexRule(valueString, it.regex).not()) {
                error = UiText.Text(it.errorMessage)
                return false
            }
        }
        return true
    }

    override fun collectValue(): List<OrionBaseComponentValue> {
        // Make sure that value != VALUE_PLACEHOLDER .
        val valueInt: Int? = state.toIntOrNull()
        if (uiConfig.baseData.required.not() && valueInt == null) return emptyList()

        val value = requireNotNull(valueInt) {
            "OrionUiComponentNumberCounter: ${uiConfig.baseData.title} is required but not selected, make sure validate is called !"
        }
        return listOf(OrionKeyStringValue(id = uiConfig.baseData.id, value = value.toString()))
    }

    override fun resetValue() {
        state = VALUE_PLACEHOLDER
    }

    private fun decrement() {
        error = null
        val currentValue = state.toIntOrNull() ?: return
        if (currentValue > uiConfig.range.first) {
            state = currentValue.dec().toString()
            listener.onValueChanged(uiConfig, collectValue())
        }
    }

    private fun increment() {
        error = null
        val currentValue = state.toIntOrNull() ?: (uiConfig.range.first - 1)
        if (currentValue < uiConfig.range.second) {
            state = currentValue.inc().toString()
            listener.onValueChanged(uiConfig, collectValue())
        }
    }

    private fun checkRegexRule(text: String, regex: String): Boolean {
        val modifiedText = text.replace("\n", " ")
        val matcher = Pattern.compile(regex).matcher(modifiedText)
        return matcher.find()
    }
}
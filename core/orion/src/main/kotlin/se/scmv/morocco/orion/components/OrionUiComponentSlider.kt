package se.scmv.morocco.orion.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.util.fastMap
import kotlinx.collections.immutable.toPersistentList
import se.scmv.morocco.designsystem.components.AvDropdown
import se.scmv.morocco.designsystem.components.DropdownData
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.domain.models.orion.OrionBaseComponentValue
import se.scmv.morocco.domain.models.orion.OrionKeyRangeValue
import se.scmv.morocco.domain.models.orion.OrionSlider
import se.scmv.morocco.domain.models.orion.Sort
import se.scmv.morocco.orion.R

class OrionUiComponentSlider(
    private val uiConfig: OrionSlider,
    private val listener: OptionalItemsListener,
    initialValue: OrionKeyRangeValue? = null,
) : OrionUiComponent(baseData = uiConfig.baseData) {

    private val items = uiConfig.items
        .fastMap { DropdownData(id = it.id, name = it.name) }
        .toPersistentList()

    private var min by mutableIntStateOf(
        initialValue?.min?.let { minId ->
            items.indexOfFirst { item -> item.id == minId.toString() }
        } ?: -1
    )
    private var max by mutableIntStateOf(
        initialValue?.max?.let { maxId ->
            items.indexOfFirst { item -> item.id == maxId.toString() }
        } ?: -1
    )

    @Composable
    override fun Content(modifier: Modifier) {
        Column(
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small)
        ) {
            with(uiConfig.baseData) {
                IconAndTitleRow(iconUrl = iconUrl, title = title, required = required)
            }
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium),
                verticalAlignment = Alignment.CenterVertically
            ) {
                AvDropdown(
                    modifier = Modifier.weight(1f),
                    items = if (max > -1) {
                        when (uiConfig.orderOfSort) {
                            Sort.DESC -> items.sortedByDescending { it.name }
                                .subList(max, items.lastIndex + 1)

                            Sort.ASC -> items.subList(0, max + 1)
                        }
                    } else {
                        items
                    }.toPersistentList(),
                    selectedItem = items.getOrNull(min),
                    onItemSelected = {
                        min = items.indexOf(it)
                        listener.onValueChanged(uiConfig, collectValue())
                    },
                    placeholder = R.string.common_min
                )
                AvDropdown(
                    modifier = Modifier.weight(1f),
                    items = if (min > -1) {
                        when (uiConfig.orderOfSort) {
                            Sort.DESC -> items.sortedByDescending { it.name }.subList(0, min + 1)
                            Sort.ASC -> items.subList(min, items.lastIndex + 1)
                        }
                    } else {
                        items
                    }.toPersistentList(),
                    selectedItem = items.getOrNull(max),
                    onItemSelected = {
                        max = items.indexOf(it)
                        listener.onValueChanged(uiConfig, collectValue())
                    },
                    placeholder = R.string.common_max
                )
            }
        }
    }

    override fun validate(): Boolean {
        if (uiConfig.baseData.required.not()) return true

        return true
    }

    override fun collectValue(): List<OrionBaseComponentValue> {
        val minValue = items.getOrNull(min)?.id?.toIntOrNull()
        val maxValue = items.getOrNull(max)?.id?.toIntOrNull()
        return if (minValue != null && maxValue != null) {
            listOf(OrionKeyRangeValue(id = uiConfig.baseData.id, min = minValue, max = maxValue))
        } else emptyList()
    }

    override fun resetValue() {
        min = -1
        max = -1
    }
}
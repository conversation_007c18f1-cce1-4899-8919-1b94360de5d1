package se.scmv.morocco.orion.components.vas

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.LocalMinimumInteractiveComponentSize
import androidx.compose.material3.LocalRippleConfiguration
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.RadioButton
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.getValue
import androidx.compose.runtime.key
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import coil.ImageLoader
import coil.compose.rememberAsyncImagePainter
import coil.decode.GifDecoder
import coil.request.ImageRequest
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList
import se.scmv.morocco.designsystem.components.AvAlert
import se.scmv.morocco.designsystem.components.AvAlertType
import se.scmv.morocco.designsystem.components.AvDropdown
import se.scmv.morocco.designsystem.components.DropdownData
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.Blue900
import se.scmv.morocco.designsystem.theme.Brown500
import se.scmv.morocco.designsystem.theme.White
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.domain.models.PriceUnit
import se.scmv.morocco.domain.models.VasPack
import se.scmv.morocco.domain.models.VasPackage
import se.scmv.morocco.domain.models.roundOffDecimal
import se.scmv.morocco.orion.R
import java.time.LocalDate
import java.time.ZoneId
import java.time.format.TextStyle
import java.util.Locale

@Composable
fun VasPacks(
    modifier: Modifier = Modifier,
    packs: ImmutableList<VasPack>,
    isAdToBoostHasImage: Boolean,
    selectedPackageId: String,
    exeSlotsDays: ImmutableList<String>?,
    exeSlotsTimes: ImmutableList<DropdownData>?,
    selectedExeSlotsDay: String?,
    selectedExeSlotsTime: String?,
    onPackageSelected: (VasPack, VasPackage) -> Unit,
    onExeSlotDaySelected: (String) -> Unit,
    onExeSlotTimeSelected: (String) -> Unit,
    onAddImageClicked: () -> Unit
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.large)
    ) {
        if (exeSlotsDays != null && exeSlotsTimes != null) {
            VasPackageExecutionSlots(
                modifier = Modifier.fillMaxWidth(),
                exeSlotsDays = exeSlotsDays,
                exeSlotsTimes = exeSlotsTimes,
                selectedExeSlotsDay = selectedExeSlotsDay,
                selectedExeSlotsTime = exeSlotsTimes.firstOrNull { it.id == selectedExeSlotsTime },
                onExeSlotDaySelected = onExeSlotDaySelected,
                onExeSlotTimeSelected = onExeSlotTimeSelected
            )
        }
        packs.forEach { pack ->
            key(pack.key) {
                VasPack(
                    modifier = Modifier,
                    pack = pack,
                    isAdToBoostHasImage = isAdToBoostHasImage,
                    selectedPackageId = selectedPackageId,
                    onPackageSelected = onPackageSelected,
                    onAddImageClicked = onAddImageClicked
                )
            }
        }
    }
}

@OptIn(ExperimentalLayoutApi::class, ExperimentalMaterial3Api::class)
@Composable
private fun VasPack(
    modifier: Modifier = Modifier,
    pack: VasPack,
    isAdToBoostHasImage: Boolean,
    selectedPackageId: String,
    onPackageSelected: (VasPack, VasPackage) -> Unit,
    onAddImageClicked: () -> Unit
) {
    Card(
        modifier = modifier,
        shape = MaterialTheme.shapes.medium,
        elevation = CardDefaults.elevatedCardElevation(),
        border = if (pack.packages.any { it.id == selectedPackageId }) {
            BorderStroke(1.dp, MaterialTheme.colorScheme.primary)
        } else null
    ) {
        if (pack.showBestSellerBadge) {
            BestSellerBadge()
        }
        val painter = rememberAsyncImagePainter(
            model = ImageRequest.Builder(LocalContext.current)
                .data(pack.image)
                .build(),
            imageLoader = ImageLoader.Builder(LocalContext.current)
                .components {
                    // TODO Upgrade to coil3 and uncomment this code.
                    /*if (VERSION.SDK_INT >= 28) {
                        add(AnimatedImageDecoder.Factory())
                    } else {
                        add(GifDecoder.Factory())
                    }*/
                    add(GifDecoder.Factory())
                }
                .build()
        )
        Image(
            modifier = Modifier
                .fillMaxWidth()
                .aspectRatio(1.6f),
            painter = painter,
            contentDescription = null,
            contentScale = ContentScale.Crop
        )
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = MaterialTheme.dimens.large)
                .padding(vertical = MaterialTheme.dimens.big),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.large)
        ) {
            Text(
                text = pack.title,
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Bold,
            )
            Text(
                text = pack.description,
                style = MaterialTheme.typography.labelMedium,
            )
            CompositionLocalProvider(LocalMinimumInteractiveComponentSize provides MaterialTheme.dimens.none) {
                FlowRow(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    pack.vasLabels.forEach {
                        Text(
                            text = it,
                            style = MaterialTheme.typography.bodyMedium,
                        )
                    }
                }
            }
            HorizontalDivider(thickness = 0.5.dp)
            if (pack.requiresImage && isAdToBoostHasImage.not()) {
                ImageRequiredWarning(onBtnClick = onAddImageClicked)
            } else {
                Column(
                    verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.large)
                ) {
                    pack.packages.forEach {
                        CompositionLocalProvider(LocalRippleConfiguration provides null) {
                            VasPackage(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .clickable { onPackageSelected(pack, it) },
                                vasPackage = it,
                                selected = it.id == selectedPackageId,
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun BestSellerBadge() {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .background(Brown500)
            .padding(start = MaterialTheme.dimens.medium)
            .padding(vertical = MaterialTheme.dimens.small),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small)
    ) {
        Icon(
            modifier = Modifier.size(MaterialTheme.dimens.default),
            painter = painterResource(R.drawable.ic_best_seller),
            contentDescription = null,
            tint = White
        )
        Text(
            stringResource(R.string.choose_vas_package_screen_best_seller_badge),
            color = White,
            style = MaterialTheme.typography.labelMedium
        )
    }
}

@Composable
fun ImageRequiredWarning(onBtnClick: () -> Unit) {
    Column(
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.large),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        OutlinedButton(
            onClick = onBtnClick,
            border = BorderStroke(1.dp, MaterialTheme.colorScheme.primary)
        ) {
            Text(text = stringResource(R.string.choose_vas_package_screen_no_image_button))
        }
        AvAlert(
            text = stringResource(R.string.choose_vas_package_screen_no_image_warning),
            type = AvAlertType.Warning
        )
    }
}

@Composable
private fun VasPackage(
    modifier: Modifier = Modifier,
    vasPackage: VasPackage,
    selected: Boolean,
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically
    ) {
        RadioButton(selected = selected, onClick = null)
        Spacer(modifier = Modifier.width(MaterialTheme.dimens.large))
        Column(
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small)
        ) {
            Text(
                text = String.format(
                    Locale.getDefault(),
                    "%s %s",
                    vasPackage.durationDays,
                    stringResource(R.string.days)
                ),
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Bold
            )
            Text(
                text = pricePerDay(
                    price = vasPackage.price,
                    priceUnit = vasPackage.priceUnit,
                    durationDays = vasPackage.durationDays
                ),
                style = MaterialTheme.typography.labelMedium,
            )
        }
        Spacer(modifier = Modifier.weight(1f))
        Text(
            text = priceWithUnit(price = vasPackage.price, priceUnit = vasPackage.priceUnit),
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Bold
        )
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun VasPackageExecutionSlots(
    modifier: Modifier = Modifier,
    exeSlotsDays: ImmutableList<String>,
    exeSlotsTimes: ImmutableList<DropdownData>,
    selectedExeSlotsDay: String?,
    selectedExeSlotsTime: DropdownData?,
    onExeSlotDaySelected: (String) -> Unit,
    onExeSlotTimeSelected: (String) -> Unit,
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.large)
    ) {
        Spacer(Modifier.height(MaterialTheme.dimens.large))
        Text(
            text = stringResource(R.string.choose_vas_package_screen_exe_slots_step_1),
            style = MaterialTheme.typography.bodyMedium,
        )
        Text(
            text = stringResource(R.string.choose_vas_package_screen_exe_slots_picker_title),
            style = MaterialTheme.typography.bodyLarge,
            color = Blue900,
            fontWeight = FontWeight.Bold
        )
        Row(
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(painter = painterResource(R.drawable.ic_date_picker), contentDescription = null)
            Text(
                text = stringResource(R.string.choose_vas_package_screen_exe_slots_date),
                style = MaterialTheme.typography.labelLarge
            )
        }
        CompositionLocalProvider(LocalMinimumInteractiveComponentSize provides MaterialTheme.dimens.none) {
            FlowRow(
                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium)
            ) {
                exeSlotsDays.forEach { day ->
                    val localDate = LocalDate.parse(day)
                        .atStartOfDay()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDate()
                    VasPackageExecutionSlotsDay(
                        dayOfWeek = localDate.dayOfWeek.getDisplayName(
                            TextStyle.SHORT,
                            Locale.getDefault()
                        ),
                        dayOfMonth = localDate.dayOfMonth.toString(),
                        selected = day == selectedExeSlotsDay,
                        onClick = { onExeSlotDaySelected(day) }
                    )
                }
            }
        }
        Row(
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(painter = painterResource(R.drawable.ic_time_picker), contentDescription = null)
            Text(
                text = stringResource(R.string.choose_vas_package_screen_exe_slots_time),
                style = MaterialTheme.typography.labelLarge
            )
        }
        AvDropdown(
            modifier = Modifier.fillMaxWidth(),
            items = exeSlotsTimes,
            selectedItem = selectedExeSlotsTime,
            onItemSelected = { onExeSlotTimeSelected(it.id) }
        )
        Spacer(Modifier.height(MaterialTheme.dimens.large))
        HorizontalDivider()
        Spacer(Modifier.height(MaterialTheme.dimens.large))
        Text(
            text = stringResource(R.string.choose_vas_package_screen_exe_slots_step_2),
            style = MaterialTheme.typography.bodyMedium,
        )
        Text(
            text = stringResource(R.string.choose_vas_package_screen_package_select_title),
            style = MaterialTheme.typography.bodyLarge,
            color = Blue900,
            fontWeight = FontWeight.Bold
        )
    }
}

@Composable
fun VasPackageExecutionSlotsDay(
    modifier: Modifier = Modifier,
    dayOfWeek: String,
    dayOfMonth: String,
    selected: Boolean,
    onClick: () -> Unit
) {
    Card(
        modifier = modifier,
        shape = CircleShape,
        onClick = onClick,
        border = if (selected) BorderStroke(1.dp, MaterialTheme.colorScheme.primary) else null
    ) {
        Column(
            modifier = Modifier.padding(MaterialTheme.dimens.medium),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(text = dayOfWeek, style = MaterialTheme.typography.labelMedium)
            Text(text = dayOfMonth, style = MaterialTheme.typography.bodyLarge)
        }
    }
}

@Composable
private fun pricePerDay(price: Int, priceUnit: PriceUnit, durationDays: Int): String {
    val pricePerDay = if (durationDays > 0) {
        price.toDouble().div(durationDays)
    } else price.toDouble()
    return String.format(
        Locale.getDefault(),
        "%s %s/%s",
        pricePerDay.roundOffDecimal(1),
        stringResource(priceUnit.displayedName()),
        stringResource(R.string.common_per_day_vas_unit)
    )
}

@Composable
private fun priceWithUnit(price: Int, priceUnit: PriceUnit) = String.format(
    Locale.getDefault(),
    "%s %s",
    price,
    stringResource(priceUnit.displayedName()),
)

fun PriceUnit.displayedName(): Int = when (this) {
    PriceUnit.DHS -> R.string.common_currency
    PriceUnit.POINTS -> R.string.common_points
    PriceUnit.AVITO_TOKEN -> R.string.common_avitoken
}

@Preview
@Composable
private fun VasPacksPreview() {
    AvitoTheme {
        Surface {
            var selectedVasPackage by remember { mutableStateOf<VasPackage?>(null) }
            VasPacks(
                modifier = Modifier
                    .fillMaxWidth()
                    .verticalScroll(rememberScrollState())
                    .padding(MaterialTheme.dimens.default),
                packs = List(3) { pack ->
                    VasPack(
                        key = "key $pack",
                        title = "Some fake vas package title",
                        description = "Some fake vas package description, Some fake vas package description",
                        image = "novum",
                        vasLabels = listOf("Label 1", "Label 2"),
                        packages = List(3) {
                            VasPackage(
                                id = "key $pack $it",
                                durationDays = 3587,
                                price = 7584,
                                priceUnit = PriceUnit.DHS
                            )
                        },
                        showBestSellerBadge = pack == 0,
                        requiresImage = pack == 1
                    )
                }.toImmutableList(),
                isAdToBoostHasImage = false,
                selectedPackageId = selectedVasPackage?.id.orEmpty(),
                exeSlotsDays = persistentListOf(
                    "2025-03-18",
                    "2025-03-19",
                    "2025-03-20",
                    "2025-03-21",
                    "2025-03-22",
                    "2025-03-23",
                    "2025-03-24"
                ),
                exeSlotsTimes = persistentListOf(
                    DropdownData(id = "1", name = "00:00-01:00"),
                    DropdownData(id = "2", name = "01:00-02:00"),
                    DropdownData(id = "3", name = "02:00-03:00"),
                    DropdownData(id = "4", name = "03:00-04:00"),
                    DropdownData(id = "5", name = "04:00-05:00"),
                    DropdownData(id = "6", name = "05:00-06:00"),
                ),
                selectedExeSlotsDay = "2025-03-20",
                selectedExeSlotsTime = "2",
                onPackageSelected = { _, it ->
                    selectedVasPackage = it
                },
                onExeSlotDaySelected = {},
                onExeSlotTimeSelected = {},
                onAddImageClicked = {}
            )
        }
    }
}
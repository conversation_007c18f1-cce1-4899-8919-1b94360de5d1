package se.scmv.morocco.orion.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Card
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import coil.decode.SvgDecoder
import coil.request.ImageRequest
import se.scmv.morocco.designsystem.components.AvAlert
import se.scmv.morocco.designsystem.components.AvAlertType
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.domain.models.orion.OrionBaseComponentData
import se.scmv.morocco.domain.models.orion.OrionBaseComponentValue
import se.scmv.morocco.orion.presentation.OrionFiltersValueListener

// TODO mark OrionUiComponent and the subclasses with @Stable.
abstract class OrionUiComponent(
    val baseData: OrionBaseComponentData,
): OrionFiltersValueListener {

    protected var isVisible by mutableStateOf(baseData.dependencies.isEmpty())
    protected var error by mutableStateOf<UiText?>(null)

    @Composable
    fun Display(modifier: Modifier) {
        if (isVisible) {
            Column(
                modifier = modifier,
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium)
            ) {
                Spacer(
                    modifier = Modifier.height(
                        if (this@OrionUiComponent is OrionUiComponentGroupHeader) {
                            MaterialTheme.dimens.default
                        } else {
                            MaterialTheme.dimens.small
                        }
                    )
                )
                Content(modifier = Modifier.fillMaxWidth())
                val errorMsg = error
                AnimatedVisibility(
                    visible = errorMsg != null
                ) {
                    AvAlert(
                        text = errorMsg?.getValue(LocalContext.current).orEmpty(),
                        type = AvAlertType.Error
                    )
                }
            }
        }
    }

    @Composable
    abstract fun Content(modifier: Modifier)

    // TODO Generate kdoc for all the implementation functions
    abstract fun validate(): Boolean

    // TODO Generate kdoc for all the implementation functions
    abstract fun collectValue(): List<OrionBaseComponentValue>

    open fun resetValue() {}

    override fun onValuesChanged(values: List<OrionBaseComponentValue>) {
        baseData.dependencies.forEach { dependency ->
            val dependencyValue = values.firstOrNull { it.id == dependency.dependsOn }
            // TODO Check the type of dependency later when we've more types of dependencies.
            isVisible = dependencyValue != null
        }
    }

    @Composable
    protected fun IconAndTitleRow(
        modifier: Modifier = Modifier,
        iconUrl: String,
        title: String,
        required: Boolean
    ) {
        if (title.isNotBlank()) {
            Row(
                modifier = modifier,
                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small),
                verticalAlignment = Alignment.CenterVertically
            ) {
                if (iconUrl.isNotBlank()) {
                    Icon(url = iconUrl)
                }
                Title(text = title, required = required)
            }
        }
    }

    @Composable
    protected fun Icon(
        modifier: Modifier = Modifier,
        url: String
    ) {
        if (url.isBlank()) return
        Card(
            modifier = modifier.size(35.dp),
            shape = CircleShape,
        ) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                AsyncImage(
                    modifier = Modifier.size(24.dp),
                    model = ImageRequest.Builder(LocalContext.current)
                        .decoderFactory(SvgDecoder.Factory())
                        .data(url)
                        .build(),
                    contentDescription = null,
                    colorFilter = ColorFilter.tint(color = MaterialTheme.colorScheme.onBackground)
                )
            }
        }
    }

    @Composable
    protected fun Title(modifier: Modifier = Modifier, text: String, required: Boolean) {
        val annotatedText = buildAnnotatedString {
            append(text)
            if (required) {
                withStyle(
                    style = SpanStyle(
                        color = MaterialTheme.colorScheme.error,
                        fontSize = MaterialTheme.typography.titleLarge.fontSize,
                    )
                ) {
                    append(" *")
                }
            }
        }
        Text(
            modifier = modifier,
            text = annotatedText,
            style = MaterialTheme.typography.bodyLarge,
            textAlign = TextAlign.Center
        )
    }
}
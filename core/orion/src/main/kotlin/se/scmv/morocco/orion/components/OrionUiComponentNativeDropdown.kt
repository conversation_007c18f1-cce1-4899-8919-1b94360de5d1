package se.scmv.morocco.orion.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.util.fastFirstOrNull
import androidx.compose.ui.util.fastMap
import kotlinx.collections.immutable.toPersistentList
import se.scmv.morocco.designsystem.components.AvDropdown
import se.scmv.morocco.designsystem.components.DropdownData
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.domain.models.orion.OrionBaseComponentValue
import se.scmv.morocco.domain.models.orion.OrionKeyStringValue
import se.scmv.morocco.domain.models.orion.OrionNativeDropdown
import java.util.regex.Pattern

class OrionUiComponentNativeDropdown(
    private val uiConfig: OrionNativeDropdown,
    private val listener: OptionalItemsListener,
    initialValue: OrionKeyStringValue? = null
) : OrionUiComponent(baseData = uiConfig.baseData) {

    private val items = uiConfig.items.fastMap {
        DropdownData(id = it.id, name = it.name)
    }.toPersistentList()
    private var selectedItem by mutableStateOf(
        items.fastFirstOrNull { it.id == initialValue?.value }
            ?: items.fastFirstOrNull { it.id == uiConfig.potentialValue?.value }
    )

    @Composable
    override fun Content(modifier: Modifier) {
        Column(
            modifier = modifier,
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small)
        ) {
            with(uiConfig.baseData) {
                IconAndTitleRow(iconUrl = iconUrl, title = title, required = required)
            }
            AvDropdown(
                modifier = Modifier.fillMaxWidth(),
                items = items,
                selectedItem = selectedItem,
                onItemSelected = {
                    error = null
                    selectedItem = it
                    listener.onValueChanged(uiConfig, collectValue())
                }
            )
        }
    }

    override fun validate(): Boolean {
        if (uiConfig.baseData.required.not()) return true

        uiConfig.baseData.validations.forEach { validation ->
            val isValid = checkRegexRule(selectedItem?.name.orEmpty(), validation.regex)
            if (isValid.not()) {
                error = UiText.Text(validation.errorMessage)
                return false
            }
        }
        return true
    }

    override fun collectValue(): List<OrionBaseComponentValue> {
        if (uiConfig.baseData.required.not() && selectedItem == null) return emptyList()

        val value = requireNotNull(selectedItem) {
            "OrionUiComponentNativeDropdown: ${uiConfig.baseData.title} is required but not selected, make sure validate is called !"
        }
        return listOf(OrionKeyStringValue(id = uiConfig.baseData.id, value = value.id))
    }

    override fun resetValue() {
        selectedItem = null
    }

    private fun checkRegexRule(text: String, regex: String): Boolean {
        val modifiedText = text.replace("\n", " ")
        val matcher = Pattern.compile(regex).matcher(modifiedText)
        return matcher.find()
    }
}
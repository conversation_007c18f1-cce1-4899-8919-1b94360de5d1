package se.scmv.morocco.orion.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.expandVertically
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ContextualFlowRow
import androidx.compose.foundation.layout.ContextualFlowRowOverflow
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowRight
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.Checkbox
import androidx.compose.material3.CheckboxDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.InputChip
import androidx.compose.material3.InputChipDefaults
import androidx.compose.material3.LocalMinimumInteractiveComponentSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.SheetState
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.util.fastFirstOrNull
import kotlinx.collections.immutable.PersistentList
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import se.scmv.morocco.common.extensions.normalized
import se.scmv.morocco.designsystem.components.AvPrimaryButton
import se.scmv.morocco.designsystem.components.AvSecondaryButton
import se.scmv.morocco.designsystem.components.AvTextField
import se.scmv.morocco.designsystem.components.avOutlinedTextFieldColors
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.domain.models.orion.OrionBaseComponentValue
import se.scmv.morocco.domain.models.orion.OrionMultipleSelectSmartDropDown
import se.scmv.morocco.domain.models.orion.OrionMultipleSelectSmartDropDownValue
import se.scmv.morocco.domain.models.orion.OrionSmartDropDownItem
import se.scmv.morocco.orion.R

// Data classes for search results
data class SearchableItem(
    val item: OrionSmartDropDownItem,
    val isParent: Boolean,
    val parent: OrionSmartDropDownItem? = null,
    val matchScore: Float = 0f,
    val matchedText: String = ""
)

class OrionUiComponentMultipleSelectSmartDropdown(
    private val uiConfig: OrionMultipleSelectSmartDropDown,
    private val initialValue: OrionMultipleSelectSmartDropDownValue? = null,
    private val listener: OptionalItemsListener,
) : OrionUiComponent(baseData = uiConfig.baseData) {

    private var selectedParents by mutableStateOf(getInitialSelectedParent())
    private var selectedChildren by mutableStateOf(getInitialSelectedChildren())

    @OptIn(ExperimentalMaterial3Api::class)
    @Composable
    override fun Content(modifier: Modifier) {
        val scope = rememberCoroutineScope()
        var showCategoriesBtmSheet by remember { mutableStateOf(false) }
        Column(
            modifier = modifier,
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small)
        ) {
            with(uiConfig.baseData) {
                Title(text = title, required = required)
            }
            DropDownButton {
                showCategoriesBtmSheet = true
            }
        }
        if (showCategoriesBtmSheet) {
            val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)
            DropdownBtmSheet(
                sheetState = sheetState,
                onDismiss = {
                    scope.launch { sheetState.hide() }.invokeOnCompletion {
                        showCategoriesBtmSheet = false
                    }
                },
                onValidate = { parents, children ->
                    selectedParents = parents
                    selectedChildren = children
                    listener.onValueChanged(uiConfig, collectValue())
                }
            )
        }
    }

    override fun validate(): Boolean {
        if (uiConfig.baseData.required && (selectedParents.isEmpty() || selectedChildren.isEmpty())) {
            error = UiText.FromRes(R.string.common_field_required_error)
            return false
        }

        return true
    }

    override fun collectValue(): List<OrionBaseComponentValue> {
        if (uiConfig.baseData.required.not() && selectedParents.isEmpty()) return emptyList()

        return listOf(
            OrionMultipleSelectSmartDropDownValue(
                id = uiConfig.baseData.id,
                parents = selectedParents.toList(),
                childId = uiConfig.childBaseData.id,
                children = selectedChildren.toList()
            ),
        )
    }

    override fun resetValue() {
        selectedParents = setOf()
        selectedChildren = setOf()
    }

    @OptIn(ExperimentalLayoutApi::class, ExperimentalMaterial3Api::class)
    @Composable
    private fun DropDownButton(onClick: () -> Unit) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .border(
                    width = 1.dp,
                    color = MaterialTheme.colorScheme.outline,
                    shape = MaterialTheme.shapes.small
                )
                .clip(shape = MaterialTheme.shapes.small)
                .clickable(onClick = onClick)
                .padding(all = MaterialTheme.dimens.large),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(
                modifier = Modifier.width(IntrinsicSize.Max),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small)
            ) {
                Icon(url = uiConfig.baseData.iconUrl)
                Text(
                    text = stringResource(
                        R.string.listing_filters_choose,
                        uiConfig.baseData.title.lowercase()
                    ),
                    style = MaterialTheme.typography.bodyMedium,
                )
            }
            Icon(
                modifier = Modifier.width(IntrinsicSize.Min),
                imageVector = Icons.AutoMirrored.Filled.KeyboardArrowRight,
                contentDescription = "Open smart dropdown icon"
            )
        }
        SelectedItemsNames(
            selectedParents = selectedParents,
            selectedChildren = selectedChildren,
            onRemoveParent = {
                selectedParents -= it
                listener.onValueChanged(uiConfig, collectValue())
            },
            onRemoveChild = {
                selectedChildren -= it
                listener.onValueChanged(uiConfig, collectValue())
            }
        )
    }

    @OptIn(ExperimentalMaterial3Api::class, kotlinx.coroutines.FlowPreview::class)
    @Composable
    private fun DropdownBtmSheet(
        sheetState: SheetState,
        onDismiss: () -> Unit,
        onValidate: (Set<OrionSmartDropDownItem>, Set<OrionSmartDropDownItem>) -> Unit,
    ) {
        var tempSelectedParents by remember { mutableStateOf(selectedParents) }
        var tempSelectedChildren by remember { mutableStateOf(selectedChildren) }

        // Search state management
        var searchText by remember { mutableStateOf("") }
        var displayItems by remember { mutableStateOf(uiConfig.items) }
        val searchFlow = remember { MutableStateFlow("") }
        val scope = rememberCoroutineScope()

        val lazyListState = rememberLazyListState()

        // Search effect with debouncing
        LaunchedEffect(Unit) {
            searchFlow
                .debounce(300)
                .collect { query: String ->
                    scope.launch(Dispatchers.Default) {
                        val results = if (query.isBlank()) {
                            emptyList()
                        } else {
                            performFuzzySearch(query, uiConfig.items)
                        }

                        withContext(Dispatchers.Main) {
                            displayItems = if (query.isBlank()) {
                                uiConfig.items
                            } else {
                                // Group search results back into parent-child structure
                                groupSearchResults(results)
                            }
                            lazyListState.scrollToItem(0)
                        }
                    }
                }
        }
        ModalBottomSheet(
            modifier = Modifier
                .fillMaxSize()
                .statusBarsPadding(),
            onDismissRequest = { onDismiss() },
            sheetState = sheetState,
            containerColor = MaterialTheme.colorScheme.background
        ) {
            Column(modifier = Modifier.fillMaxSize()) {
                DropdownBtmSheetHeader(onCloseClicked = onDismiss)
                HorizontalDivider()
                Column(
                    modifier = Modifier
                        .padding(
                            horizontal = MaterialTheme.dimens.screenPaddingHorizontal,
                            vertical = MaterialTheme.dimens.screenPaddingVertical
                        )
                        .fillMaxSize(),
                    verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium)
                ) {
                    DropdownBtmSheetSearchView(
                        text = searchText,
                        onSearch = { text ->
                            if (text != searchText) {
                                searchText = text
                                searchFlow.value = text
                            }
                        }
                    )
                    SelectedItemsNames(
                        selectedParents = tempSelectedParents,
                        selectedChildren = tempSelectedChildren,
                        onRemoveParent = { tempSelectedParents -= it },
                        onRemoveChild = { tempSelectedChildren -= it }
                    )
                    if (searchText.isNotEmpty() && displayItems.isEmpty()) {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .weight(1f),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = stringResource(R.string.common_search_no_results),
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    } else {
                        // State to track which parent is currently expanded (accordion behavior)
                        var expandedParentId by remember { mutableStateOf<String?>(null) }
                        LazyColumn(
                            modifier = Modifier
                                .fillMaxWidth()
                                .weight(1f),
                            state = lazyListState
                        ) {
                            items(
                                items = displayItems,
                                key = { it.id + it.name }
                            ) { item ->
                                DropdownItem(
                                    name = item.name,
                                    checked = tempSelectedParents.any { it.id == item.id },
                                    onCheckChange = { checked ->
                                        if (checked.not()) {
                                            tempSelectedParents -= item
                                            val children = item.children.filter {
                                                it in tempSelectedChildren
                                            }
                                            tempSelectedChildren =
                                                tempSelectedChildren.minus(children)
                                        } else {
                                            tempSelectedParents += item
                                            expandedParentId = item.id
                                        }
                                    },
                                    children = item.children.toPersistentList(),
                                    selectedChildren = tempSelectedChildren.toPersistentList(),
                                    onChildCheckChange = { id, checked ->
                                        if (!tempSelectedParents.any { it.id == item.id }) {
                                            tempSelectedParents += item
                                        }
                                        if (checked.not()) {
                                            tempSelectedChildren -= id
                                        } else {
                                            tempSelectedChildren += id
                                        }
                                    },
                                    searchQuery = searchText,
                                    isExpanded = expandedParentId == item.id || searchText.isNotEmpty(),
                                    onExpandChange = { shouldExpand ->
                                        expandedParentId = if (shouldExpand) item.id else null
                                    }
                                )
                            }
                        }
                    }
                    DropdownBtmSheetFooter(
                        onClearClicked = {
                            tempSelectedParents = setOf()
                            tempSelectedChildren = setOf()
                        },
                        onApplyClicked = {
                            onValidate(tempSelectedParents, tempSelectedChildren)
                            onDismiss()
                        }
                    )
                }
            }
        }
    }

    @Composable
    @OptIn(ExperimentalMaterial3Api::class)
    private fun DropdownBtmSheetHeader(onCloseClicked: () -> Unit) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(TopAppBarDefaults.TopAppBarExpandedHeight),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Spacer(modifier = Modifier.weight(1f))
            Text(text = uiConfig.baseData.title)
            Spacer(modifier = Modifier.weight(1f))
            IconButton(
                onClick = { onCloseClicked() }
            ) {
                Icon(
                    imageVector = Icons.Default.Close,
                    contentDescription = "Close button"
                )
            }
        }
    }

    @Composable
    private fun DropdownBtmSheetFooter(
        onClearClicked: () -> Unit,
        onApplyClicked: () -> Unit
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = MaterialTheme.dimens.default)
                .systemBarsPadding(),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium),
            verticalAlignment = Alignment.CenterVertically
        ) {
            AvSecondaryButton(
                modifier = Modifier.weight(1f),
                text = stringResource(R.string.common_clear),
                onClick = onClearClicked
            )
            AvPrimaryButton(
                modifier = Modifier.weight(1f),
                text = stringResource(R.string.common_apply),
                onClick = onApplyClicked
            )
        }
    }

    @Composable
    @OptIn(ExperimentalLayoutApi::class)
    private fun SelectedItemsNames(
        selectedParents: Set<OrionSmartDropDownItem>,
        selectedChildren: Set<OrionSmartDropDownItem>,
        onRemoveParent: (OrionSmartDropDownItem) -> Unit,
        onRemoveChild: (OrionSmartDropDownItem) -> Unit
    ) {
        val selectedItems = selectedParents.map { parent ->
            val children = parent.children.filter { child ->
                selectedChildren.any { it.id == child.id }
            }
            if (children.isEmpty()) {
                listOf(parent to null)
            } else {
                children.map { parent to it }
            }
        }.flatten()
        CompositionLocalProvider(LocalMinimumInteractiveComponentSize provides 0.dp) {
            ContextualFlowRow(
                itemCount = selectedItems.size,
                maxLines = 2,
                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small),
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small),
                overflow = ContextualFlowRowOverflow.expandIndicator {
                    val leftItems = totalItemCount - shownItemCount
                    if (leftItems > 0) {
                        Box(
                            modifier = Modifier
                                .size(InputChipDefaults.Height)
                                .background(
                                    color = MaterialTheme.colorScheme.primary.copy(alpha = 0.1f),
                                    shape = CircleShape
                                )
                                .border(
                                    width = 1.dp,
                                    color = MaterialTheme.colorScheme.primary,
                                    shape = CircleShape
                                ),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = "+$leftItems",
                                style = MaterialTheme.typography.labelMedium,
                                color = MaterialTheme.colorScheme.primary
                            )
                        }
                    }
                }
            ) { index ->
                selectedItems.getOrNull(index)?.let { (parent, child) ->
                    InputChip(
                        selected = false,
                        enabled = false,
                        label = {
                            Text(
                                text = child?.let { "${parent.name} - ${it.name}" } ?: parent.name,
                                style = MaterialTheme.typography.labelMedium
                            )
                        },
                        trailingIcon = {
                            IconButton(
                                modifier = Modifier.size(MaterialTheme.dimens.big),
                                onClick = {
                                    if (child != null) {
                                        onRemoveChild(child)
                                    } else {
                                        onRemoveParent(parent)
                                    }
                                }
                            ) {
                                Icon(
                                    modifier = Modifier.size(MaterialTheme.dimens.big),
                                    imageVector = Icons.Default.Close,
                                    contentDescription = "Remove item"
                                )
                            }
                        },
                        shape = MaterialTheme.shapes.large,
                        colors = InputChipDefaults.inputChipColors(
                            disabledLabelColor = MaterialTheme.colorScheme.primary,
                            disabledContainerColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.1f),
                            disabledTrailingIconColor = MaterialTheme.colorScheme.primary
                        ),
                        border = InputChipDefaults.inputChipBorder(
                            enabled = false,
                            selected = false,
                            borderColor = MaterialTheme.colorScheme.primary,
                            disabledBorderColor = MaterialTheme.colorScheme.primary,
                            selectedBorderColor = MaterialTheme.colorScheme.primary
                        ),
                        onClick = {}
                    )
                }
            }
        }
    }

    @Composable
    private fun DropdownBtmSheetSearchView(
        modifier: Modifier = Modifier,
        text: String,
        onSearch: (String) -> Unit
    ) {
        val focusManager = LocalFocusManager.current
        AvTextField(
            modifier = modifier.fillMaxWidth(),
            value = text,
            onValueChanged = onSearch,
            placeholder = R.string.common_search,
            leadingIcon = {
                Icon(
                    imageVector = Icons.Default.Search,
                    contentDescription = null
                )
            },
            trailingIcon = {
                if (text.isNotBlank()) {
                    IconButton(onClick = { onSearch("") }) {
                        Icon(
                            modifier = Modifier.size(MaterialTheme.dimens.big),
                            imageVector = Icons.Default.Clear,
                            contentDescription = null
                        )
                    }
                }
            },
            keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
            keyboardActions = KeyboardActions(
                onDone = { focusManager.clearFocus() }
            ),
            shape = CircleShape,
            colors = avOutlinedTextFieldColors
        )
    }

    @Composable
    private fun DropdownItem(
        modifier: Modifier = Modifier,
        name: String,
        checked: Boolean,
        onCheckChange: (Boolean) -> Unit,
        children: PersistentList<OrionSmartDropDownItem>,
        selectedChildren: PersistentList<OrionSmartDropDownItem>,
        onChildCheckChange: (OrionSmartDropDownItem, Boolean) -> Unit,
        searchQuery: String = "",
        isExpanded: Boolean = false,
        onExpandChange: (Boolean) -> Unit = {}
    ) {
        val hasChildren = remember { children.size > 1 }
        var expandMore by remember { mutableStateOf(false) }
        // Reset expandMore when parent collapses
        LaunchedEffect(isExpanded) {
            if (!isExpanded) {
                expandMore = false
            }
        }

        //Unchecked parents should always be collapsed
        LaunchedEffect(checked) {
            if (!checked && isExpanded) {
                onExpandChange(false)
            }
        }
        Column(
            modifier = modifier.fillMaxWidth(),
            verticalArrangement = Arrangement.Center
        ) {
            Row(
                modifier = modifier
                    .fillMaxWidth()
                    .clickable {
                        // Click on parent (Column/Row) toggles checkbox
                        onCheckChange(!checked)
                    },
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                DropdownItemInfo(
                    name = name,
                    checked = checked,
                    onCheckChange = onCheckChange,
                    searchQuery = searchQuery
                )
                if (hasChildren) {
                    IconButton(
                        onClick = {
                            if (isExpanded) {
                                // Only collapse, no check changes
                                onExpandChange(false)
                            } else {
                                // Auto-check parent if not checked, then expand
                                if (!checked) {
                                    onCheckChange(true)
                                }
                                onExpandChange(true)
                            }
                            expandMore = false
                        }
                    ) {
                        Icon(
                            imageVector = if (isExpanded) {
                                Icons.Default.KeyboardArrowUp
                            } else Icons.Default.KeyboardArrowDown,
                            contentDescription = "Show children icon"
                        )
                    }
                }
            }
            if (hasChildren) {
                // Pre-calculate children lists to avoid repeated expensive operations
                val (visibleChildren, hiddenChildren) = remember(children) {
                    if (children.size <= 4) {
                        children to emptyList()
                    } else {
                        children.take(4) to children.drop(4)
                    }
                }
                val hasHiddenChildren by remember { derivedStateOf { hiddenChildren.isNotEmpty() } }
                AnimatedVisibility(isExpanded) {
                    Column(
                        modifier = modifier
                            .fillMaxWidth()
                            .padding(start = MaterialTheme.dimens.large)
                    ) {
                        // Always visible children (first 4)
                        visibleChildren.forEach { item ->
                            DropdownItemInfo(
                                name = item.name,
                                checked = selectedChildren.any { it.id == item.id },
                                onCheckChange = { onChildCheckChange(item, it) },
                                searchQuery = searchQuery
                            )
                        }

                        // Additional children with smooth animation
                        if (hasHiddenChildren) {
                            AnimatedVisibility(
                                visible = expandMore,
                                enter = expandVertically(),
                                exit = shrinkVertically()
                            ) {
                                Column {
                                    hiddenChildren.forEach { item ->
                                        DropdownItemInfo(
                                            name = item.name,
                                            checked = selectedChildren.any { it.id == item.id },
                                            onCheckChange = { onChildCheckChange(item, it) },
                                            searchQuery = searchQuery
                                        )
                                    }
                                }
                            }
                            if (!expandMore) {
                                Text(
                                    modifier = Modifier
                                        .padding(start = MaterialTheme.dimens.betweenRegularDefault)
                                        .clickable(
                                            interactionSource = remember { MutableInteractionSource() },
                                            indication = null
                                        ) { expandMore = true },
                                    text = stringResource(R.string.common_see_more),
                                    style = MaterialTheme.typography.labelLarge,
                                    textDecoration = TextDecoration.Underline
                                )
                            }
                        }
                    }
                }
            }
        }
    }

    @Composable
    private fun DropdownItemInfo(
        name: String,
        checked: Boolean,
        onCheckChange: (Boolean) -> Unit,
        searchQuery: String = ""
    ) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            Checkbox(
                checked = checked,
                onCheckedChange = onCheckChange,
                colors = CheckboxDefaults.colors(
                    uncheckedColor = MaterialTheme.colorScheme.outline
                )
            )
            Text(
                text = if (searchQuery.isNotEmpty()) {
                    createHighlightedText(name, searchQuery)
                } else {
                    buildAnnotatedString { append(name) }
                },
                style = MaterialTheme.typography.bodyMedium,
                overflow = TextOverflow.Ellipsis
            )
        }
    }

    @Composable
    private fun createHighlightedText(text: String, query: String) = buildAnnotatedString {
        val normalizedText = text.normalized()
        val normalizedQuery = query.normalized()
        val startIndex = normalizedText.indexOf(normalizedQuery, ignoreCase = true)

        if (startIndex >= 0) {
            // Add text before match
            append(text.substring(0, startIndex))

            // Add highlighted match
            withStyle(
                style = SpanStyle(
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary
                )
            ) {
                append(text.substring(startIndex, startIndex + query.length))
            }

            // Add text after match
            append(text.substring(startIndex + query.length))
        } else {
            append(text)
        }
    }


    private fun getInitialSelectedParent(): Set<OrionSmartDropDownItem> {
        return initialValue?.parents?.mapNotNull { parentId ->
            uiConfig.items.fastFirstOrNull { it.id == parentId.id }
        }?.toSet() ?: setOf()
    }

    private fun getInitialSelectedChildren(): Set<OrionSmartDropDownItem> {
        return initialValue?.children?.mapNotNull { childId ->
            uiConfig.items.flatMap { it.children }.fastFirstOrNull { it.id == childId.id }
        }?.toSet() ?: setOf()
    }

    // Fuzzy search implementation
    private fun performFuzzySearch(
        query: String,
        items: List<OrionSmartDropDownItem>
    ): List<SearchableItem> {
        if (query.isBlank()) return emptyList()

        val normalizedQuery = query.normalized()
        val results = mutableListOf<SearchableItem>()

        items.forEach { parent ->
            // Search in parent name
            val parentScore = calculateFuzzyScore(normalizedQuery, parent.name.normalized())
            if (parentScore > 0.3f) { // Threshold for fuzzy matching
                results.add(
                    SearchableItem(
                        item = parent,
                        isParent = true,
                        matchScore = parentScore,
                        matchedText = parent.name
                    )
                )
            }

            // Search in children
            parent.children.forEach { child ->
                val childScore = calculateFuzzyScore(normalizedQuery, child.name.normalized())
                if (childScore > 0.3f) {
                    results.add(
                        SearchableItem(
                            item = child,
                            isParent = false,
                            parent = parent,
                            matchScore = childScore,
                            matchedText = child.name
                        )
                    )
                }
            }
        }

        // Sort by match score (highest first)
        return results.sortedByDescending { it.matchScore }
    }

    private fun calculateFuzzyScore(query: String, target: String): Float {
        if (query.isEmpty() || target.isEmpty()) return 0f

        // Exact match gets highest score
        if (target.contains(query, ignoreCase = true)) {
            return if (target.equals(query, ignoreCase = true)) 1.0f
            else if (target.startsWith(query, ignoreCase = true)) 0.9f
            else 0.8f
        }

        // Calculate Levenshtein distance for fuzzy matching
        val distance = levenshteinDistance(query, target)
        val maxLength = maxOf(query.length, target.length)

        return if (maxLength == 0) 0f
        else maxOf(0f, 1f - (distance.toFloat() / maxLength))
    }

    private fun levenshteinDistance(s1: String, s2: String): Int {
        val len1 = s1.length
        val len2 = s2.length
        val dp = Array(len1 + 1) { IntArray(len2 + 1) }

        for (i in 0..len1) dp[i][0] = i
        for (j in 0..len2) dp[0][j] = j

        for (i in 1..len1) {
            for (j in 1..len2) {
                val cost = if (s1[i - 1] == s2[j - 1]) 0 else 1
                dp[i][j] = minOf(
                    dp[i - 1][j] + 1,      // deletion
                    dp[i][j - 1] + 1,      // insertion
                    dp[i - 1][j - 1] + cost // substitution
                )
            }
        }

        return dp[len1][len2]
    }

    private fun groupSearchResults(searchResults: List<SearchableItem>): List<OrionSmartDropDownItem> {
        val parentMap = mutableMapOf<String, OrionSmartDropDownItem>()
        val childrenMap = mutableMapOf<String, MutableList<OrionSmartDropDownItem>>()

        searchResults.forEach { searchableItem ->
            if (searchableItem.isParent) {
                // Add parent if not already added
                if (!parentMap.containsKey(searchableItem.item.id)) {
                    parentMap[searchableItem.item.id] = searchableItem.item
                    childrenMap[searchableItem.item.id] = mutableListOf()
                }
            } else {
                // Add child and ensure parent is included
                val parent = searchableItem.parent!!
                if (!parentMap.containsKey(parent.id)) {
                    parentMap[parent.id] = parent
                    childrenMap[parent.id] = mutableListOf()
                }
                childrenMap[parent.id]?.add(searchableItem.item)
            }
        }

        // Reconstruct the items with filtered children
        return parentMap.values.map { parent ->
            parent.copy(children = childrenMap[parent.id] ?: emptyList())
        }
    }
}
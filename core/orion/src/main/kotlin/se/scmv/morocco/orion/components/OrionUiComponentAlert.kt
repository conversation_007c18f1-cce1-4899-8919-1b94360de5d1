package se.scmv.morocco.orion.components

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import se.scmv.morocco.designsystem.components.AvAlert
import se.scmv.morocco.designsystem.components.AvAlertType
import se.scmv.morocco.domain.models.orion.OrionAlert
import se.scmv.morocco.domain.models.orion.OrionBaseComponentValue

class OrionUiComponentAlert(
    private val uiConfig: OrionAlert
) : OrionUiComponent(baseData = uiConfig.baseData) {

    @Composable
    override fun Content(modifier: Modifier) {
        AvAlert(
            modifier = modifier.fillMaxWidth(),
            text = uiConfig.text,
            type = when (uiConfig.type) {
                OrionAlert.Type.INFO -> AvAlertType.Info
                OrionAlert.Type.WARNING -> AvAlertType.Warning
            }
        )
    }

    override fun validate(): Boolean {
        return true
    }

    override fun collectValue(): List<OrionBaseComponentValue> {
        return emptyList()
    }
}
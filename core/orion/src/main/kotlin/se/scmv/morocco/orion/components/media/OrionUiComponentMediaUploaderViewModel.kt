package se.scmv.morocco.orion.components.media

import androidx.lifecycle.ViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import se.scmv.morocco.domain.models.AdInsertMediaType
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.repositories.AdRepository
import java.io.File
import javax.inject.Inject

@HiltViewModel
class OrionUiComponentMediaUploaderViewModel @Inject constructor(
    private val adRepository: AdRepository
) : ViewModel() {

    suspend fun uploadMedia(file: File, mediaType: AdInsertMediaType): String? {
        return when (val result = adRepository.uploadMedia(file = file, adInsertMediaType = mediaType)) {
            is Resource.Success -> result.data
            is Resource.Failure -> null
        }
    }
}
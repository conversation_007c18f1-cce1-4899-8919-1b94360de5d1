package se.scmv.morocco.orion.components.media

import android.widget.Toast
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.IconButton
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedCard
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.key
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.util.fastAny
import androidx.compose.ui.util.fastFilterNotNull
import androidx.compose.ui.util.fastMapNotNull
import androidx.hilt.navigation.compose.hiltViewModel
import coil.ImageLoader
import coil.compose.rememberAsyncImagePainter
import coil.decode.VideoFrameDecoder
import coil.request.ImageRequest
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import se.scmv.morocco.common.utils.IOUtils
import se.scmv.morocco.designsystem.components.AvAttachmentTypeChooserBtmSheet
import se.scmv.morocco.designsystem.components.AvIconButton
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.domain.models.AdInsertMediaType
import se.scmv.morocco.domain.models.orion.OrionBaseComponentValue
import se.scmv.morocco.domain.models.orion.OrionKeyStringItem
import se.scmv.morocco.domain.models.orion.OrionMediaUploaderValue
import se.scmv.morocco.domain.models.orion.OrionVideoUploader
import se.scmv.morocco.orion.R
import se.scmv.morocco.orion.components.OrionUiComponent
import se.scmv.morocco.ui.SnackBarController
import se.scmv.morocco.ui.SnackBarEvent
import se.scmv.morocco.ui.SnackBarType
import java.io.File

class OrionUiComponentVideoUploader(
    private val uiConfig: OrionVideoUploader,
    initialValue: OrionMediaUploaderValue? = null
) : OrionUiComponent(baseData = uiConfig.baseData), OrionUiComponentMediaUploader {

    override val maximum = uiConfig.maximum
    override val mediaType = AdInsertMediaType.VIDEO
    override var mediaFilePath: String? = null

    override val media = mutableStateOf(
        List(uiConfig.maximum) { index ->
            initialValue?.medias?.getOrNull(index)?.let { media ->
                OrionUiComponentMedia(
                    id = media.id,
                    path = media.name,
                    status = OrionUiComponentMediaStatus.UPLOADED,
                    isLocal = false
                )
            }
        }.toPersistentList()
    )

    @Composable
    override fun Content(modifier: Modifier) {
        val viewModel = hiltViewModel<OrionUiComponentMediaUploaderViewModel>()
        val context = LocalContext.current
        val scope = rememberCoroutineScope()
        val galleryLauncher = rememberLauncherForActivityResult(
            contract = ActivityResultContracts.GetContent(),
            onResult = { uri ->
                if (uri != null) {
                    scope.launch(Dispatchers.IO) {
                        // Less than 30 MB
                        val isLarge = IOUtils.getFileSize(context, uri) > 31_457_280
                        if (isLarge) {
                            SnackBarController.showSnackBar(
                                event = SnackBarEvent(
                                    message = UiText.FromRes(R.string.ad_insert_video_upload_too_big_error),
                                    type = SnackBarType.ERROR,
                                )
                            )
                        } else {
                            onMediasPicked(
                                uris = listOf(uri),
                                viewModel = viewModel,
                                context = context
                            )
                        }
                    }
                }
            }
        )
        val galleryPermissionLauncher = rememberContentPermissionLauncher(
            mimeType = mediaType.mimeType,
            scope = scope,
            galleryLauncher = galleryLauncher,
            notGrantedMessage = R.string.common_image_video_permissions_required
        )

        var showAttachmentPicker by remember { mutableStateOf(false) }
        Column(verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.large)) {
            MediaUploadButton(
                modifier = Modifier.fillMaxWidth(),
                text = stringResource(
                    R.string.ad_insert_video_upload_comp_button,
                    leftCountToFill()
                ),
                icon = R.drawable.ic_video,
                onClick = {
                    if (leftCountToFill() <= 0) {
                        Toast.makeText(
                            context,
                            R.string.ad_insert_video_upload_limit_reached,
                            Toast.LENGTH_SHORT
                        ).show()
                    } else {
                        showAttachmentPicker = true
                    }
                }
            )
            VideosGrid(
                videos = media.value,
                onUploadVideo = { showAttachmentPicker = true },
                onRetryVideoUpload = { retryMediaUpload(media = it, viewModel = viewModel) }
            )
        }
        if (showAttachmentPicker) {
            AvAttachmentTypeChooserBtmSheet(
                title = stringResource(R.string.ad_insert_video_upload_picker_title),
                onDismiss = {
                    showAttachmentPicker = false
                },
                onCamera = null,
                onGallery = {
                    checkVideoGalleryPermissionGranted(
                        mimeType = mediaType.mimeType,
                        context = context,
                        galleryPermissionLauncher = galleryPermissionLauncher,
                        galleryLauncher = galleryLauncher
                    )
                }
            )
        }
    }

    override fun validate(): Boolean {
        return !media.value.fastAny { it?.status == OrionUiComponentMediaStatus.PENDING }
    }

    override fun collectValue(): List<OrionBaseComponentValue> {
        return listOf(
            OrionMediaUploaderValue(
                id = uiConfig.baseData.id,
                medias = media.value.fastFilterNotNull().fastMapNotNull { media ->
                    media.id?.let { OrionKeyStringItem(id = it, name = media.path) }
                }
            )
        )
    }

    @Composable
    fun VideosGrid(
        videos: List<OrionUiComponentMedia?>,
        onUploadVideo: () -> Unit,
        onRetryVideoUpload: (OrionUiComponentMedia) -> Unit
    ) {
        Column(verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium)) {
            videos.chunked(2).forEachIndexed { rowIndex, row ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium)
                ) {
                    repeat(2) { columnIndex ->
                        val itemIndex = rowIndex * 2 + columnIndex
                        if (columnIndex < row.size) {
                            val item = row[columnIndex]
                            key(item) {
                                VideoItem(
                                    modifier = Modifier.weight(1f),
                                    video = item,
                                    onUploadVideo = onUploadVideo,
                                    onRemove = { onRemoveMedia(itemIndex) },
                                    onRetryVideoUpload = { onRetryVideoUpload(it) }
                                )
                            }
                        } else {
                            // Maintain spacing for missing items
                            Spacer(modifier = Modifier.weight(1f))
                        }
                    }
                }
            }
        }
    }

    @Composable
    fun VideoItem(
        modifier: Modifier = Modifier,
        video: OrionUiComponentMedia?,
        onUploadVideo: () -> Unit,
        onRemove: () -> Unit,
        onRetryVideoUpload: (OrionUiComponentMedia) -> Unit
    ) {
        if (video != null) {
            Box(modifier.aspectRatio(1f)) {
                OutlinedCard(
                    modifier = Modifier.fillMaxSize()
                ) {
                    val painter = rememberAsyncImagePainter(
                        model = ImageRequest.Builder(LocalContext.current)
                            .apply {
                                if (video.isLocal) {
                                    data(File(video.path))
                                } else {
                                    data(video.path)
                                }
                            }
                            .build(),
                        imageLoader = ImageLoader.Builder(LocalContext.current)
                            .components { add(VideoFrameDecoder.Factory()) }
                            .build()
                    )
                    Image(
                        modifier = Modifier.fillMaxSize(),
                        painter = painter,
                        contentDescription = null,
                        contentScale = ContentScale.Crop
                    )
                }
                IconButton(
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                        .size(MaterialTheme.dimens.extraBig),
                    onClick = onRemove,
                ) {
                    Image(
                        painter = painterResource(R.drawable.ic_close_fill),
                        contentDescription = "Remove Video",
                    )
                }
                when (video.status) {
                    OrionUiComponentMediaStatus.PENDING -> CircularProgressIndicator(
                        modifier = Modifier
                            .size(MaterialTheme.dimens.default)
                            .align(Alignment.Center)
                    )

                    OrionUiComponentMediaStatus.UPLOADED -> Unit

                    OrionUiComponentMediaStatus.ERROR -> AvIconButton(
                        modifier = Modifier.align(Alignment.Center),
                        icon = Icons.Default.Refresh,
                        colors = IconButtonDefaults.filledIconButtonColors(
                            containerColor = MaterialTheme.colorScheme.errorContainer,
                            contentColor = MaterialTheme.colorScheme.onErrorContainer
                        ),
                        onClick = { onRetryVideoUpload(video) }
                    )
                }
            }
        } else Box(
            modifier = modifier
                .dashedBorder()
                .aspectRatio(1f)
                .clickable(onClick = onUploadVideo),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small)
            ) {
                androidx.compose.material3.Icon(
                    modifier = Modifier.size(MaterialTheme.dimens.bigger),
                    painter = painterResource(R.drawable.ic_upload),
                    contentDescription = "Upload Video"
                )
            }
        }
    }
}
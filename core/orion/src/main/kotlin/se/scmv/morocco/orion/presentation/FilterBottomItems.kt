package se.scmv.morocco.orion.presentation

import androidx.annotation.StringRes
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import se.scmv.morocco.designsystem.components.AvPrimaryButton
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.orion.R

@Composable
fun FilterBottomSheetHeader(
    modifier: Modifier = Modifier,
    @StringRes title: Int? = null,
    onCloseClick:() -> Unit
){
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                modifier = Modifier
                    .padding(
                        MaterialTheme.dimens.big
                    ),
                text = stringResource(title?: R.string.filter_bootom_sheet_title),
                fontWeight = MaterialTheme.typography.titleLarge.fontWeight,
                fontSize = MaterialTheme.typography.titleLarge.fontSize
            )
            IconButton(
                onClick = {
                    onCloseClick()
                }) {
                Icon(
                    imageVector = Icons.Default.Close,
                    contentDescription = null
                )
            }
        }
        HorizontalDivider()
    }
}

@Composable
fun FilterBottomSheetFooter(
    modifier: Modifier = Modifier,
    isLoading: Boolean = false,
    adCount: Int = 0,
    onClick : () -> Unit
){
    Box(
        modifier = modifier
    ){
        HorizontalDivider(
            modifier = Modifier.height(2.dp)
        )
        AvPrimaryButton(
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    MaterialTheme.dimens.medium
                ),
            loading = isLoading,
            text = stringResource(R.string.ad_count_text, adCount)
        ) {
            onClick()
        }
    }
}

@Composable
fun ClearFilterSection(
    modifier: Modifier = Modifier,
    @StringRes buttonTitle: Int,
    onClearFilter: () -> Unit
){
    Column (
        modifier = modifier.fillMaxWidth()
    ) {
        TextButton(
            modifier = Modifier
                .align(Alignment.End)
                .padding(
                    MaterialTheme.dimens.small
                ),
            onClick = {
                onClearFilter()
            }
        ) {
            Text(
                text = stringResource(buttonTitle),
                fontWeight = MaterialTheme.typography.titleMedium.fontWeight,
                fontFamily = MaterialTheme.typography.titleMedium.fontFamily,
                fontSize = MaterialTheme.typography.titleMedium.fontSize
            )
        }
    }
}

@Preview
@Composable
fun FilterBottomSheetHeaderPreview(){
    AvitoTheme {
        Column {
            FilterBottomSheetHeader(
                title = R.string.filter_bootom_sheet_title
            ) {

            }
            HorizontalDivider()
            ClearFilterSection(
                buttonTitle = R.string.common_clear
            ) { }
        }
    }

}
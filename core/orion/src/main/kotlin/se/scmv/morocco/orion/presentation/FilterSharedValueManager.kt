package se.scmv.morocco.orion.presentation

import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import se.scmv.morocco.domain.models.FilterValue
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class FilterSharedValueManager @Inject constructor() {

    /**
     * A private mutable shared flow that emits filter values.
     * Only the FilterSharedValueManager class can emit new values.
     */
    private val _filterValueSharedFlow = MutableSharedFlow<FilterValue?>()

    /**
     * A public shared flow exposed for consumers to collect.
     * Consumers can collect this flow to receive updates on filter values.
     */
    val filterValueSharedFlow: SharedFlow<FilterValue?> = _filterValueSharedFlow.asSharedFlow()

    /**
     * Function used by the producer (such as the filter component) to emit a new filter value.
     * The emitted value is collected by all active consumers listening to the shared flow.
     *
     * @param filterValue: The filter value to emit (can be null).
     *
     * Usage example:
     * ```
     * viewModelScope.launch {
     *     val newFilterValue = FilterValue(minPrice = 10, maxPrice = 100)
     *     filterSharedValueManager.emitValue(newFilterValue)
     * }
     * ```
     */
    suspend fun emitValue(filterValue: FilterValue?) {
        _filterValueSharedFlow.emit(filterValue)
    }
}

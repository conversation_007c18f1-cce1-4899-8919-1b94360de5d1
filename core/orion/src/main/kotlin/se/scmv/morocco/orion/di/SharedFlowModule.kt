package se.scmv.morocco.orion.di

import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import se.scmv.morocco.orion.presentation.FilterSharedValueManager
import se.scmv.morocco.orion.presentation.OrionFiltersSharedValuesManager
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object SharedFlowModule {

    @Provides
    @Singleton
    fun provideFilterSharedFlow(): FilterSharedValueManager {
        return FilterSharedValueManager()
    }

    @Provides
    @Singleton
    fun provideOrionFiltersValuesManager(): OrionFiltersSharedValuesManager {
        return OrionFiltersSharedValuesManager()
    }
}
package se.scmv.morocco.orion.presentation

import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import se.scmv.morocco.domain.models.orion.ORION_KEYWORD_KEY
import se.scmv.morocco.domain.models.orion.OrionBaseComponentValue
import se.scmv.morocco.domain.models.orion.OrionKeyStringValue
import javax.inject.Inject

data class OrionFiltersValues(
    val dynamicFilters: List<OrionBaseComponentValue> = emptyList(),
    val extendSearch: Boolean = true
)

class OrionFiltersSharedValuesManager @Inject constructor() {
    private var latestFiltersValues = OrionFiltersValues()

    private val _filterValueSharedFlow = MutableSharedFlow<OrionFiltersValues>(replay = 1)
    val filterValueSharedFlow = _filterValueSharedFlow.asSharedFlow()

    private val _initialFiltersValues = MutableSharedFlow<List<OrionBaseComponentValue>>()
    val initialFiltersValues = _initialFiltersValues.asSharedFlow()

    suspend fun applyFilters(filterValue: OrionFiltersValues) {
        latestFiltersValues = filterValue
        _filterValueSharedFlow.emit(filterValue)
    }

    suspend fun applyKeywordSearch(keyword: String) {
        val filters = listOf(OrionKeyStringValue(ORION_KEYWORD_KEY, keyword))
        _filterValueSharedFlow.emit(OrionFiltersValues(dynamicFilters = filters))
    }

    suspend fun removeKeywordSearch() {
        _filterValueSharedFlow.emit(latestFiltersValues)
    }

    suspend fun initializeFiltersValues(filterValues: List<OrionBaseComponentValue>) {
        _initialFiltersValues.emit(filterValues)
    }
}

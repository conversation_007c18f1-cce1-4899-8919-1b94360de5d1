package se.scmv.morocco.orion.components

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedIconButton
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import se.scmv.morocco.designsystem.components.avReadOnlyTextFieldColors
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.domain.models.orion.OrionBaseComponentValue
import se.scmv.morocco.domain.models.orion.OrionKeyStringValue
import se.scmv.morocco.domain.models.orion.OrionTimePicker
import java.util.regex.Pattern

class OrionUiComponentTimePicker(
    private val uiConfig: OrionTimePicker,
    private val listener: OptionalItemsListener,
    initialValue: OrionKeyStringValue? = null
) : OrionUiComponent(baseData = uiConfig.baseData) {

    // If the initialValue is null the position will be -1
    private var state by mutableIntStateOf(
        uiConfig.items.indexOfFirst { it.id == initialValue?.value }
    )

    @Composable
    override fun Content(modifier: Modifier) {
        Column(
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small)
        ) {
            with(uiConfig.baseData) {
                IconAndTitleRow(title = title, required = required, iconUrl = iconUrl)
            }
            Row(
                modifier = modifier,
                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium),
                verticalAlignment = Alignment.CenterVertically
            ) {
                OutlinedIconButton(
                    onClick = ::decrement,
                    colors = IconButtonDefaults.outlinedIconButtonColors(
                        containerColor = MaterialTheme.colorScheme.background
                    ),
                    border = BorderStroke(0.1.dp, MaterialTheme.colorScheme.outline),
                ) {
                    Text(text = "-", style = MaterialTheme.typography.headlineSmall)
                }
                OutlinedTextField(
                    modifier = Modifier.weight(1f),
                    value = uiConfig.items.getOrNull(state)?.name.orEmpty(),
                    onValueChange = {},
                    readOnly = true,
                    enabled = false,
                    colors = avReadOnlyTextFieldColors,
                    shape = MaterialTheme.shapes.medium
                )
                OutlinedIconButton(
                    onClick = ::increment,
                    colors = IconButtonDefaults.outlinedIconButtonColors(
                        containerColor = MaterialTheme.colorScheme.background
                    ),
                    border = BorderStroke(0.1.dp, MaterialTheme.colorScheme.outline),
                ) {
                    Text(text = "+", style = MaterialTheme.typography.headlineSmall)
                }
            }
        }
    }

    override fun validate(): Boolean {
        if (uiConfig.baseData.required.not() && state == -1) return true

        uiConfig.baseData.validations.forEach { validation ->
            val isValid = checkRegexRule(uiConfig.items.getOrNull(state)?.id.orEmpty(), validation.regex)
            if (isValid.not()) {
                error = UiText.Text(validation.errorMessage)
                return false
            }
        }
        return true
    }

    override fun collectValue(): List<OrionBaseComponentValue> {
        if (uiConfig.baseData.required.not() && state == -1) return emptyList()

        val value = requireNotNull(uiConfig.items.getOrNull(state)) {
            "OrionUiComponentTimePicker: ${uiConfig.baseData.title} is required but not inserted, make sure validate is called !"
        }
        return listOf( OrionKeyStringValue(id = uiConfig.baseData.id, value = value.id))
    }

    override fun resetValue() {
        state = -1
    }

    private fun decrement() {
        if (state == 0) {
            state = uiConfig.items.size - 1
        } else {
            state--
        }
        listener.onValueChanged(uiConfig, collectValue())
    }

    private fun increment() {
        state = (state + 1) % uiConfig.items.size
        listener.onValueChanged(uiConfig, collectValue())
    }

    private fun checkRegexRule(text: String, regex: String): Boolean {
        val modifiedText = text.replace("\n", " ")
        val matcher = Pattern.compile(regex).matcher(modifiedText)
        return matcher.find()
    }
}
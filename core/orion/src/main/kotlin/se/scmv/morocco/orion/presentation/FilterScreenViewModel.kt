package se.scmv.morocco.orion.presentation

import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import ma.avito.orion.ui.OrionValueChildren
import se.scmv.morocco.domain.models.FilterValue
import se.scmv.morocco.domain.models.ToggleConfigKey
import se.scmv.morocco.domain.models.filter.ListingCategoryFilters.Field
import se.scmv.morocco.domain.models.filter.RangeParam
import se.scmv.morocco.domain.models.filter.RangeValue
import se.scmv.morocco.orion.domaine.DynamicKeyValueManager
import se.scmv.morocco.orion.presentation.SaveDateKeys.SELECTED_CHIPS
import se.scmv.morocco.orion.presentation.SaveDateKeys.SELECTED_SMART_DROP_DOWN
import se.scmv.morocco.orion.presentation.SaveDateKeys.SINGLE_SELECTED_BOOLEAN_PARAMS
import se.scmv.morocco.orion.presentation.SaveDateKeys.SINGLE_SELECTED_TEXT_PARAMS
import se.scmv.morocco.orion.presentation.SaveDateKeys.SLIDE_RANGE_VALUES
import se.scmv.morocco.orion.presentation.SaveDateKeys.TOGGLE_CHECKED
import javax.inject.Inject

@HiltViewModel
class FilterScreenViewModel @Inject constructor(
    private val savedStateHandle: SavedStateHandle,
    private val sharedValueManager: FilterSharedValueManager
): ViewModel() {

    private val dynamicKeyValueManager = DynamicKeyValueManager()
    private val _selectedChips = MutableStateFlow<List<Field>>(emptyList())
    val selectedChips: StateFlow<List<Field>> = _selectedChips.asStateFlow()

    private val _selectedSmartDropDown = MutableStateFlow<List<Pair<String, ArrayList<OrionValueChildren>>>>(
        emptyList()
    )
    val selectedSmartDropDown: StateFlow<List<Pair<String, ArrayList<OrionValueChildren>>>> = _selectedSmartDropDown.asStateFlow()

    private val _singleSelectedTextParams = MutableStateFlow<List<Pair<String, String>>>(emptyList())
    val singleSelectedTextParams: StateFlow<List<Pair<String, String>>> = _singleSelectedTextParams.asStateFlow()

    private val _singleSelectedBooleanParams = MutableStateFlow<List<Pair<String, Boolean>>>(emptyList())

    private val _toggleChecked = MutableStateFlow<List<String>>(emptyList())
    val toggleChecked: StateFlow<List<String>> = _toggleChecked.asStateFlow()

    private val _rangeParams = MutableStateFlow<List<RangeParam>>(emptyList())
    val rangeParams: StateFlow<List<RangeParam>> = _rangeParams.asStateFlow()

    private val _brands = mutableStateOf(emptyList<String>())
    private val _models = mutableStateOf(emptyList<String>())

    private val _brandKey = mutableStateOf("")
    private val _modelKey = mutableStateOf("")


    private val _hasImage = mutableStateOf(false)
    private val _hasPrice = mutableStateOf(false)
    private val _isHotDeal = mutableStateOf(false)
    private val _isUrgent = mutableStateOf(false)
    private val _hasDelivery = mutableStateOf(false)
    private val _offerShipping = mutableStateOf(false)

    fun addOrUpdateRangValue(
        id: String,
        greaterThanOrEqual: String,
        lessThanOrEqual: String
    ) {
        val currentList = _rangeParams.value.toMutableList()
        val index = currentList.indexOfFirst { it.name == id }
        if (index != -1) {
            currentList[index] = (RangeParam(
                name = id, value = RangeValue(
                    greaterThanOrEqual = greaterThanOrEqual.toDoubleOrNull()?: 0.0,
                    lessThanOrEqual = lessThanOrEqual.toDoubleOrNull()?: 0.0
                )
            ))
        } else {
            currentList.add(
                RangeParam(
                    name = id, value = RangeValue(
                        greaterThanOrEqual = greaterThanOrEqual.toDoubleOrNull()?: 0.0,
                        lessThanOrEqual = lessThanOrEqual.toDoubleOrNull()?: 0.0
                    )
                )
            )
        }
        _rangeParams.value = currentList
        savedStateHandle[SLIDE_RANGE_VALUES] = _rangeParams.value
        onFilterChangeEvent(FilterValue.RangeParams(_rangeParams.value.toList()))
    }


    fun addOrUpdateSingleNumericParam(singleNumeric: Field){
        val currentSelection = _selectedChips.value.toMutableList()
        if (currentSelection.any { it.id == singleNumeric.id }){
            currentSelection.removeAll { it.id == singleNumeric.id }
        }else{
            currentSelection.add(singleNumeric)
        }
        _selectedChips.value = currentSelection
        savedStateHandle[SELECTED_CHIPS] = _selectedChips.value
        onFilterChangeEvent(FilterValue.SingleMatchNumeric(_selectedChips.value.map { Pair(it.id , 1.0) }))
    }

    fun updateBrandKey(brandId: String){
        _brandKey.value = brandId
    }

    fun updateModelKey(modelId: String){
        _modelKey.value = modelId
    }

    fun addDropDownSelectedItem(id: String, items: List<OrionValueChildren>){
        val currentItems = _selectedSmartDropDown.value.toMutableList()
        val index = currentItems.indexOfFirst { it.first == id }
        if (index != -1){
            currentItems[index] = Pair(id, ArrayList(items))
        }else{
            currentItems.add(Pair(id, ArrayList(items)))
        }
        _selectedSmartDropDown.value = currentItems
        savedStateHandle[SELECTED_SMART_DROP_DOWN] = _selectedSmartDropDown.value
        addBrand(items.map { it.key })
        addModels(items.flatMap { it.children }.map { it.key })
        onFilterChangeEvent(FilterValue.SmartDropDownIcon(prepareBrandAndModel()))
    }

    /**
     * prepare brands and models to be used on get ads request as filter params
     */
    private fun prepareBrandAndModel(): List<Pair<String, List<String>>>{
        return listOf(
            Pair(_brandKey.value, _brands.value),
            Pair(_modelKey.value, _models.value)
        )
    }

    fun addOrUpdateSingleTextParam(singleText: Pair<String, String>){
        val currentSingleSelected = singleSelectedTextParams.value.toMutableList()
        if(currentSingleSelected.any { it.first == singleText.first }){
            val index = currentSingleSelected.indexOfFirst { it.first ==  singleText.first}
            //to avoid networking call if the chip already selected
            if (currentSingleSelected[index].second == singleText.second) return
            currentSingleSelected[index] = singleText
        }else{
            currentSingleSelected.add(singleText)
        }
        _singleSelectedTextParams.value = currentSingleSelected
        savedStateHandle[SINGLE_SELECTED_TEXT_PARAMS] = _singleSelectedTextParams.value
        onFilterChangeEvent(FilterValue.SingleTextParam(_singleSelectedTextParams.value))
    }

    private fun addOrUpdateSingleBooleanParam(singleText: Pair<String, Boolean>){
        val currentSingleSelected = _singleSelectedBooleanParams.value.toMutableList()
        if(currentSingleSelected.any { it.first == singleText.first }){
            val index = currentSingleSelected.indexOfFirst { it.first ==  singleText.first}
            currentSingleSelected[index] = singleText
        }else{
            currentSingleSelected.add(singleText)
        }
        _singleSelectedBooleanParams.value = currentSingleSelected
        savedStateHandle[SINGLE_SELECTED_BOOLEAN_PARAMS] = _singleSelectedBooleanParams.value
        onFilterChangeEvent(FilterValue.SingleBooleanParam(_singleSelectedBooleanParams.value))
    }

    private fun addBrand(brands : List<String>){
        val mutableList = _brands.value.toMutableList()
        mutableList.clear()
        mutableList.addAll(brands)
        _brands.value = mutableList
    }

    private fun addModels(models: List<String>){
        val mutableList = _models.value.toMutableList()
        mutableList.clear()
        mutableList.addAll(models)
        _models.value = mutableList
    }

    fun addVariable(key: String, value: Any, isParam: Boolean){
        dynamicKeyValueManager.putValue(key, value)
        val keyValue = dynamicKeyValueManager.getValue(key, false)?: false
        val currentSelectedToggle = _toggleChecked.value.toMutableList()
        when(ToggleConfigKey.getFromString(key)){
            is ToggleConfigKey.HasImage -> {
                _hasImage.value = keyValue
                if (keyValue){
                    currentSelectedToggle.add(key)
                }else{
                    currentSelectedToggle.removeAll { it ==  key}
                }
                onFilterChangeEvent(
                    FilterValue.ToggleButtonValue(
                    ToggleConfigKey.HasImage(key = key, value = _hasImage.value)
                ))
            }
            is ToggleConfigKey.HasPrice -> {
                _hasPrice.value = keyValue
                if (keyValue){
                    currentSelectedToggle.add(key)
                }else{
                    currentSelectedToggle.removeAll { it ==  key}
                }
                onFilterChangeEvent(
                    FilterValue.ToggleButtonValue(
                    ToggleConfigKey.HasPrice(key = key, value = _hasPrice.value)
                ))
            }
            is ToggleConfigKey.IsHotDeal -> {
                _isHotDeal.value = keyValue
                if (keyValue){
                    currentSelectedToggle.add(key)
                }else{
                    currentSelectedToggle.removeAll { it ==  key}
                }
                onFilterChangeEvent(
                    FilterValue.ToggleButtonValue(
                    ToggleConfigKey.IsHotDeal(key = key, value = _isHotDeal.value)
                ))
            }
            is ToggleConfigKey.IsUrgent -> {
                _isUrgent.value = keyValue
                if (keyValue){
                    currentSelectedToggle.add(key)
                }else{
                    currentSelectedToggle.removeAll { it ==  key}
                }
                onFilterChangeEvent(
                    FilterValue.ToggleButtonValue(
                    ToggleConfigKey.IsUrgent(key = key, value = _isUrgent.value)
                ))
            }
            is ToggleConfigKey.HasDelivery -> {
                _hasDelivery.value = keyValue
                if (keyValue){
                    currentSelectedToggle.add(key)
                }else{
                    currentSelectedToggle.removeAll { it ==  key}
                }
                onFilterChangeEvent(
                    FilterValue.ToggleButtonValue(
                    ToggleConfigKey.HasDelivery(key = key, value = _hasDelivery.value)
                ))
            }
            is ToggleConfigKey.OfferShippingWithInCity -> {
                _offerShipping.value = keyValue
                if (keyValue){
                    currentSelectedToggle.add(key)
                }else{
                    currentSelectedToggle.removeAll { it ==  key}
                }
                onFilterChangeEvent(
                    FilterValue.ToggleButtonValue(
                    ToggleConfigKey.HasDelivery(key = key, value = _offerShipping.value)
                ))
            }
            else -> {
                if (isParam){
                    if (keyValue){
                        currentSelectedToggle.add(key)
                    }else{
                        currentSelectedToggle.removeAll { it ==  key}
                    }
                   addOrUpdateSingleBooleanParam(
                       Pair(key, value as? Boolean ?: false)
                   )
                }
            }
        }
        _toggleChecked.value = currentSelectedToggle
        savedStateHandle[TOGGLE_CHECKED] = _toggleChecked.value
    }

    fun clearFilter(){
        _toggleChecked.value = emptyList()
        savedStateHandle[TOGGLE_CHECKED] = _toggleChecked.value
        _selectedChips.value = emptyList()
        savedStateHandle[SELECTED_CHIPS] = _selectedChips.value
        _singleSelectedTextParams.value = emptyList()
        savedStateHandle[SINGLE_SELECTED_TEXT_PARAMS] = _singleSelectedTextParams.value
        _selectedSmartDropDown.value = emptyList()
        _brands.value = emptyList()
        _models.value = emptyList()
        savedStateHandle[SELECTED_SMART_DROP_DOWN] = _selectedSmartDropDown.value
        _rangeParams.value = emptyList()
        savedStateHandle[SLIDE_RANGE_VALUES] = _rangeParams.value
        onFilterChangeEvent(null)
    }

    private fun onFilterChangeEvent(filterValue: FilterValue?){
        viewModelScope.launch {
            sharedValueManager.emitValue(filterValue)
        }
    }

    override fun onCleared() {
        super.onCleared()
        clearFilter()
    }
}

object SaveDateKeys{
    const val SELECTED_CHIPS = "selectedChips"
    const val TOGGLE_CHECKED = "toggleChecked"
    const val SINGLE_SELECTED_TEXT_PARAMS = "singleSelectedTextParams"
    const val SINGLE_SELECTED_BOOLEAN_PARAMS = "singleSelectedBooleanParams"
    const val PRICE_RANGE = "priceRange"
    const val SELECTED_SMART_DROP_DOWN = "selectedSmartDropDown"
    const val SLIDE_RANGE_VALUES = "slideRangeValues"
}
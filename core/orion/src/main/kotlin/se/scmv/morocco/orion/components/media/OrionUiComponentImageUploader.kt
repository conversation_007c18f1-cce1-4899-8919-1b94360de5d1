package se.scmv.morocco.orion.components.media

import android.widget.Toast
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.Checkbox
import androidx.compose.material3.CheckboxDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.IconButton
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedCard
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.key
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.util.fastAny
import androidx.compose.ui.util.fastFilterNotNull
import androidx.compose.ui.util.fastMapNotNull
import androidx.hilt.navigation.compose.hiltViewModel
import coil.compose.AsyncImage
import coil.request.ImageRequest
import kotlinx.collections.immutable.PersistentList
import kotlinx.collections.immutable.toPersistentList
import se.scmv.morocco.designsystem.components.AvAttachmentTypeChooserBtmSheet
import se.scmv.morocco.designsystem.components.AvIconButton
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.domain.models.AdInsertMediaType
import se.scmv.morocco.domain.models.orion.OrionBaseComponentValue
import se.scmv.morocco.domain.models.orion.OrionImageUploader
import se.scmv.morocco.domain.models.orion.OrionKeyStringItem
import se.scmv.morocco.domain.models.orion.OrionMediaUploaderValue
import se.scmv.morocco.orion.R
import se.scmv.morocco.orion.components.OrionUiComponent
import java.io.File

class OrionUiComponentImageUploader(
    private val uiConfig: OrionImageUploader,
    initialValue: OrionMediaUploaderValue? = null,
    private val onPrincipalImageChanged: (OrionUiComponentMedia?) -> Unit
) : OrionUiComponent(baseData = uiConfig.baseData), OrionUiComponentMediaUploader {

    override val maximum = uiConfig.maximum
    override val mediaType = AdInsertMediaType.IMAGE
    override var mediaFilePath: String? = null

    override val media = mutableStateOf(
        List(uiConfig.maximum) { index ->
            initialValue?.medias?.getOrNull(index)?.let { media ->
                OrionUiComponentMedia(
                    id = media.id,
                    path = media.name,
                    status = OrionUiComponentMediaStatus.UPLOADED,
                    isLocal = false,
                    isMain = index == 0
                )
            }
        }.toPersistentList()
    )

    @Composable
    override fun Content(modifier: Modifier) {
        val viewModel = hiltViewModel<OrionUiComponentMediaUploaderViewModel>()
        val context = LocalContext.current
        val scope = rememberCoroutineScope()
        val galleryLauncher = rememberLauncherForActivityResult(
            contract = ActivityResultContracts.GetMultipleContents(),
            onResult = {
                onMediasPicked(uris = it, viewModel = viewModel, context = context)
            }
        )
        val cameraLauncher = rememberLauncherForActivityResult(
            contract = ActivityResultContracts.TakePicture(),
            onResult = {
                onMediaPicked(picked = it, viewModel = viewModel)
            }
        )
        // Permission launchers
        val cameraPermissionLauncher = rememberCameraPermissionLauncher(
            context = context,
            scope = scope,
            cameraLauncher = cameraLauncher,
            setFilePath = { mediaFilePath = it }
        )
        val galleryPermissionLauncher = rememberMultipleContentPermissionLauncher(
            mimeType = mediaType.mimeType,
            scope = scope,
            galleryLauncher = galleryLauncher
        )

        var showAttachmentPicker by remember { mutableStateOf(false) }
        Column(verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.large)) {
            MediaUploadButton(
                modifier = Modifier.fillMaxWidth(),
                text = stringResource(
                    R.string.ad_insert_image_upload_comp_button,
                    leftCountToFill()
                ),
                icon = R.drawable.ic_ad_insert_step3,
                onClick = {
                    if (leftCountToFill() <= 0) {
                        Toast.makeText(
                            context,
                            R.string.ad_insert_image_upload_limit_reached,
                            Toast.LENGTH_SHORT
                        ).show()
                    } else {
                        showAttachmentPicker = true
                    }
                }
            )
            ImagesGrid(
                images = media.value,
                onMainImageToggle = { index ->
                    val previousMainImageIndex =
                        media.value.indexOfFirst { it?.isMain == true }
                    if (index != previousMainImageIndex) {
                        val image = media.value.getOrNull(index)?.copy(isMain = true)
                        media.value = media.value.toMutableList().apply {
                            if (previousMainImageIndex != -1) {
                                set(
                                    index = previousMainImageIndex,
                                    element = media.value.getOrNull(previousMainImageIndex)
                                        ?.copy(isMain = false)
                                )
                            }
                            set(index = index, element = image)
                        }.toPersistentList()
                        image?.let { onPrincipalImageChanged(it) }
                    }
                },
                onUploadImage = { showAttachmentPicker = true },
                onRemoveImage = { image ->
                    onRemoveMedia(image)
                    if (!media.value.fastAny { it != null }) {
                        onPrincipalImageChanged(null)
                    }
                },
                onRetryImageUpload = {
                    retryMediaUpload(media = it, viewModel = viewModel)
                }
            )
        }
        if (showAttachmentPicker) {
            AvAttachmentTypeChooserBtmSheet(
                title = stringResource(R.string.ad_insert_image_upload_picker_title),
                onDismiss = {
                    showAttachmentPicker = false
                },
                onCamera = {
                    handleCameraSelection(
                        context = context,
                        scope = scope,
                        cameraPermissionLauncher = cameraPermissionLauncher,
                        cameraLauncher = cameraLauncher,
                        setFilePath = { mediaFilePath = it }
                    )
                },
                onGallery = {
                    checkImageGalleryPermissionGranted(
                        mimeType = mediaType.mimeType,
                        context = context,
                        galleryPermissionLauncher = galleryPermissionLauncher,
                        galleryLauncher = galleryLauncher
                    )
                }
            )
        }
        LaunchedEffect(Unit) {
            onFirstMediaUploaded()
        }
    }

    override fun validate(): Boolean {
        return !media.value.fastAny { it?.status == OrionUiComponentMediaStatus.PENDING }
    }

    override fun collectValue(): List<OrionBaseComponentValue> {
        return listOf(
            OrionMediaUploaderValue(
                id = uiConfig.baseData.id,
                medias = media.value.fastFilterNotNull()
                    .sortedByDescending { it.isMain }
                    .fastMapNotNull { media ->
                        media.id?.let { OrionKeyStringItem(id = it, name = media.path) }
                    }
            )
        )
    }

    override fun onFirstMediaUploaded() {
        media.value.firstOrNull()?.let { onPrincipalImageChanged(it) }
    }

    /**
     * Implementation of image grid for the image uploader component.
     *
     * The grid displays uploaded images in rows of 3 items and provides
     * delete functionality for each image.
     *
     * Example grid with 5 images showing rowIndex and columnIndex values:
     * ```
     * rowIndex=0:
     * ┌─────────┐ ┌─────────┐ ┌─────────┐
     * │ col=0   │ │ col=1   │ │ col=2   │
     * │ item=0  │ │ item=1  │ │ item=2  │
     * └─────────┘ └─────────┘ └─────────┘
     *
     * rowIndex=1:
     * ┌─────────┐ ┌─────────┐ ┌─────────┐
     * │ col=0   │ │ col=1   │ │ col=2   │
     * │ item=3  │ │ item=4  │ │ empty   │
     * └─────────┘ └─────────┘ └─────────┘
     * ```
     *
     * - rowIndex: Index of the current row in the chunked list (0-based)
     * - columnIndex: Position within each row (0 to 2)
     * - itemIndex: Calculated as (rowIndex * 3 + columnIndex) to get the actual position in the flat list
     *
     * @param images List of image URIs to display in the grid, with null values representing empty slots
     * @param onMainImageToggle Callback invoked when user toggles an image as main image
     * @param onUploadImage Callback invoked when user clicks on upload image button
     */
    @Composable
    private fun ImagesGrid(
        images: PersistentList<OrionUiComponentMedia?>,
        onMainImageToggle: (Int) -> Unit,
        onUploadImage: () -> Unit,
        onRemoveImage: (Int) -> Unit,
        onRetryImageUpload: (OrionUiComponentMedia) -> Unit
    ) {
        Column(verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium)) {
            images.chunked(3).forEachIndexed { rowIndex, row ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium)
                ) {
                    repeat(3) { columnIndex ->
                        val itemIndex = rowIndex * 3 + columnIndex
                        if (columnIndex < row.size) {
                            val item = row[columnIndex]
                            key(item) {
                                ImageItem(
                                    modifier = Modifier.weight(1f),
                                    image = item,
                                    itemIndex = itemIndex + 1,
                                    onRemove = { onRemoveImage(itemIndex) },
                                    onMainImageToggle = { onMainImageToggle(itemIndex) },
                                    onUploadImage = onUploadImage,
                                    onRetryImageUpload = onRetryImageUpload
                                )
                            }
                        } else {
                            // Maintain spacing for missing items
                            Spacer(modifier = Modifier.weight(1f))
                        }
                    }
                }
            }
        }
    }

    @OptIn(ExperimentalLayoutApi::class)
    @Composable
    private fun ImageItem(
        modifier: Modifier = Modifier,
        image: OrionUiComponentMedia?,
        itemIndex: Int,
        onRemove: () -> Unit,
        onMainImageToggle: () -> Unit,
        onUploadImage: () -> Unit,
        onRetryImageUpload: (OrionUiComponentMedia) -> Unit
    ) {
        if (image != null) {
            Box(modifier.aspectRatio(1f)) {
                OutlinedCard(
                    modifier = Modifier.fillMaxSize()
                ) {
                    AsyncImage(
                        modifier = Modifier.fillMaxSize(),
                        model = ImageRequest.Builder(LocalContext.current)
                            .apply {
                                if (image.isLocal) {
                                    data(File(image.path))
                                } else {
                                    data(image.path)
                                }
                            }
                            .build(),
                        contentDescription = "Item Image",
                        contentScale = ContentScale.Crop,
                    )
                }
                IconButton(
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                        .size(MaterialTheme.dimens.extraBig),
                    onClick = onRemove,
                ) {
                    Image(
                        painter = painterResource(R.drawable.ic_close_fill),
                        contentDescription = "Remove Image",
                    )
                }
                when (image.status) {
                    OrionUiComponentMediaStatus.PENDING -> CircularProgressIndicator(
                        modifier = Modifier
                            .size(MaterialTheme.dimens.default)
                            .align(Alignment.Center)
                    )

                    OrionUiComponentMediaStatus.UPLOADED -> FlowRow(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(
                                color = MaterialTheme.colorScheme.onBackground.copy(alpha = 0.5f),
                                shape = MaterialTheme.shapes.small
                            )
                            .clickable(onClick = onMainImageToggle)
                            .padding(
                                horizontal = MaterialTheme.dimens.tiny,
                                vertical = MaterialTheme.dimens.medium
                            )
                            .align(Alignment.BottomCenter),
                        horizontalArrangement = Arrangement.SpaceEvenly,
                        verticalArrangement = Arrangement.Center
                    ) {
                        Text(
                            modifier = Modifier.align(Alignment.CenterVertically),
                            text = stringResource(R.string.ad_insert_image_upload_comp_image_item_main_picture),
                            style = MaterialTheme.typography.labelSmall,
                            color = MaterialTheme.colorScheme.background
                        )
                        Checkbox(
                            checked = image.isMain,
                            onCheckedChange = null,
                            enabled = false,
                            colors = CheckboxDefaults.colors(
                                disabledCheckedColor = MaterialTheme.colorScheme.primary,
                            )
                        )
                    }

                    OrionUiComponentMediaStatus.ERROR -> AvIconButton(
                        modifier = Modifier.align(Alignment.Center),
                        icon = Icons.Default.Refresh,
                        colors = IconButtonDefaults.filledIconButtonColors(
                            containerColor = MaterialTheme.colorScheme.errorContainer,
                            contentColor = MaterialTheme.colorScheme.onErrorContainer
                        ),
                        onClick = { onRetryImageUpload(image) }
                    )
                }
            }
        } else Box(
            modifier = modifier
                .dashedBorder()
                .aspectRatio(1f)
                .clickable(onClick = onUploadImage),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small)
            ) {
                androidx.compose.material3.Icon(
                    modifier = Modifier.size(MaterialTheme.dimens.bigger),
                    painter = painterResource(R.drawable.ic_upload),
                    contentDescription = "Upload Image"
                )
                Text(
                    text = stringResource(
                        R.string.ad_insert_image_upload_comp_image_item_placeholder,
                        itemIndex
                    ),
                    style = MaterialTheme.typography.bodySmall
                )
            }
        }
    }
}
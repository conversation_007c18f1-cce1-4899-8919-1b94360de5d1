package se.scmv.morocco.orion.presentation

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.collections.immutable.persistentSetOf
import kotlinx.collections.immutable.toPersistentList
import kotlinx.collections.immutable.toPersistentSet
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import se.scmv.morocco.analytics.AnalyticsHelper
import se.scmv.morocco.analytics.models.AnalyticsEvent
import se.scmv.morocco.common.lang.LocaleManager
import se.scmv.morocco.designsystem.components.ChipData
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.domain.models.AdTypeKey
import se.scmv.morocco.domain.models.BookmarkedSearch
import se.scmv.morocco.domain.models.CATEGORY_ID_ALL
import se.scmv.morocco.domain.models.CategoryTree
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.models.orion.OrionBaseComponent
import se.scmv.morocco.domain.models.orion.OrionBaseComponentData
import se.scmv.morocco.domain.models.orion.OrionBaseComponentValue
import se.scmv.morocco.domain.models.orion.OrionKeyBooleanValue
import se.scmv.morocco.domain.models.orion.OrionSingleSelectCategoryDropdownValue
import se.scmv.morocco.domain.repositories.AccountRepository
import se.scmv.morocco.domain.repositories.AdRepository
import se.scmv.morocco.domain.repositories.ConfigRepository
import se.scmv.morocco.orion.components.OptionalItemsListener
import se.scmv.morocco.orion.components.toOrionUiComponent
import se.scmv.morocco.ui.R
import se.scmv.morocco.ui.renderFailure
import se.scmv.morocco.ui.renderSuccess
import javax.inject.Inject

@HiltViewModel
class OrionFiltersViewModel @Inject constructor(
    private val sharedValuesManager: OrionFiltersSharedValuesManager,
    private val configRepository: ConfigRepository,
    private val adRepository: AdRepository,
    private val accountRepository: AccountRepository,
    private val analyticsHelper: AnalyticsHelper
) : ViewModel(), OptionalItemsListener {

    private val _viewState = MutableStateFlow(OrionFiltersViewState())
    val viewState = _viewState.asStateFlow()

    private val _onetimeEvents = MutableSharedFlow<OrionFiltersOneTimeEvents>()
    val onetimeEvents = _onetimeEvents.asSharedFlow()

    private var selectedCategoryId = CATEGORY_ID_ALL
    private var selectedAdTypeKey = AdTypeKey.ALL
    private var categories = emptyList<CategoryTree>()
    private val valuesListeners = mutableListOf<OrionFiltersValueListener>()
    private var extendedSearch = true

    init {
        viewModelScope.launch {
            sharedValuesManager.initialFiltersValues
                .distinctUntilChanged { old, new -> old == new }
                .collectLatest { filtersValues ->
                    val orionCategoryValue = filtersValues
                        .filterIsInstance<OrionSingleSelectCategoryDropdownValue>()
                        .firstOrNull()
                    if (orionCategoryValue != null) {
                        selectedCategoryId = orionCategoryValue.category.id
                        selectedAdTypeKey = orionCategoryValue.adTypeKey ?: AdTypeKey.ALL
                    }
                    getInitialData(filtersValues)
                }
        }
        observeLanguageChanges()
    }

    fun onClear() {
        if (selectedCategoryId == CATEGORY_ID_ALL && selectedAdTypeKey == AdTypeKey.ALL) {
            _viewState.update { it.copy(selectedFilters = persistentSetOf()) }
            _viewState.value.filters.forEach { it.resetValue() }
            viewModelScope.launch {
                getAdsCount()
            }
        } else viewModelScope.launch {
            onCategorySelected(CATEGORY_ID_ALL, AdTypeKey.ALL)
        }
    }

    fun onBookmarkSearchChanged(isBookmarked: Boolean) {
        viewModelScope.launch {
            showHideRefreshing(true)
            if (isBookmarked) {
                val filters = collectFiltersValues()
                val query = configRepository.buildBookmarkSearchQuery(filters)
                val result = accountRepository.bookmarkSearch(query)
                showHideRefreshing(false)
                when (result) {
                    is Resource.Success -> {
                        _viewState.update { it.copy(bookmarkedSearch = result.data) }
                        renderSuccess(UiText.FromRes(R.string.bookmarked_searches_screen_save_search_success))
                        trackSaveSearchEvent(filters)
                    }

                    is Resource.Failure -> renderFailure(result.error)
                }
            } else {
                _viewState.value.bookmarkedSearch?.let {
                    val result = accountRepository.unBookmarkSearch(it)
                    showHideRefreshing(false)
                    when (result) {
                        is Resource.Success -> {
                            _viewState.update { it.copy(bookmarkedSearch = null) }
                            renderSuccess(UiText.FromRes(R.string.bookmarked_searches_screen_delete_search_success))
                        }

                        is Resource.Failure -> renderFailure(result.error)
                    }
                } ?: run { showHideRefreshing(false) }
            }
        }
    }

    /**
     * This function is called when the user click on the refresh button of the error state.
     * So we just need to reload the current state of data.
     */
    fun onRefresh() {
        getInitialData()
    }

    fun onConfirm() {
        viewModelScope.launch {
            notifyFiltersChanged()
            _onetimeEvents.emit(OrionFiltersOneTimeEvents.NavigateBackAndSwitchToListing)
        }
    }

    fun removeFromSelectedFilters(component: ChipData) {
        _viewState.value.filters.find { it.baseData.id == component.id }?.let {
            it.resetValue()
            viewModelScope.launch {
                getAdsCount()
                refreshSelectedFilters(componentData = it.baseData, values = emptyList())
            }
        }
    }

    fun preselectFilters(filtersValues: List<OrionBaseComponentValue>) {
        viewModelScope.launch {
            val category = filtersValues.filterIsInstance<OrionSingleSelectCategoryDropdownValue>()
                .firstOrNull()
            if (category != null) {
                onCategorySelected(
                    categoryId = category.category.id,
                    adType = category.adTypeKey,
                    initialValues = filtersValues,
                    notifyFiltersChanged = true
                )
            }
        }
    }

    fun onCategoryChanged(categoryId: String, adTypeKey: String?) {
        viewModelScope.launch {
            onCategorySelected(
                categoryId = categoryId,
                adType = adTypeKey?.let { AdTypeKey.safeValueOf(it) },
                notifyFiltersChanged = true
            )
        }
    }

    fun onBookmarkedSearchSelected(bookmarkedSearch: BookmarkedSearch) {
        viewModelScope.launch {
            val filtersValues = bookmarkedSearch.filtersValues
            val category = filtersValues.filterIsInstance<OrionSingleSelectCategoryDropdownValue>()
                .firstOrNull()
            if (category != null) {
                onCategorySelected(
                    categoryId = category.category.id,
                    adType = category.adTypeKey,
                    initialValues = filtersValues,
                    notifyFiltersChanged = true
                )
            }
        }
    }

    fun onCancelExtendedDeliveryClicked() {
        // TODO We shouldn't hardcode the key. We should find a better way to do this.
        _viewState.value.filters.find { it.baseData.id == "delivery" }?.let {
            it.resetValue()
            // Remove the chip from the selected filters
            refreshSelectedFilters(componentData = it.baseData, values = emptyList())
            viewModelScope.launch {
                getAdsCount()
                notifyFiltersChanged()
            }
        }
    }

    fun onCancelExtendedSearchClicked() {
        viewModelScope.launch {
            extendedSearch = false
            getAdsCount()
            notifyFiltersChanged()
        }
    }

    override fun onValueChanged(
        component: OrionBaseComponent,
        values: List<OrionBaseComponentValue>
    ) {
        viewModelScope.launch {
            values.filterIsInstance<OrionSingleSelectCategoryDropdownValue>().firstOrNull()?.let {
                onCategorySelected(categoryId = it.category.id, adType = it.adTypeKey)
            } ?: run {
                getAdsCount()
            }
            refreshSelectedFilters(componentData = component.baseData, values = values)
        }
    }

    /**
     * This function is called when the view model is created, error state refresh button is clicked, or when the language changes.
     * case 1: view model is created, we need to load the categories and filters.
     * case 2: error state refresh button is clicked, we need to reload the failed data load.
     * case 3: language changes, we need to reload the categories and filters. And keep the selected filters values.
     */
    private fun getInitialData(
        filtersValues: List<OrionBaseComponentValue> = emptyList(),
        notifyFiltersChanged: Boolean = true
    ) {
        viewModelScope.launch {
            if (categories.isEmpty()) {
                showHideLoading(true)
                val result = configRepository.getFiltersCategories()
                when (result) {
                    is Resource.Success -> {
                        categories = result.data
                        getFilters(
                            categoryId = selectedCategoryId,
                            adTypeKey = selectedAdTypeKey,
                            showLoading = true,
                            showRefreshing = false,
                            initialValues = filtersValues,
                            notifyFiltersChanged = notifyFiltersChanged
                        )
                    }

                    is Resource.Failure -> OrionFiltersViewState(
                        isLoading = false,
                        error = R.string.common_network_error_verify_and_try_later
                    )
                }
            } else {
                getFilters(
                    categoryId = selectedCategoryId,
                    adTypeKey = selectedAdTypeKey,
                    showLoading = true,
                    showRefreshing = false,
                    initialValues = filtersValues
                )
            }
        }
    }

    /**
     * When the language changes, we need to reload the categories and filters.
     * This is because the categories and filters are fetched from the backend and are language dependent.
     * But we need to keep the selected filters values.
     */
    private fun observeLanguageChanges() {
        viewModelScope.launch {
            LocaleManager.languageChangeEvent.collectLatest {
                categories = emptyList()
                val filtersValues = collectFiltersValues().filter { value ->
                    value.id in viewState.value.selectedFilters.map { it.id }
                }
                getInitialData(filtersValues)
            }
        }
    }

    private suspend fun onCategorySelected(
        categoryId: String,
        adType: AdTypeKey?,
        initialValues: List<OrionBaseComponentValue> = emptyList(),
        notifyFiltersChanged: Boolean = true
    ) {
        if (selectedCategoryId == categoryId && selectedAdTypeKey == adType) return
        selectedCategoryId = categoryId
        selectedAdTypeKey = adType ?: AdTypeKey.SELL
        extendedSearch = true
        getFilters(
            categoryId = categoryId,
            adTypeKey = adType ?: AdTypeKey.SELL,
            showLoading = false,
            showRefreshing = true,
            initialValues = initialValues,
            notifyFiltersChanged = notifyFiltersChanged
        )
    }

    private suspend fun getFilters(
        categoryId: String,
        adTypeKey: AdTypeKey,
        showLoading: Boolean,
        showRefreshing: Boolean,
        initialValues: List<OrionBaseComponentValue> = emptyList(),
        notifyFiltersChanged: Boolean = true
    ) {
        if (showLoading) {
            showHideLoading(true)
        }
        if (showRefreshing) {
            showHideRefreshing(true)
        }
        val result = configRepository.getFilters(categoryId = categoryId, adTypeKey = adTypeKey)
        when (result) {
            is Resource.Success -> {
                val uiComponents = withContext(Dispatchers.Default) {
                    result.data.mapNotNull {
                        val uiComponent = it.toOrionUiComponent(
                            categories = categories,
                            categoryId = categoryId,
                            adTypeKey = adTypeKey,
                            hasNextTextFieldItem = false,
                            initialValues = initialValues,
                            listener = this@OrionFiltersViewModel
                        )
                        if (uiComponent != null && it.baseData.dependencies.isNotEmpty()) {
                            valuesListeners.add(uiComponent)
                        }
                        uiComponent
                    }.toPersistentList()
                }
                val initialValuesId = initialValues.map { it.id }
                val selectedFilters = result.data.filter { it.baseData.id in initialValuesId }.map {
                    with(it.baseData) {
                        ChipData(id = id, name = title, selected = true)
                    }
                }.toSet().toPersistentSet()
                OrionFiltersViewState(
                    filters = uiComponents,
                    selectedFilters = selectedFilters,
                    isLoading = false,
                    error = null
                ).let { state -> _viewState.update { state } }
                if (notifyFiltersChanged) {
                    notifyFiltersChanged()
                }
                getAdsCount()
            }

            is Resource.Failure -> _viewState.update {
                OrionFiltersViewState(
                    isLoading = false,
                    error = R.string.common_network_error_verify_and_try_later
                )
            }
        }
    }

    private suspend fun getAdsCount() {
        _viewState.update { it.copy(isCalculatingCount = true, bookmarkedSearch = null) }
        val filtersValues = collectFiltersValues()
        valuesListeners.forEach { it.onValuesChanged(filtersValues) }
        val result = adRepository.getAdsCount(filtersValues, extendedSearch)
        val count = when (result) {
            is Resource.Success -> result.data
            is Resource.Failure -> null
        }
        _viewState.update { it.copy(count = count, isCalculatingCount = false) }
    }

    private fun refreshSelectedFilters(
        componentData: OrionBaseComponentData,
        values: List<OrionBaseComponentValue>
    ) {
        if (
            values.size == 1
            && (values.first() as? OrionSingleSelectCategoryDropdownValue)?.category?.id == CATEGORY_ID_ALL
        ) return

        val chip = with(componentData) {
            ChipData(id = id, name = title, selected = true)
        }
        if (values.isEmpty() || (values.size == 1 && (values.first() as? OrionKeyBooleanValue)?.value == false)) {
            _viewState.update { state ->
                state.copy(selectedFilters = state.selectedFilters.remove(chip))
            }
        } else {
            _viewState.update { state -> state.copy(selectedFilters = state.selectedFilters.add(chip)) }
        }
    }

    private suspend fun collectFiltersValues(): List<OrionBaseComponentValue> {
        return withContext(Dispatchers.Default) {
            viewState.value.filters.map { it.collectValue() }.flatten()
        }
    }

    private suspend fun notifyFiltersChanged() {
        val filtersValues = collectFiltersValues()
        sharedValuesManager.applyFilters(OrionFiltersValues(filtersValues, extendedSearch))
    }

    private fun showHideLoading(isLoading: Boolean) {
        _viewState.update { it.copy(isLoading = isLoading) }
    }

    private fun showHideRefreshing(isRefreshing: Boolean) {
        _viewState.update { it.copy(isRefreshing = isRefreshing) }
    }

    private fun trackSaveSearchEvent(filters: List<OrionBaseComponentValue>) {
        val categoryProperties = filters
            .filterIsInstance<OrionSingleSelectCategoryDropdownValue>()
            .firstOrNull()?.category?.getAnalyticsParams()
        analyticsHelper.logEvent(
            AnalyticsEvent(
                name = AnalyticsEvent.Types.SAVE_SEARCH,
                properties = categoryProperties.orEmpty()
            )
        )
    }
}
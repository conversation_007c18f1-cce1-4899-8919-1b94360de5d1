package se.scmv.morocco.orion.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.domain.models.orion.OrionBaseComponentValue
import se.scmv.morocco.domain.models.orion.OrionGroupHeader

class OrionUiComponentGroupHeader(
    private val uiConfig: OrionGroupHeader
) : OrionUiComponent(baseData = uiConfig.baseData) {

    @Composable
    override fun Content(modifier: Modifier) {
        Column(
            modifier = modifier,
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small)
        ) {
            Text(
                text = uiConfig.baseData.title,
                style = MaterialTheme.typography.titleMedium
            )
            Text(
                text = uiConfig.subtitle,
                style = MaterialTheme.typography.bodyMedium
            )
        }
    }

    override fun validate(): Boolean {
        return true
    }

    override fun collectValue(): List<OrionBaseComponentValue> {
        return emptyList()
    }
}
package se.scmv.morocco.orion.presentation

import android.annotation.SuppressLint
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.util.fastFirst
import androidx.compose.ui.viewinterop.AndroidView
import androidx.hilt.navigation.compose.hiltViewModel
import ma.avito.orion.ui.OrionValueChildren
import ma.avito.orion.ui.input.dropdown.OrionSmartDropdown
import ma.avito.orion.ui.input.dropdown.OrionSmartDropdownInValues
import se.scmv.morocco.designsystem.components.filter.AvChipGroup
import se.scmv.morocco.designsystem.components.filter.AvChipMultiSelectExtended
import se.scmv.morocco.designsystem.components.filter.AvRangeSlider
import se.scmv.morocco.designsystem.components.filter.AvToggleFieldFilter
import se.scmv.morocco.designsystem.components.filter.MinMaxTextField
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.domain.models.filter.ListingCategoryFilters
import se.scmv.morocco.domain.models.filter.ListingCategoryFilters.BaseFilters
import se.scmv.morocco.orion.R
import se.scmv.morocco.orion.domaine.FilterItemType
import se.scmv.morocco.orion.domaine.toOrionValueChildren


@Composable
fun FilterBottomSheetRootScreen(
    modifier: Modifier = Modifier,
    filters: List<BaseFilters>,
    adsCount: () -> Int,
    isLoading: Boolean = false,
    filterScreenViewModel: FilterScreenViewModel = hiltViewModel(),
    onApplyFilterClick: () -> Unit,
    onCloseClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black.copy(alpha = 0.15f)),
        contentAlignment = Alignment.BottomCenter
    ) {
        Column(
            modifier = modifier
                .fillMaxWidth()
                .wrapContentHeight()
                .align(Alignment.BottomCenter)
                .clip(
                    RoundedCornerShape(
                        topStart = MaterialTheme.dimens.medium,
                        topEnd = MaterialTheme.dimens.medium
                    )
                )
                .background(MaterialTheme.colorScheme.background),
        ) {
            FilterBottomSheetHeader(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(MaterialTheme.colorScheme.background),
                title = R.string.filter_bootom_sheet_title
            ) {
                onCloseClick()
            }
            LazyColumn(
                modifier = Modifier
                    .weight(1f)
                    .padding(bottom = MaterialTheme.dimens.medium)
            ) {
                item {
                    ClearFilterSection(
                        buttonTitle = R.string.common_clear
                    ) {
                        filterScreenViewModel.clearFilter()
                    }
                }
                items(items = filters, key = { item -> item.id }) { item ->
                    FilterBottomSheetItem(
                        filterItemType = FilterItemType.fromString(item.type ?: "")!!,
                        baseFilter = item,
                    )
                }
            }
            FilterBottomSheetFooter(
                modifier = Modifier
                    .background(
                        color = MaterialTheme.colorScheme.background
                    ),
                isLoading = isLoading,
                adCount = adsCount()
            ) {
                onApplyFilterClick()
            }

        }
    }
}

@Composable
fun FilterBottomSheetItem(
    filterItemType: FilterItemType,
    baseFilter: BaseFilters,
    id: String = "",
    filterScreenViewModel: FilterScreenViewModel = hiltViewModel()
) {
    var openSmartDropdownDialog by remember {
        mutableStateOf(false)
    }
    when (filterItemType) {
        FilterItemType.MinMax -> {
            val rangeState = filterScreenViewModel.rangeParams.collectAsState()
            val minValue =
                rangeState.value.find { it.name == baseFilter.id }?.value?.greaterThanOrEqual
                    ?: baseFilter.range?.first()?.toDouble()
            val maxValue =
                rangeState.value.find { it.name == baseFilter.id }?.value?.lessThanOrEqual
                    ?: baseFilter.range?.get(1)?.toDouble()
            MinMaxTextField(
                minValue = minValue?.toString() ?: baseFilter.range?.first()?.toDouble().toString(),
                maxValue = maxValue?.toString() ?: baseFilter.range?.get(1)?.toDouble().toString(),
                minPlaceHolder = minValue?.toString() ?: "",
                maxPlaceHolder = maxValue?.toString() ?: "",
                label = "${baseFilter.name ?: ""}-${baseFilter.suffix ?: ""}",
                iconPath = baseFilter.icon ?: ""
            ) { min, max ->
                filterScreenViewModel.addOrUpdateRangValue(
                    id = baseFilter.id,
                    greaterThanOrEqual = min,
                    lessThanOrEqual = max
                )
            }
        }

        FilterItemType.MultiSelectExtended -> {
            val selectedChips by filterScreenViewModel.selectedChips.collectAsState()
            AvChipMultiSelectExtended(
                fields = baseFilter.fields ?: emptyList(),
                selectedChips = selectedChips.toMutableList(),
            ) {
                filterScreenViewModel.addOrUpdateSingleNumericParam(it)
            }
        }

        FilterItemType.MultipleSelectSmartDropdown -> {}
        FilterItemType.MultipleSelectSmartDropdownIcon -> {
            val selectedItems = filterScreenViewModel.selectedSmartDropDown.collectAsState()
            val orionValueChildrenList = baseFilter.toOrionValueChildren()
            SmartDropDownIcon(
                modifier = Modifier.fillMaxWidth(),
                enableMultiSelected = true,
                isDialogOpening = openSmartDropdownDialog,
                filterItems = orionValueChildrenList,
                selectedItem = selectedItems.value.find { itemSelected ->
                    itemSelected.first == baseFilter.id
                }?.second ?: ArrayList(emptyList()),
                icon = baseFilter.icon ?: "",
                title = baseFilter.name ?: ""
            ) { value ->
                filterScreenViewModel.updateBrandKey(baseFilter.id)
                filterScreenViewModel.updateModelKey(baseFilter.child ?: "")
                filterScreenViewModel.addDropDownSelectedItem(id = baseFilter.id, value)
                openSmartDropdownDialog = false
            }
        }

        FilterItemType.SingleSelectCategorySearchDropDown -> {}
        FilterItemType.SingleSelectExtended -> {
            val singleSelectedItem = filterScreenViewModel.singleSelectedTextParams.collectAsState()
            AvChipGroup(
                modifier = Modifier.padding(
                    vertical = MaterialTheme.dimens.medium,
                    horizontal = MaterialTheme.dimens.medium,
                ),
                filterItems = baseFilter.filterItems,
                selectedItem = if (singleSelectedItem.value.any { it.first == baseFilter.id }) {
                    singleSelectedItem.value.fastFirst { it.first == baseFilter.id }.second
                } else {
                    ""
                },
                iconUrl = baseFilter.icon ?: "",
                label = baseFilter.name ?: "",
                id = baseFilter.id
            ) {
                filterScreenViewModel.addOrUpdateSingleTextParam(it)
            }
        }

        FilterItemType.Slider -> {
            val slideRangeValuesState = filterScreenViewModel.rangeParams.collectAsState()
            AvRangeSlider(
                filterItems = baseFilter.filterItems,
                sliderIcon = baseFilter.icon ?: "",
                sliderLabel = baseFilter.name ?: "",
                selectedRangeValues = slideRangeValuesState.value,
                id = baseFilter.id
            ) { greaterThanOrEqual, lessThanOrEqual ->
                filterScreenViewModel.addOrUpdateRangValue(
                    id = baseFilter.id,
                    greaterThanOrEqual = greaterThanOrEqual,
                    lessThanOrEqual = lessThanOrEqual
                )
            }
        }

        FilterItemType.TextFieldType -> {}
        FilterItemType.ToggleFieldFilter -> {

            val toggleFieldFilter = filterScreenViewModel.toggleChecked.collectAsState()
            AvToggleFieldFilter(
                modifier = Modifier
                    .padding(
                        start = MaterialTheme.dimens.medium
                    ),
                iconUrl = baseFilter.icon ?: "",
                label = baseFilter.name ?: "",
                isChecked = toggleFieldFilter.value.any { it == baseFilter.id },
                description = baseFilter.description ?: "",
                key = baseFilter.id
            ) { pair ->
                filterScreenViewModel.addVariable(
                    key = pair.first,
                    value = pair.second,
                    isParam = baseFilter.isParam ?: false
                )
            }
        }
    }
}

@SuppressLint("SuspiciousIndentation")
@Composable
fun SmartDropDownIcon(
    modifier: Modifier = Modifier,
    filterItems: ArrayList<OrionValueChildren>,
    selectedItem: ArrayList<OrionValueChildren>,
    isDialogOpening: Boolean = false,
    icon: String,
    title: String,
    enableMultiSelected: Boolean = false,
    required: Boolean = false,
    onChanged: (List<OrionValueChildren>) -> Unit
) {
    val values = OrionSmartDropdownInValues(
        parentKey = selectedItem.joinToString(",") { it.key },
        childKey = selectedItem.joinToString(",") { parent ->
            parent.children.joinToString(",") { it.key }
        }
    )

    Column(
        modifier = modifier
    ) {
        AndroidView(
            modifier = modifier,
            factory = { context ->
                OrionSmartDropdown.Builder(context)
                    .setTitle(title)
                    .setData(ArrayList(filterItems))
                    .setValue(values)
                    .onValueSelectedListener(onChanged)
                    .setRequired(true)
                    .setSheettitle(title)
                    .setMultiSelect(enableMultiSelected)
                    .setIcon(icon)
                    .setRequired(required)
                    .build()
            }
        ) { dropdown ->
            if (isDialogOpening) {
                dropdown.openDialog()
            }
        }
    }

}

@Preview
@Composable
fun FilterBottomSheetPreview() {
    AvitoTheme {
        FilterBottomSheetRootScreen(
            filters = fakeFilters,
            adsCount = { 0 },
            onApplyFilterClick = {}
        ) { }
    }
}

//I had add this fake data for preview
val fakeFilters = listOf(
    BaseFilters(
        icon = "icon_url_1",
        id = "1",
        isParam = true,
        filterItems = listOf(
            ListingCategoryFilters.FilterItems(
                key = "key1",
                name = "Filter Name 1",
                label = "Label 1",
                short = "Short 1",
                trackingName = "Tracking 1",
                children = listOf(
                    ListingCategoryFilters.Child(
                        key = "childKey1",
                        label = "Child Label 1",
                        isSelected = true
                    ),
                    ListingCategoryFilters.Child(
                        key = "childKey2",
                        label = "Child Label 2",
                        isSelected = false
                    )
                ),
                isSelected = false,
                selectedChildCount = 1
            ),
            ListingCategoryFilters.FilterItems(
                key = "key2",
                name = "Filter Name 2",
                label = "Label 2",
                short = "Short 2",
                trackingName = "Tracking 2",
                children = listOf(
                    ListingCategoryFilters.Child(
                        key = "childKey3",
                        label = "Child Label 3",
                        isSelected = true
                    ),
                    ListingCategoryFilters.Child(
                        key = "childKey4",
                        label = "Child Label 4",
                        isSelected = true
                    )
                ),
                isSelected = true,
                selectedChildCount = 2
            )
        ),
        range = listOf(0, 100),
        step = 5,
        suffix = "%",
        name = "Base Filter 1",
        type = "range",
        child = null,
        fields = listOf(
            ListingCategoryFilters.Field(
                id = "field1",
                isParam = "true",
                name = "Field Name 1",
                isSelected = false
            ),
            ListingCategoryFilters.Field(
                id = "field2",
                isParam = "false",
                name = "Field Name 2",
                isSelected = true
            )
        ),
        childParam = null,
        description = "This is a description for Base Filter 1",
        defaultValue = false
    ),
    BaseFilters(
        icon = "icon_url_2",
        id = "2",
        isParam = false,
        filterItems = listOf(
            ListingCategoryFilters.FilterItems(
                key = "key3",
                name = "Filter Name 3",
                label = "Label 3",
                short = "Short 3",
                trackingName = "Tracking 3",
                children = listOf(
                    ListingCategoryFilters.Child(
                        key = "childKey5",
                        label = "Child Label 5",
                        isSelected = false
                    ),
                    ListingCategoryFilters.Child(
                        key = "childKey6",
                        label = "Child Label 6",
                        isSelected = true
                    )
                ),
                isSelected = false,
                selectedChildCount = 1
            )
        ),
        range = listOf(10, 50),
        step = 2,
        suffix = "units",
        name = "Base Filter 2",
        type = "discrete",
        child = "child2",
        fields = listOf(
            ListingCategoryFilters.Field(
                id = "field3",
                isParam = "true",
                name = "Field Name 3",
                isSelected = true
            )
        ),
        childParam = ListingCategoryFilters.ChildParam("id", "name"),
        description = "This is a description for Base Filter 2",
        defaultValue = true
    )
)

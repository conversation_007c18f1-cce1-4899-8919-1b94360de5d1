package se.scmv.morocco.orion.domaine

sealed class FilterItemType {
    object TextFieldType : FilterItemType()
    object SingleSelectCategorySearchDropDown: FilterItemType()
    object MultipleSelectSmartDropdown: FilterItemType()
    object MultipleSelectSmartDropdownIcon: FilterItemType()
    object SingleSelectExtended: FilterItemType()
    object MinMax: FilterItemType()
    object Slider: FilterItemType()
    object ToggleFieldFilter: FilterItemType()
    object MultiSelectExtended: FilterItemType()

    companion object {
        fun fromString(fieldName: String): FilterItemType?{
            return when(fieldName){
                "text_field" -> TextFieldType
                "single_select_category_dropdown_search" -> SingleSelectCategorySearchDropDown
                "multiple_select_smart_dropdown" -> MultipleSelectSmartDropdown
                "multiple_select_smart_dropdown_icon" -> MultipleSelectSmartDropdownIcon
                "single_select_extended" -> SingleSelectExtended
                "min_max_field" -> MinMax
                "slider" -> Slider
                "toggle_field_filter" -> ToggleFieldFilter
                "multiselect_extended" -> MultiSelectExtended
                else -> null
            }
        }
    }
}
package se.scmv.morocco.orion.components

import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import coil.compose.AsyncImage
import coil.request.ImageRequest
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.coroutines.flow.StateFlow
import se.scmv.morocco.designsystem.components.AvAlert
import se.scmv.morocco.designsystem.components.AvAlertType
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.domain.models.VasPack
import se.scmv.morocco.domain.models.VasPackageExecutionSlots
import se.scmv.morocco.domain.models.orion.OrionBaseComponentValue
import se.scmv.morocco.domain.models.orion.OrionLap
import se.scmv.morocco.orion.R
import se.scmv.morocco.orion.components.media.OrionUiComponentMedia
import java.io.File

@Stable
data class AdInsertLapViewState(
    val vasPacks: ImmutableList<VasPack> = persistentListOf(),
    val vasExecutionSlots: VasPackageExecutionSlots? = null,
    val image: OrionUiComponentMedia? = null,
    val title: String? = null,
    val description: String? = null,
    val limitation: AdInsertLapLimitation? = null
)

@Stable
data class AdInsertLapLimitation(
    val messageResId: Int,
    val messageArgs: ImmutableList<String> = persistentListOf(),
    val alertType: AvAlertType,
    val isLimit: Boolean
)

class OrionUiComponentLap(
    uiConfig: OrionLap,
    private val viewState: StateFlow<AdInsertLapViewState>
) : OrionUiComponent(baseData = uiConfig.baseData) {

    @Composable
    override fun Content(modifier: Modifier) {
        val state = viewState.collectAsStateWithLifecycle().value
        Column(
            modifier = modifier,
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium)
        ) {
            AsyncImage(
                modifier = Modifier
                    .fillMaxWidth()
                    .aspectRatio(1.6f)
                    .clip(MaterialTheme.shapes.small)
                    .border(0.5.dp, MaterialTheme.colorScheme.outline, MaterialTheme.shapes.small),
                model = state.image?.let { image ->
                    ImageRequest.Builder(LocalContext.current)
                        .apply {
                            if (image.isLocal) {
                                data(File(image.path))
                            } else {
                                data(image.path)
                            }
                        }
                        .build()
                },
                contentDescription = "Principal Image",
                contentScale = ContentScale.Crop,
                placeholder = painterResource(R.drawable.img_no_image_placeholder),
                error = painterResource(R.drawable.img_no_image_placeholder),
            )
            state.title?.let { title ->
                Text(text = title, style = MaterialTheme.typography.bodyLarge)
            }
            state.description?.let { description ->
                Text(text = description, style = MaterialTheme.typography.labelMedium)
            }
            state.limitation?.let { limitation ->
                AvAlert(
                    text = stringResource(
                        limitation.messageResId,
                        *limitation.messageArgs.toTypedArray()
                    ),
                    type = limitation.alertType
                )
            }
        }
    }

    override fun validate(): Boolean {
        return true
    }

    override fun collectValue(): List<OrionBaseComponentValue> {
        return emptyList()
    }
}
package se.scmv.morocco.orion.domaine

import ma.avito.orion.ui.OrionValueChildren
import se.scmv.morocco.domain.models.filter.ListingCategoryFilters

fun ListingCategoryFilters.BaseFilters.toOrionValueChildren()  =
    ArrayList(
        this.filterItems.map { map ->
            OrionValueChildren(
                key = map.key ?: "",
                value = map.label ?: "",
                children = map.children?.map { child ->
                    OrionValueChildren(
                        key = child.key,
                        value = child.label,
                        isSelected = child.isSelected
                    )
                }?.let { java.util.ArrayList(it) } ?: arrayListOf(),
                analyticalValue = map.trackingName ?: ""
            )
        }
    )

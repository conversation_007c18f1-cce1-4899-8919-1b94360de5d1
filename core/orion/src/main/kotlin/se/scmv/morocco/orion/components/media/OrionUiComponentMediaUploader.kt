package se.scmv.morocco.orion.components.media

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import androidx.activity.compose.ManagedActivityResultLauncher
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.unit.dp
import androidx.compose.ui.util.fastFilter
import androidx.compose.ui.util.fastMapNotNull
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider
import androidx.lifecycle.viewModelScope
import kotlinx.collections.immutable.PersistentList
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.launch
import se.scmv.morocco.common.utils.IOUtils
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.domain.models.AdInsertMediaType
import se.scmv.morocco.orion.R
import se.scmv.morocco.ui.SnackBarController
import se.scmv.morocco.ui.SnackBarEvent
import se.scmv.morocco.ui.SnackBarType
import java.io.File
import java.io.IOException

interface OrionUiComponentMediaUploader {
    val maximum: Int
    val mediaType: AdInsertMediaType
    val media: MutableState<PersistentList<OrionUiComponentMedia?>>
    var mediaFilePath: String?
    fun onFirstMediaUploaded() {}

    fun onMediasPicked(
        uris: List<Uri>,
        viewModel: OrionUiComponentMediaUploaderViewModel,
        context: Context,
    ) {
        val pathsToAdd = uris
            .fastMapNotNull { uri -> IOUtils.getRealPathFromURI(uri, context) }
            .fastFilter { path -> media.value.none { it?.path == path } }
            .take(leftCountToFill())
        addMediasToList(pathsToAdd)
        viewModel.viewModelScope.launch {
            uploadMedias(paths = pathsToAdd, viewModel = viewModel)
        }
    }

    fun onMediaPicked(
        picked: Boolean,
        viewModel: OrionUiComponentMediaUploaderViewModel,
    ) {
        if (picked) {
            mediaFilePath?.let { filePath ->
                addMediasToList(listOf(filePath))
                uploadMedias(paths = listOf(filePath), viewModel = viewModel)
            }
        }
    }

    fun uploadMedias(
        paths: List<String>,
        viewModel: OrionUiComponentMediaUploaderViewModel,
    ) {
        viewModel.viewModelScope.launch {
            val differs = paths.map { path ->
                async {
                    val mediaId = viewModel.uploadMedia(
                        file = File(path),
                        mediaType = mediaType
                    )
                    path to mediaId
                }
            }
            val results = differs.awaitAll()
            results.forEach {
                editMedia(
                    uri = it.first,
                    mediaId = it.second,
                    status = if (it.second != null) {
                        OrionUiComponentMediaStatus.UPLOADED
                    } else OrionUiComponentMediaStatus.ERROR,
                )
            }
            if (results.firstOrNull()?.second != null) {
                onFirstMediaUploaded()
            }
        }
    }

    fun retryMediaUpload(
        media: OrionUiComponentMedia,
        viewModel: OrionUiComponentMediaUploaderViewModel,
    ) {
        editMedia(
            uri = media.path,
            mediaId = media.id,
            status = OrionUiComponentMediaStatus.PENDING
        )
        uploadMedias(paths = listOf(media.path), viewModel = viewModel)
    }

    fun addMediasToList(paths: List<String>) {
        media.value = media.value.toMutableList()
            .apply {
                paths.forEach { path ->
                    val indexToFill = indexOfFirst { it == null }
                    if (indexToFill == -1) return
                    set(
                        indexToFill,
                        OrionUiComponentMedia(
                            path = path,
                            id = null,
                            status = OrionUiComponentMediaStatus.PENDING,
                            isMain = indexToFill == 0
                        )
                    )
                }
            }
            .toPersistentList()
    }

    fun editMedia(uri: String, mediaId: String?, status: OrionUiComponentMediaStatus) {
        val indexOfFirst = media.value.indexOfFirst { it?.path == uri }
        if (indexOfFirst == -1) return
        media.value = media.value.toMutableList()
            .apply {
                set(
                    indexOfFirst,
                    media.value.getOrNull(indexOfFirst)?.copy(
                        path = uri,
                        id = mediaId,
                        status = status,
                    )
                )
            }
            .toPersistentList()
    }

    fun onRemoveMedia(position: Int) {
        media.value = media.value.toMutableList()
            .apply {
                removeAt(position)
                add(null)
            }
            .toPersistentList()
    }

    fun leftCountToFill() = maximum - media.value.filterNotNull().size
}

@Composable
fun MediaUploadButton(
    modifier: Modifier = Modifier,
    text: String,
    icon: Int,
    onClick: () -> Unit
) {
    Column(
        modifier = modifier
            .dashedBorder()
            .clickable(onClick = onClick, role = Role.Button)
            .padding(MaterialTheme.dimens.large),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        androidx.compose.material3.Icon(
            modifier = Modifier.size(MaterialTheme.dimens.extraBig),
            painter = painterResource(icon),
            contentDescription = "Upload Media"
        )
        Text(
            text = text,
            style = MaterialTheme.typography.bodySmall
        )
    }
}

@Composable
fun Modifier.dashedBorder(): Modifier {
    val stroke = Stroke(
        width = 2f,
        pathEffect = PathEffect.dashPathEffect(floatArrayOf(10f, 10f), 0f)
    )
    return drawBehind {
        drawRoundRect(
            color = Color.Gray,
            style = stroke,
            cornerRadius = CornerRadius(8.dp.toPx())
        )
    }
        .clip(MaterialTheme.shapes.small)
        .background(MaterialTheme.colorScheme.surfaceContainerHighest)
}

@Composable
fun rememberCameraPermissionLauncher(
    context: Context,
    scope: CoroutineScope,
    cameraLauncher: ManagedActivityResultLauncher<Uri, Boolean>,
    setFilePath: (String) -> Unit
): ManagedActivityResultLauncher<String, Boolean> {
    return rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            launchCamera(
                context = context,
                cameraLauncher = cameraLauncher,
                scope = scope,
                setFilePath = setFilePath
            )
        } else {
            scope.launch {
                SnackBarController.showSnackBar(
                    SnackBarEvent(
                        message = UiText.FromRes(R.string.common_camera_permissions_required),
                        type = SnackBarType.WARNING
                    )
                )
            }
        }
    }
}

@Composable
fun rememberMultipleContentPermissionLauncher(
    mimeType: String,
    scope: CoroutineScope,
    galleryLauncher: ManagedActivityResultLauncher<String, List<Uri>>,
    notGrantedMessage: Int = R.string.common_image_gallery_permissions_required
): ManagedActivityResultLauncher<String, Boolean> {
    return rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            galleryLauncher.launch(mimeType)
        } else {
            scope.launch {
                SnackBarController.showSnackBar(
                    SnackBarEvent(
                        message = UiText.FromRes(notGrantedMessage),
                        type = SnackBarType.WARNING
                    )
                )
            }
        }
    }
}

@Composable
fun rememberContentPermissionLauncher(
    mimeType: String,
    scope: CoroutineScope,
    galleryLauncher: ManagedActivityResultLauncher<String, Uri?>,
    notGrantedMessage: Int
): ManagedActivityResultLauncher<String, Boolean> {
    return rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            galleryLauncher.launch(mimeType)
        } else {
            scope.launch {
                SnackBarController.showSnackBar(
                    SnackBarEvent(
                        message = UiText.FromRes(notGrantedMessage),
                        type = SnackBarType.WARNING
                    )
                )
            }
        }
    }
}

fun handleCameraSelection(
    context: Context,
    scope: CoroutineScope,
    cameraPermissionLauncher: ManagedActivityResultLauncher<String, Boolean>,
    cameraLauncher: ManagedActivityResultLauncher<Uri, Boolean>,
    setFilePath: (String) -> Unit
) {
    when (PackageManager.PERMISSION_GRANTED) {
        ContextCompat.checkSelfPermission(context, Manifest.permission.CAMERA) -> {
            launchCamera(
                context = context,
                cameraLauncher = cameraLauncher,
                scope = scope,
                setFilePath = setFilePath
            )
        }

        else -> cameraPermissionLauncher.launch(Manifest.permission.CAMERA)
    }
}

fun checkImageGalleryPermissionGranted(
    mimeType: String,
    context: Context,
    galleryPermissionLauncher: ManagedActivityResultLauncher<String, Boolean>,
    galleryLauncher: ManagedActivityResultLauncher<String, List<@JvmSuppressWildcards Uri>>
) {
    // For Android 13+ (API 33+), use READ_MEDIA_IMAGES permission
    val permission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
        Manifest.permission.READ_MEDIA_IMAGES
    } else {
        Manifest.permission.READ_EXTERNAL_STORAGE
    }

    when {
        ContextCompat.checkSelfPermission(
            context,
            permission
        ) == PackageManager.PERMISSION_GRANTED -> {
            galleryLauncher.launch(mimeType)
        }

        else -> galleryPermissionLauncher.launch(permission)
    }
}

fun checkVideoGalleryPermissionGranted(
    mimeType: String,
    context: Context,
    galleryPermissionLauncher: ManagedActivityResultLauncher<String, Boolean>,
    galleryLauncher: ManagedActivityResultLauncher<String, Uri?>
) {
    // For Android 13+ (API 33+), use READ_MEDIA_IMAGES permission
    val permission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
        Manifest.permission.READ_MEDIA_VIDEO
    } else {
        Manifest.permission.READ_EXTERNAL_STORAGE
    }

    when {
        ContextCompat.checkSelfPermission(
            context,
            permission
        ) == PackageManager.PERMISSION_GRANTED -> {
            galleryLauncher.launch(mimeType)
        }

        else -> galleryPermissionLauncher.launch(permission)
    }
}

fun launchCamera(
    context: Context,
    cameraLauncher: ManagedActivityResultLauncher<Uri, Boolean>,
    scope: CoroutineScope,
    setFilePath: (String) -> Unit
) {
    try {
        IOUtils.createCameraImageTempFile(context)?.let { file ->
            setFilePath(file.absolutePath)
            val pictureUri = FileProvider.getUriForFile(
                context,
                context.packageName + ".imagesprovider",
                file
            )
            cameraLauncher.launch(pictureUri)
        }
    } catch (e: IOException) {
        scope.launch {
            SnackBarController.showSnackBar(
                SnackBarEvent(
                    message = UiText.FromRes(R.string.common_error_camera_launch),
                    type = SnackBarType.ERROR
                )
            )
        }
    }
}
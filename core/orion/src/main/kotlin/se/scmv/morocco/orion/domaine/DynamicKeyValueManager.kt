package se.scmv.morocco.orion.domaine

class DynamicKeyValueManager {

    /**
     * A map to hold dynamic key-value pairs
     */
    private val keyValueStore = mutableMapOf<String, Any>()

    /**
     * Method to add or update a key-value pair
     */
    fun putValue(key: String, value: Any) {
        keyValueStore[key] = value
    }

    /**
     * Method to get a value by key
     */
    fun <T> getValue(key: String, defaultValue: T? = null): T? {
        return keyValueStore[key] as? T ?: defaultValue
    }

    /**
     * Method to get a value by key
     */
    fun removeKey(key: String) {
        keyValueStore.remove(key)
    }

    /**
     * Method to get a value by key
     */
    fun containsKey(key: String): Boolean {
        return keyValueStore.containsKey(key)
    }

    /**
     * Get all boolean values
      */
    fun getAllBooleanValues(): Map<String, Boolean> {
        return keyValueStore.filterValues { it is Boolean }
            .mapValues { it.value as Boolean }
    }

    /**
     * Get all string values
     */
    fun getAllStringValues(): Map<String, String> {
        return keyValueStore.filterValues { it is String }
            .mapValues { it.value as String }
    }
}
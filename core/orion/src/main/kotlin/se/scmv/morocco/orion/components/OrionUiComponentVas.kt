package se.scmv.morocco.orion.components

import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.flow.StateFlow
import se.scmv.morocco.designsystem.components.DropdownData
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.domain.models.VasPack
import se.scmv.morocco.domain.models.VasPackage
import se.scmv.morocco.domain.models.VasPackageExecutionSlotsTime
import se.scmv.morocco.domain.models.orion.OrionBaseComponentValue
import se.scmv.morocco.domain.models.orion.OrionVas
import se.scmv.morocco.domain.models.orion.OrionVasValue
import se.scmv.morocco.orion.R
import se.scmv.morocco.orion.components.vas.VasPacks
import se.scmv.morocco.ui.SnackBarType

class OrionUiComponentVas(
    private val uiConfig: OrionVas,
    private val lapViewState: StateFlow<AdInsertLapViewState>,
    private val onVasPackageSelected: (VasPack, VasPackage) -> Unit,
    private val onExeSlotDaySelected: (String) -> Unit,
    private val onExeSlotTimeSelected: (String) -> Unit,
    private val onAddImageClicked: () -> Unit,
    private val showSnackBar: (UiText, SnackBarType) -> Unit
) : OrionUiComponent(baseData = uiConfig.baseData) {

    private val days = lapViewState.value.vasExecutionSlots?.days?.toImmutableList()
    private val times = lapViewState.value.vasExecutionSlots?.times?.map { it.toDropdownData() }?.toImmutableList()

    private var selectedVasPackage: VasPackage? by mutableStateOf(null)
    private var selectedExeSlotsDay by mutableStateOf(days?.getOrNull(0))
    private var selectedExeSlotsTime by mutableStateOf(
        lapViewState.value.vasExecutionSlots?.times?.let {
            VasPackageExecutionSlotsTime.findCurrentOrFirstTimeSlot(it)?.toDropdownData()
        }
    )

    @Composable
    override fun Content(modifier: Modifier) {
        val lapViewState = lapViewState.collectAsStateWithLifecycle().value
        VasPacks(
            modifier = modifier,
            packs = lapViewState.vasPacks.toImmutableList(),
            isAdToBoostHasImage = lapViewState.image != null,
            selectedPackageId = selectedVasPackage?.id.orEmpty(),
            exeSlotsDays = days,
            exeSlotsTimes = times,
            selectedExeSlotsDay = selectedExeSlotsDay,
            selectedExeSlotsTime = selectedExeSlotsTime?.id,
            onPackageSelected = { vasCategory, vasPackage ->
                error = null
                selectedVasPackage = vasPackage
                onVasPackageSelected(vasCategory, vasPackage)
            },
            onExeSlotDaySelected = { day ->
                selectedExeSlotsDay = day
                onExeSlotDaySelected(day)
            },
            onExeSlotTimeSelected = { timeId ->
                selectedExeSlotsTime = times?.firstOrNull { it.id == timeId }
                onExeSlotTimeSelected(timeId)
            },
            onAddImageClicked = {
                onAddImageClicked()
            }
        )
    }

    override fun validate(): Boolean {
        if (selectedVasPackage == null) {
            showSnackBar(
                UiText.FromRes(R.string.choose_vas_package_screen_package_selection_required),
                SnackBarType.ERROR
            )
            return false
        }
        return true
    }

    override fun collectValue(): List<OrionBaseComponentValue> {
        return listOf(
            OrionVasValue(
                id = uiConfig.baseData.id,
                packageId = selectedVasPackage?.id.orEmpty(),
                exeDay = selectedExeSlotsDay,
                exeTimeId = selectedExeSlotsTime?.id
            )
        )
    }

    private fun VasPackageExecutionSlotsTime.toDropdownData() = DropdownData(
        id = id,
        name = displayedTime()
    )
}
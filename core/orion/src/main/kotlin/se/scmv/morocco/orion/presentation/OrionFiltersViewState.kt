package se.scmv.morocco.orion.presentation

import androidx.compose.runtime.Stable
import kotlinx.collections.immutable.PersistentList
import kotlinx.collections.immutable.PersistentSet
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentSetOf
import se.scmv.morocco.designsystem.components.ChipData
import se.scmv.morocco.domain.models.BookmarkedSearchQuery
import se.scmv.morocco.orion.components.OrionUiComponent

@Stable
data class OrionFiltersViewState(
    val filters: PersistentList<OrionUiComponent> = persistentListOf(),
    val selectedFilters: PersistentSet<ChipData> = persistentSetOf(),
    val bookmarkedSearch: BookmarkedSearchQuery? = null,
    val count: Int? = null,
    val isCalculatingCount: Boolean = false,
    val isLoading: Boolean = true,
    val isRefreshing: Boolean = false,
    val error: Int? = null,
)

sealed interface OrionFiltersOneTimeEvents {
    data object NavigateBackAndSwitchToListing : OrionFiltersOneTimeEvents
}
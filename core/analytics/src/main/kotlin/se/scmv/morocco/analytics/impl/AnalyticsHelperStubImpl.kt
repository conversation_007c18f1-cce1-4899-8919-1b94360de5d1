package se.scmv.morocco.analytics.impl

import android.util.Log
import se.scmv.morocco.analytics.AnalyticsHelper
import se.scmv.morocco.analytics.models.AnalyticsAddons
import se.scmv.morocco.analytics.models.AnalyticsEvent
import se.scmv.morocco.analytics.models.UserProperties
import javax.inject.Inject

private const val TAG = "StubAnalyticsHelper"

/**
 * Debug AnalyticsHelper which logs events in the console.
 */
internal class AnalyticsHelperStubImpl @Inject constructor() : AnalyticsHelper {

    override fun logEvent(event: AnalyticsEvent, where: Set<AnalyticsAddons>) {
        where.forEach {
            Log.d(TAG, "Log analytics event into ${it.name}: $event")
        }
    }

    override fun identify(accountId: String, properties: UserProperties) {}

    override fun clearUserData() {}
}
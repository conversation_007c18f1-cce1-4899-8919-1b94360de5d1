package se.zrcoding.convention

import com.android.build.api.dsl.CommonExtension
import org.gradle.api.JavaVersion
import org.gradle.api.Project
import org.gradle.kotlin.dsl.dependencies
import org.gradle.kotlin.dsl.withType
import org.jetbrains.kotlin.gradle.dsl.JvmTarget
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

/**
 * Configure base Kotlin with Android options
 */
internal fun Project.configureKotlinAndroid(
    commonExtension: CommonExtension<*, *, *, *, *, *>,
) {
    commonExtension.apply {
        compileSdk = versionCatalog().findVersion("compileSdk").get().toString().toInt()

        defaultConfig {
            minSdk = versionCatalog().findVersion("minSdk").get().toString().toInt()
        }

        compileOptions {
            // Up to Java 11 APIs are available through desugaring
            // https://developer.android.com/studio/write/java11-minimal-support-table
            sourceCompatibility = JavaVersion.VERSION_17
            targetCompatibility = JavaVersion.VERSION_17
            isCoreLibraryDesugaringEnabled = true
        }
    }

    configureKotlin()

    dependencies {
        add("implementation", versionCatalog().findLibrary("androidx.core.ktx").get())
        add("coreLibraryDesugaring", versionCatalog().findLibrary("desugar.jdk.libs").get())
        add("implementation", versionCatalog().findLibrary("kotlinx.datetime").get())
        add("implementation", versionCatalog().findLibrary("kotlinx.serialization").get())
    }
}

/**
 * Configure base Kotlin options
 */
private fun Project.configureKotlin() {
    // Use withType to workaround https://youtrack.jetbrains.com/issue/KT-55947
    tasks.withType<KotlinCompile>().configureEach {
        compilerOptions {
            // Set JVM target to 17
            jvmTarget.set(JvmTarget.JVM_17)
        }
    }
}

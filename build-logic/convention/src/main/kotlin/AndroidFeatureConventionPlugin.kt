import com.android.build.gradle.LibraryExtension
import se.zrcoding.convention.versionCatalog
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.configure
import org.gradle.kotlin.dsl.dependencies

class AndroidFeatureConventionPlugin : Plugin<Project> {

    override fun apply(target: Project) {
        with(target) {
            pluginManager.apply {
                apply("avito.android.library")
                apply("avito.android.library.compose")
                apply("avito.android.hilt")
            }
            extensions.configure<LibraryExtension> {
                defaultConfig {
                    testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
                }

                dependencies {
                    add(
                        configurationName = "implementation",
                        dependencyNotation = project(":core:designsystem")
                    )
                    add(
                        configurationName = "implementation",
                        dependencyNotation = project(":core:common")
                    )
                    add(
                        configurationName = "implementation",
                        dependencyNotation = project(":core:ui")
                    )
                    add(
                        configurationName = "implementation",
                        dependencyNotation = project(":core:domain")
                    )
                    add(
                        configurationName = "implementation",
                        dependencyNotation = project(":core:analytics")
                    )
                    add(
                        configurationName = "implementation",
                        dependencyNotation = versionCatalog().findLibrary("androidx.navigation").get()
                    )
                    add(
                        configurationName = "implementation",
                        dependencyNotation = versionCatalog().findLibrary("androidx.hilt.navigation.compose").get()
                    )
                    add(
                        configurationName = "implementation",
                        dependencyNotation = versionCatalog().findLibrary("androidx.paging.compose").get()
                    )
                    add(
                        configurationName = "androidTestImplementation",
                        dependencyNotation = versionCatalog().findLibrary("androidx.compose.ui.test").get()
                    )
                    add(
                        configurationName = "androidTestDebugImplementation",
                        dependencyNotation = versionCatalog().findLibrary("androidx.compose.ui.testManifest").get()
                    )
                }
            }
        }
    }
}
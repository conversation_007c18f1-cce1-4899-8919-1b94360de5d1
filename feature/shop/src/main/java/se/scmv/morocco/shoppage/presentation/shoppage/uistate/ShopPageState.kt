package se.scmv.morocco.shoppage.presentation.shoppage.uistate

import se.scmv.morocco.domain.models.ListingAd
import se.scmv.morocco.domain.models.StoreProfileInfo

data class ShopPageScreenUiState(
    val isSuccess: <PERSON>olean = false,
    val isLoading: Boolean = false,
    val isError: Boolean = false,
    val storeProfile: StoreProfileInfo = StoreProfileInfo(),
    val adCount: Int = 0
)


data class ShowWarningDialogState(
    val isVisible: <PERSON>olean = false,
    val phoneNumber: String = "",
    val storeName: String = ""
)

sealed interface FilterUiState{
    data object Loading : FilterUiState
    data class Counted(val count: Int): FilterUiState
}

sealed interface ShopPageOneTimeEvents{
    data object FilterChange: ShopPageOneTimeEvents
    data object OnCallActionClick: ShopPageOneTimeEvents
    data class OnWhatsAppActionClick(val phoneNumber: String): ShopPageOneTimeEvents
    data object OnDissmisDialog: ShopPageOneTimeEvents
    data object OnNavigateToMakeCall: ShopPageOneTimeEvents
    data class OnUpdateFavoriteAdState(val storeListingAd: ListingAd.Published): ShopPageOneTimeEvents
}
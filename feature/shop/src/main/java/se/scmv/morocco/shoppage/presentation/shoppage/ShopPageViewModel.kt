package se.scmv.morocco.shoppage.presentation.shoppage

import androidx.annotation.StringRes
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.graphics.Color
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.toRoute
import androidx.paging.PagingData
import androidx.paging.cachedIn
import androidx.paging.map
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import se.scmv.morocco.analytics.AnalyticsHelper
import se.scmv.morocco.analytics.models.AnalyticsEvent
import se.scmv.morocco.analytics.models.Param
import se.scmv.morocco.common.lang.LocaleManager
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.domain.models.Account
import se.scmv.morocco.domain.models.FilterValue
import se.scmv.morocco.domain.models.ListingAd
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.models.StoreProfileInfo
import se.scmv.morocco.domain.models.ToggleConfigKey
import se.scmv.morocco.domain.models.filter.ListingCategoryFilters
import se.scmv.morocco.domain.models.filter.RangeParam
import se.scmv.morocco.domain.navigation.AppAction
import se.scmv.morocco.domain.navigation.AppNavigation
import se.scmv.morocco.domain.repositories.AccountRepository
import se.scmv.morocco.domain.repositories.ListingAdRepository
import se.scmv.morocco.orion.presentation.FilterSharedValueManager
import se.scmv.morocco.shoppage.presentation.ShopPageRoute
import se.scmv.morocco.shoppage.presentation.shoppage.uistate.FilterUiState
import se.scmv.morocco.shoppage.presentation.shoppage.uistate.ShopPageOneTimeEvents
import se.scmv.morocco.shoppage.presentation.shoppage.uistate.ShopPageScreenUiState
import se.scmv.morocco.shoppage.presentation.shoppage.uistate.ShowWarningDialogState
import se.scmv.morocco.ui.R
import se.scmv.morocco.ui.renderFailure
import se.scmv.morocco.ui.renderSuccess
import javax.inject.Inject

@HiltViewModel
class ShopPageViewModel @Inject constructor(
    private val sharedValueManager: FilterSharedValueManager,
    private val listingAdRepository: ListingAdRepository,
    private val appNavigation: AppNavigation,
    private val appAction: AppAction,
    private val analyticsHelper: AnalyticsHelper,
    private val accountRepository: AccountRepository,
    savedStateHandle: SavedStateHandle
) : ViewModel() {

    private val route = savedStateHandle.toRoute<ShopPageRoute>()

    private val _snackBarData = MutableStateFlow<Pair<Int, Color>?>(null)
    val snackBarData: StateFlow<Pair<Int, Color>?> = _snackBarData

    private val _storeInfoState = MutableStateFlow(ShopPageScreenUiState())
    val storeInfoState: StateFlow<ShopPageScreenUiState> get() = _storeInfoState.asStateFlow()

    val account: StateFlow<Account> = accountRepository.currentAccount.stateIn(
        scope = viewModelScope,
        initialValue = Account.NotConnected,
        started = SharingStarted.WhileSubscribed(5_000),
    )

    private val _filterItems = mutableStateOf<List<ListingCategoryFilters.BaseFilters>>(emptyList())

    private val _currentCategoryId = mutableStateOf("0")

    private val _hasImage = mutableStateOf(false)
    private val _hasPrice = mutableStateOf(false)
    private val _isHotDeal = mutableStateOf(false)
    private val _isUrgent = mutableStateOf(false)
    private val _hasDelivery = mutableStateOf(false)
    private val _offerShippingCity = mutableStateOf(false)

    private val adsCount = mutableIntStateOf(-1)

    private val _isBottomSheetIsShowing = mutableStateOf(false)
    val isBottomSheetIsShowing: State<Boolean> get() = _isBottomSheetIsShowing
    private val _rangeParams = mutableStateOf<List<RangeParam>>(emptyList())
    private val _eventChannel = MutableSharedFlow<ShopPageOneTimeEvents>(replay = 0)

    @OptIn(FlowPreview::class)
    val eventChannel = _eventChannel.asSharedFlow().debounce(300L)

    private val _navigateToAuthEvent = MutableSharedFlow<Unit>()
    val navigateToAuthEvent = _navigateToAuthEvent.asSharedFlow()

    private val bookmarkUpdates = MutableStateFlow<Map<String, Boolean>>(emptyMap())

    private val refreshAdsTrigger = MutableSharedFlow<Unit>()
    @OptIn(ExperimentalCoroutinesApi::class)
    val ads: Flow<PagingData<ListingAd.Published>> = refreshAdsTrigger
        .onStart { emit(Unit) }
        .flatMapLatest { getListingAds() }
        .cachedIn(viewModelScope)
        .combine(bookmarkUpdates) { pagingData, updates ->
            pagingData.map {
                if (updates.contains(it.listId)) {
                    val isFavorite = updates[it.listId] ?: it.isFavorite
                    it.copy(isFavorite = isFavorite)
                } else it
            }
        }

    private val _filterUiState = MutableStateFlow<FilterUiState>(FilterUiState.Loading)
    val filterUiState = _filterUiState.asStateFlow()

    private val selectedItemIds = mutableListOf("")

    private val _storeCategoryId = mutableStateOf("0")

    private val _allFilters = mutableStateOf<List<ListingCategoryFilters.BaseFilters>>(emptyList())
    val allFilters: State<List<ListingCategoryFilters.BaseFilters>> get() = _allFilters

    private val _selectedChips = mutableStateOf<List<Pair<String, Double>>?>(emptyList())
    private val _singleSelectedTextParams = mutableStateOf<List<Pair<String, String>>>(emptyList())
    private val _singleSelectedBooleanParams =
        mutableStateOf<List<Pair<String, Boolean>>>(emptyList())
    private val _priceRange: MutableState<Pair<Double, Double>?>? = null
    private val _brandAndModels = mutableStateOf<List<Pair<String, List<String>>>>(emptyList())


    private val _showWarningDialogState = MutableStateFlow(ShowWarningDialogState())

    @OptIn(FlowPreview::class)
    val showWarningDialogState: StateFlow<ShowWarningDialogState>
        get() = _showWarningDialogState
            .debounce(300L)
            .stateIn(
                scope = viewModelScope,
                started = SharingStarted.WhileSubscribed(5000),
                initialValue = ShowWarningDialogState()
            )

    init {
        viewModelScope.launch {
            eventChannel.collect { event ->
                handleEvent(event)
            }
        }
        getStoreInfo()
        getStoreAd()
        collectFilterValues()
    }

    private fun handleEvent(event: ShopPageOneTimeEvents) {
        when (event) {
            ShopPageOneTimeEvents.FilterChange -> {
                viewModelScope.launch { refreshAdsTrigger.emit(Unit) }
                getStoreAd()
            }

            ShopPageOneTimeEvents.OnCallActionClick -> {
                _showWarningDialogState.update {
                    it.copy(
                        isVisible = true,
                        storeName = _storeInfoState.value.storeProfile.name
                    )
                }
                trackShowPhoneAction(sellerId = route.storeId)
            }

            ShopPageOneTimeEvents.OnDissmisDialog -> {
                _showWarningDialogState.update { it.copy(isVisible = false) }
            }

            ShopPageOneTimeEvents.OnNavigateToMakeCall -> {
                trackCallAction(sellerId = route.storeId)
                _showWarningDialogState.update { it.copy(isVisible = false) }
            }

            is ShopPageOneTimeEvents.OnUpdateFavoriteAdState -> {
                _showWarningDialogState.update { it.copy(isVisible = false) }
                updateFavorite(storeListingAd = event.storeListingAd)
            }

            is ShopPageOneTimeEvents.OnWhatsAppActionClick -> {
                viewModelScope.launch {
                    appAction.openWhatsappPhoneNumber(event.phoneNumber)
                }
                trackWhatsappAction(sellerId = route.storeId)
            }
        }
    }



    fun triggerEvent(event: ShopPageOneTimeEvents) {
        viewModelScope.launch {
            _eventChannel.emit(event)
        }
    }

    private fun trackCallAction(sellerId: String) {
        analyticsHelper.logEvent(
            event = AnalyticsEvent(
                name = AnalyticsEvent.Types.SHOP_LEAD,
                properties = setOf(
                    Param(
                        key = AnalyticsEvent.ParamKeys.LANG,
                        value = LocaleManager.getCurrentLanguage()
                    ),
                    Param(
                        key = AnalyticsEvent.ParamKeys.LEAD_TYPE,
                        value = AnalyticsEvent.ParamValues.CALL
                    ),
                    Param(
                        key = AnalyticsEvent.ParamKeys.SELLER_ID,
                        value = sellerId
                    )
                )
            )
        )
    }

    private fun trackShowPhoneAction(sellerId: String) {
        analyticsHelper.logEvent(
            event = AnalyticsEvent(
                name = AnalyticsEvent.Types.SHOP_LEAD,
                properties = setOf(
                    Param(
                        key = AnalyticsEvent.ParamKeys.LANG,
                        value = LocaleManager.getCurrentLanguage()
                    ),
                    Param(
                        key = AnalyticsEvent.ParamKeys.LEAD_TYPE,
                        value = AnalyticsEvent.ParamValues.SHOW_PHONE
                    ),
                    Param(
                        key = AnalyticsEvent.ParamKeys.SELLER_ID,
                        value = sellerId
                    )
                )
            )
        )
    }

    fun trackWhatsappAction(sellerId: String) {
        analyticsHelper.logEvent(
            event = AnalyticsEvent(
                name = AnalyticsEvent.Types.SHOP_LEAD,
                properties = setOf(
                    Param(
                        key = AnalyticsEvent.ParamKeys.LANG,
                        value = LocaleManager.getCurrentLanguage()
                    ),
                    Param(
                        key = AnalyticsEvent.ParamKeys.LEAD_TYPE,
                        value = AnalyticsEvent.ParamValues.WHATSAPP
                    ),
                    Param(
                        key = AnalyticsEvent.ParamKeys.SELLER_ID,
                        value = sellerId
                    )
                )
            )
        )
    }

    /**
     * start collecting filters flow
     */
    private fun collectFilterValues() {
        viewModelScope.launch(Dispatchers.IO) {
            sharedValueManager.filterValueSharedFlow.collect { value ->
                when (value) {
                    is FilterValue.ToggleButtonValue -> {
                        checkToggleType(value)
                    }

                    is FilterValue.SingleTextParam -> {
                        _singleSelectedTextParams.value = value.params
                    }

                    is FilterValue.SmartDropDownIcon -> {
                        _brandAndModels.value = value.items
                    }

                    is FilterValue.SingleMatchNumeric -> {
                        _selectedChips.value = value.numericParams
                    }

                    is FilterValue.RangeParams -> {
                        _rangeParams.value = value.rangeParams!!
                    }

                    null -> {
                        clearParams()
                    }

                    is FilterValue.SingleBooleanParam -> {
                        _singleSelectedBooleanParams.value = value.params
                    }
                }
                handleEvent(ShopPageOneTimeEvents.FilterChange)
            }
        }
    }

    private fun checkToggleType(value: FilterValue.ToggleButtonValue) {
        when (value.toggle) {
            is ToggleConfigKey.HasDelivery -> {
                _hasDelivery.value = (value.toggle as ToggleConfigKey.HasDelivery).value
            }

            is ToggleConfigKey.HasImage -> {
                _hasImage.value = (value.toggle as ToggleConfigKey.HasImage).value
            }

            is ToggleConfigKey.HasPrice -> {
                _hasPrice.value = (value.toggle as ToggleConfigKey.HasPrice).value
            }

            is ToggleConfigKey.IsHotDeal -> {
                _isHotDeal.value = (value.toggle as ToggleConfigKey.IsHotDeal).value
            }

            is ToggleConfigKey.IsUrgent -> {
                _isUrgent.value = (value.toggle as ToggleConfigKey.IsUrgent).value
            }

            is ToggleConfigKey.OfferShippingWithInCity -> {
                _offerShippingCity.value =
                    (value.toggle as ToggleConfigKey.OfferShippingWithInCity).value
            }
        }
    }

    private fun getListingAds(): Flow<PagingData<ListingAd.Published>> {
        return listingAdRepository
            .getListingStoreAdByStoreIDForPagination(
                storeId = route.storeId,
                text = "",
                categoryId = _currentCategoryId.value.toInt(),
                hasImage = _hasImage.value,
                hasPrice = _hasPrice.value,
                priceRange = _priceRange?.value,
                cityIds = null,
                offersShipping = _hasDelivery.value,
                isHotDeal = _isHotDeal.value,
                isUrgent = _isUrgent.value,
                numericParams = _selectedChips.value,
                textParams = _brandAndModels.value,
                rangeParams = _rangeParams.value,
                singleBooleanParam = _singleSelectedBooleanParams.value,
                singleTextParam = _singleSelectedTextParams.value
            )
    }

    private fun clearParams() {
        _hasImage.value = false
        _hasPrice.value = false
        _hasDelivery.value = false
        _isUrgent.value = false
        _selectedChips.value = null
        _brandAndModels.value = emptyList()
        _rangeParams.value = emptyList()
        _singleSelectedTextParams.value = emptyList()
        _singleSelectedBooleanParams.value = emptyList()
        _priceRange?.value = null
    }

    fun showSnackBar(@StringRes message: Int, backgroundColor: Color) {
        _snackBarData.value = message to backgroundColor
    }

    fun snackBarShowing() {
        _snackBarData.value = null
    }

    fun updateCurrentCategoryId(categoryId: String) {
        _currentCategoryId.value = categoryId
        handleEvent(ShopPageOneTimeEvents.FilterChange)
    }


    fun updateBottomSheetShowing(boolean: Boolean) {
        _isBottomSheetIsShowing.value = boolean
    }

    private fun getStoreInfo() {
        viewModelScope.launch {
            when (val response = listingAdRepository.getStoreDetails(route.storeId)) {
                is Resource.Failure -> {
                    _storeInfoState.value = _storeInfoState.value.copy(
                        isSuccess = false,
                        isLoading = false,
                        isError = true,
                        adCount = 0,
                    )
                }

                is Resource.Success -> {
                    _showWarningDialogState.update {
                        it.copy(phoneNumber = response.data.phoneNumber)
                    }
                    _storeInfoState.value = _storeInfoState.value.copy(
                        isSuccess = true,
                        isLoading = false,
                        storeProfile = response.data,
                        adCount = response.data.count
                    )
                    if (response.data.adFiltersAllowed) {
                        _storeCategoryId.value = response.data.categoryId
                    }

                    trackScreen(response.data)
                }
            }
        }
    }

    private fun trackScreen(response: StoreProfileInfo) {
        analyticsHelper.logEvent(
            event = AnalyticsEvent(
                name = AnalyticsEvent.Types.SCREEN_VIEW,
                properties = setOf(
                    Param(
                        key = AnalyticsEvent.ParamKeys.LANG,
                        value = LocaleManager.getCurrentLanguage()
                    ),
                    Param(
                        key = AnalyticsEvent.ParamKeys.CONTENT_TYPE,
                        value = AnalyticsEvent.ScreensNames.SHOPE_PAGE
                    ),
                    Param(
                        key = AnalyticsEvent.ParamKeys.VERTICAL_ID,
                        value = response.categoryId
                    ),
                    Param(
                        key = AnalyticsEvent.ParamKeys.VERTICAL_NAME,
                        value = response.categoryName
                    ),
                    Param(
                        key = AnalyticsEvent.ParamKeys.SELLER_ID,
                        value = response.id
                    )
                )
            )
        )
    }


    private fun getStoreAd() {
        viewModelScope.launch {
            _filterUiState.value = FilterUiState.Loading
            val response = listingAdRepository.getListingStoreAdByStoreID(
                storeId = route.storeId,
                text = "",
                categoryId = _currentCategoryId.value.toInt(),
                hasImage = _hasImage.value,
                hasPrice = _hasPrice.value,
                priceRange = _priceRange?.value,
                cityIds = null,
                offersShipping = _hasDelivery.value,
                isHotDeal = _isHotDeal.value,
                isUrgent = _isUrgent.value,
                numericParams = _selectedChips.value,
                textParams = _brandAndModels.value,
                rangeParams = _rangeParams.value,
                singleBooleanParam = _singleSelectedBooleanParams.value,
                singleTextParam = _singleSelectedTextParams.value
            )
            when (response) {
                is Resource.Failure -> {
                    // FIXME Why Loading here ?
                    _filterUiState.value = FilterUiState.Loading
                }

                is Resource.Success -> {
                    _filterUiState.value = FilterUiState.Counted(response.data)
                    adsCount.value = response.data
                }
            }
        }
    }




    fun updateFiltersItem(filterItems: List<ListingCategoryFilters.BaseFilters>) {
        val brandModel = filterItems.filter {
            (it.type == "multiple_select_smart_dropdown_icon")
        }
        if (brandModel.isNotEmpty()) {
            _filterItems.value = filterItems
        }
    }

    fun updateAllFilters(filters: ListingCategoryFilters.Filters?) {
        if (filters != null) {
            _allFilters.value =
                filters.navBarFilters + filters.primaryFilters + filters.secondaryFilters
        }
    }

    fun navigateToAdView(listingAd: String) {
        appNavigation.navigateToAdView(listingAd)
    }

    private fun updateFavorite(storeListingAd: ListingAd.Published) {
        viewModelScope.launch {
            val isLoggedIn = accountRepository.currentAccount.firstOrNull()?.isLogged() ?: false
            if (!isLoggedIn) {
                _navigateToAuthEvent.emit(Unit)
                return@launch
            }
            val isFavorites = storeListingAd.isFavorite
            val result = if (isFavorites) {
                accountRepository.unBookmarkAd(storeListingAd.listId)
            } else {
                accountRepository.bookmarkAd(storeListingAd.listId)
            }
            when (result) {
                is Resource.Success -> {
                    val message = if (isFavorites) {
                        R.string.bookmarked_ads_screen_delete_ad_success
                    } else R.string.bookmarked_ads_screen_bookmark_ad_success
                    renderSuccess(UiText.FromRes(message))
                    bookmarkUpdates.update { map ->
                        map + (storeListingAd.listId to isFavorites.not())
                    }
                }

                is Resource.Failure -> renderFailure(result.error)
            }
        }
    }

    fun navigateToAuthentication() {
        // This will be handled by the calling activity/fragment
        // The actual navigation logic should be implemented in the UI layer
    }
}
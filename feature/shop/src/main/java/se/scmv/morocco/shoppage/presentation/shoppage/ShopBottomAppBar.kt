package se.scmv.morocco.shoppage.presentation.shoppage

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.BottomAppBar
import androidx.compose.material3.BottomAppBarScrollBehavior
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import se.scmv.morocco.designsystem.components.AvButtonWithTextAndIcon
import se.scmv.morocco.designsystem.theme.GreenLight
import se.scmv.morocco.designsystem.theme.StatusValid
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.shop.R

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ShopPageBottomAppBar(
    modifier: Modifier = Modifier,
    storeName: String,
    isVerifiedSeller: Boolean,
    onWhatsappClick : () -> Unit,
    showWhatsAppIcon : Boolean = true,
    onShowPhoneNumber: (String, String) -> Unit,
    scrollBehavior: BottomAppBarScrollBehavior?
) {
    BottomAppBar(
        modifier = modifier,
        containerColor = MaterialTheme.colorScheme.background,
        scrollBehavior = scrollBehavior
    ) {
        Row(
            horizontalArrangement = Arrangement.End,
            modifier = Modifier.wrapContentWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(
                modifier = Modifier
                    .padding(
                        horizontal = MaterialTheme.dimens.medium
                    )
                    .weight(1f)
            ) {
                Text(
                    overflow = TextOverflow.Ellipsis,
                    maxLines = 1,
                    text = storeName,
                    fontSize = MaterialTheme.typography.titleSmall.fontSize
                )
                if (isVerifiedSeller) {
                    Image(
                        painter = painterResource(R.drawable.verified_check_icon),
                        contentDescription = null,
                        modifier = Modifier
                            .padding(
                                start = MaterialTheme.dimens.small
                            )
                            .size(20.dp)
                    )
                }
            }

            if (showWhatsAppIcon){
                OutlinedButton(
                    modifier = Modifier
                        .padding(
                            MaterialTheme.dimens.small
                        )
                        .size(50.dp),
                    shape = CircleShape,
                    border = BorderStroke(width = 1.dp, color = GreenLight),
                    colors = ButtonDefaults.outlinedButtonColors(
                        contentColor = Color.Yellow,
                        containerColor = GreenLight
                    ),
                    contentPadding = PaddingValues(0.dp),
                    onClick = {
                        onWhatsappClick()
                    }
                ) {
                    Icon(
                        painter = painterResource(R.drawable.ic_whatsapp),
                        contentDescription = null,
                        tint = StatusValid,
                    )
                }
            }
            AvButtonWithTextAndIcon(
                icon = R.drawable.ic_call_grid,
                buttonTitle = stringResource(R.string.common_call)
            ){
                onShowPhoneNumber(storeName, "")
            }

        }

    }
}
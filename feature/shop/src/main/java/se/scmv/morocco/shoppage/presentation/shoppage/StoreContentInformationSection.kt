package se.scmv.morocco.shoppage.presentation.shoppage

import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.DateRange
import androidx.compose.material.icons.filled.LocationOn
import androidx.compose.material.icons.filled.Share
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.OutlinedCard
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextLayoutResult
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.util.fastForEachIndexed
import coil.compose.AsyncImage
import se.scmv.morocco.designsystem.components.SmartAsyncImage
import se.scmv.morocco.designsystem.theme.Gray400
import se.scmv.morocco.designsystem.theme.ImageCardColor
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.domain.models.StoreProfileInfo
import se.scmv.morocco.shop.R


@Composable
fun StoreInformationItem(
    isVerifiedSeller: Boolean,
    onShareClick : () -> Unit,
    storeProfileInfo: StoreProfileInfo
){
    OutlinedCard(
        modifier = Modifier.padding(
            horizontal = MaterialTheme.dimens.medium,
            vertical = MaterialTheme.dimens.medium
        ),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.background
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 3.dp
        )
    ) {
        Column {
            ContentHeader(
                storeName = storeProfileInfo.name,
                storeLogo = storeProfileInfo.storeLogo?: "",
                memberShipDate = storeProfileInfo.registrationDate.take(4),
                isVerifiedSeller = isVerifiedSeller,
            ) {
                onShareClick()
            }
            StoreInformationSection(storeProfileInfo)
        }
    }
}

@Composable
fun StoreInformationSection(
    storeProfileInfo: StoreProfileInfo
) {
    Column {
        Box(
            contentAlignment = Alignment.TopEnd,
            modifier = Modifier.height(250.dp)

        ) {
                LastAdImagesSection(
                    images = storeProfileInfo.lastActiveAdsImages,
                    isVerifiedSeller = storeProfileInfo.isVerifiedSeller,
                    storeArticlesCount = storeProfileInfo.numberOfActiveAds
                )
        }
        DescriptionText(description = storeProfileInfo.longDescription)
        StoreContactInfo(
            address = storeProfileInfo.address,
            storeWebSite = storeProfileInfo.storeWebsite
        )
    }

}

@Composable
fun DescriptionText(description: String = "") {
    Column(
        modifier = Modifier.padding(
            horizontal = MaterialTheme.dimens.big,
            vertical = MaterialTheme.dimens.small
        )
    ) {
        val miniMumLineLength = 3

        var expandedState by remember { mutableStateOf(false) }
        var showReadMoreButtonState by remember { mutableStateOf(false) }
        val maxLines = if (expandedState) 200 else miniMumLineLength

        Text(
            text = description,
            style = MaterialTheme.typography.bodySmall,
            overflow = TextOverflow.Ellipsis,
            maxLines = maxLines,
            fontStyle = MaterialTheme.typography.bodyMedium.fontStyle,
            fontSize = MaterialTheme.typography.bodyMedium.fontSize,
            onTextLayout = { textLayoutResult: TextLayoutResult ->
                try {
                    if (textLayoutResult.isLineEllipsized(miniMumLineLength - 1)) showReadMoreButtonState =
                        true
                } catch (_: Exception) {
                    showReadMoreButtonState = false
                }
            }
        )
        if (showReadMoreButtonState) {
            Text(
                text = if (expandedState) stringResource(R.string.read_less_label) else stringResource(
                    R.string.see_more_label
                ),
                textDecoration = TextDecoration.Underline,
                fontStyle = MaterialTheme.typography.bodyMedium.fontStyle,
                fontSize = MaterialTheme.typography.bodyMedium.fontSize,
                color = MaterialTheme.colorScheme.primary,
                modifier = Modifier.clickable {
                    expandedState = !expandedState
                }
            )
        }
    }
}

@Composable
fun StoreContactInfo(
    address: String,
    storeWebSite: String
) {
    val context = LocalContext.current
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(
                horizontal = MaterialTheme.dimens.big,
                vertical = MaterialTheme.dimens.medium
            )
    ) {
        if (address.isNotEmpty()) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = MaterialTheme.dimens.small)
            ) {
                Icon(
                    imageVector = Icons.Default.LocationOn,
                    contentDescription = null
                )
                Text(
                    modifier = Modifier.padding(
                        start = MaterialTheme.dimens.medium
                    ),
                    fontSize = MaterialTheme.typography.bodyMedium.fontSize,
                    text = address
                )
            }
        }
        if (storeWebSite.isNotEmpty()) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = MaterialTheme.dimens.medium)
            ) {
                Image(
                    painter = painterResource(R.drawable.ic_www_circle),
                    contentDescription = null,
                )

                Text(
                    modifier = Modifier
                        .padding(
                            start = MaterialTheme.dimens.medium
                        )
                        .clickable {
                            openUrlInBrowser(context, storeWebSite)
                        },
                    text = storeWebSite,
                    fontSize = MaterialTheme.typography.bodyMedium.fontSize,
                    color = MaterialTheme.colorScheme.primary,
                    maxLines = 1
                )
            }
        }
    }
}

@Composable
fun ContentHeader(
    storeLogo: String,
    storeName: String,
    memberShipDate: String,
    isVerifiedSeller: Boolean,
    onShareClick : () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(
                vertical = MaterialTheme.dimens.medium,
                horizontal = MaterialTheme.dimens.medium
            ),
        verticalAlignment = Alignment.CenterVertically
    ) {
        SmartAsyncImage(
            imageUrl = storeLogo,
            modifier = Modifier
                .size(40.dp)
                .clip(
                    CircleShape
                )
        )
        Column {
            Row {
                Text(
                    text = storeName,
                    fontStyle = MaterialTheme.typography.titleSmall.fontStyle,
                    fontSize = MaterialTheme.typography.titleSmall.fontSize,
                    modifier = Modifier
                        .padding(
                            start = MaterialTheme.dimens.medium
                        )
                        .widthIn(max = 200.dp)
                )
                if (isVerifiedSeller) {
                    Image(
                        painter = painterResource(R.drawable.verified_check_icon),
                        contentDescription = null,
                        modifier = Modifier
                            .padding(
                                start = MaterialTheme.dimens.small
                            )
                            .size(20.dp)
                    )
                }
            }
            Row(
                modifier = Modifier,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.DateRange,
                    contentDescription = null,
                    tint = Gray400,
                    modifier = Modifier
                        .padding(start = MaterialTheme.dimens.medium)
                        .size(15.dp),
                )
                Text(
                    modifier = Modifier.padding(
                        start = MaterialTheme.dimens.medium
                    ),
                    fontSize = MaterialTheme.typography.titleSmall.fontSize,
                    text = stringResource(R.string.member_since_label, memberShipDate),
                    color = Gray400
                )
            }
        }
        Spacer(modifier = Modifier.weight(1f))
        OutlinedButton(
            modifier = Modifier
                .padding(
                )
                .size(40.dp),
            shape = CircleShape,
            border = BorderStroke(1.dp, Color.Gray),
            contentPadding = PaddingValues(0.dp),
            colors = ButtonDefaults.outlinedButtonColors(
                contentColor = MaterialTheme.colorScheme.onSurface,
                containerColor = MaterialTheme.colorScheme.surface
            ),
            onClick = {
                onShareClick()
            }) {
            Icon(
                imageVector = Icons.Default.Share,
                contentDescription = null,
            )
        }

    }

}

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun LastAdImagesSection(
    images: List<String?> = emptyList(),
    isVerifiedSeller: Boolean = false,
    storeArticlesCount: Int
){
    val listState = rememberLazyListState()
    val indexImageToCheck = listOf(0,1,2)

    var currentImageIndex by remember { mutableStateOf(0) }

    LaunchedEffect(Unit) {
        listState.scrollToItem(currentImageIndex)
    }
    Box(
        modifier = Modifier.fillMaxWidth(),
        contentAlignment = Alignment.Center
    ) {
        FlowRow(
            modifier = Modifier
                .padding(
                    MaterialTheme.dimens.medium
                )
                .fillMaxWidth(),
            horizontalArrangement = Arrangement.Center,
            maxItemsInEachRow = 2
        ) {
            indexImageToCheck.fastForEachIndexed { index, _ ->
                val imageUrl = images.getOrNull(index)
                if (imageUrl != null){
                    AsyncImage(
                        modifier = Modifier
                            .align(Alignment.CenterVertically)
                            .width(50.dp)
                            .height(100.dp)
                            .weight(1f, true)
                            .padding(
                                MaterialTheme.dimens.medium
                            ),
                        model = imageUrl,
                        contentDescription = null,
                        contentScale = ContentScale.Crop,
                    )
                }else{
                    Image(
                        modifier = Modifier
                            .align(Alignment.CenterVertically)
                            .width(50.dp)
                            .height(100.dp)
                            .weight(1f, true)
                            .padding(
                                MaterialTheme.dimens.medium
                            ),
                        painter = painterResource(R.drawable.ic_no_image),
                        contentDescription = null,
                        contentScale = ContentScale.Inside,
                    )
                }
                if (index == 2){
                    Box(modifier = Modifier
                        .align(Alignment.CenterVertically)
                        .width(50.dp)
                        .height(100.dp)
                        .weight(1f, true)
                        .padding(
                            MaterialTheme.dimens.medium
                        )
                        .background(color = ImageCardColor),
                        contentAlignment = Alignment.Center,
                    ){
                        Text(
                            modifier = Modifier.wrapContentSize(),
                            text = stringResource(R.string.articles_label, storeArticlesCount),
                            textAlign = TextAlign.Center,
                            color = MaterialTheme.colorScheme.primary
                        )
                    }
                }
            }
        }
        if (isVerifiedSeller) {
            Image(
                painter = painterResource(R.drawable.ic_pro_badge),
                contentDescription = null,
                modifier = Modifier
                    .align(Alignment.TopStart)
                    .padding(
                        start = MaterialTheme.dimens.medium,
                    )
                    .size(50.dp)
                    .clip(CircleShape)
                    .background(color = Color.Black.copy(alpha = 0.6f))


            )
        }
    }
}

@Preview
@Composable
fun LastAdImagesSectionPreview(){
    LastAdImagesSection(
        storeArticlesCount = 100
    )
}

fun openUrlInBrowser(context: Context, url: String) {
    val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
    context.startActivity(intent)
}
package se.scmv.morocco.shoppage.presentation.shoppage

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.paging.LoadState
import androidx.paging.compose.LazyPagingItems
import androidx.paging.compose.itemContentType
import kotlinx.coroutines.launch
import se.scmv.morocco.designsystem.components.ChatHandler
import se.scmv.morocco.designsystem.components.EmptyNoResultsScreen
import se.scmv.morocco.designsystem.components.IndeterminateLoading
import se.scmv.morocco.designsystem.components.ListingCard
import se.scmv.morocco.designsystem.components.ShowWarningPhoneCallDialog
import se.scmv.morocco.designsystem.utils.ContactActionUtils
import se.scmv.morocco.domain.models.Account
import se.scmv.morocco.domain.models.ContactMethod
import se.scmv.morocco.domain.models.ListingAd
import se.scmv.morocco.domain.models.StoreProfileInfo
import se.scmv.morocco.domain.models.filter.ListingCategoryFilters
import se.scmv.morocco.shop.R
import se.scmv.morocco.shoppage.presentation.primaryFilter.PrimaryFilterRoute

@OptIn(ExperimentalFoundationApi::class)
@SuppressLint("SuspiciousIndentation")
@Composable
fun AdListing(
    modifier: Modifier = Modifier,
    storeProfileInfo: StoreProfileInfo,
    storeListingAds: LazyPagingItems<ListingAd.Published>,
    lazyListState: LazyListState,
    showSnackBar: (Int, Color) -> Unit,
    onFavoriteClick : (ListingAd.Published) -> Unit,
    navigateToAdView: (String) -> Unit,
    onCategoryChange: (String) -> Unit,
    updateFilters: (List<ListingCategoryFilters.BaseFilters>, ListingCategoryFilters.Filters?) -> Unit,
    onItemSelected: (List<ListingCategoryFilters.BaseFilters>, ListingCategoryFilters.Filters?, String, String) -> Unit,
    onEmptyStateActionClicked: () -> Unit,
    navigateToAuthentication: () -> Unit,
    account: Account,
    onSendMessage: (String, String) -> Unit
) {

    val context = LocalContext.current
    var showPhoneDialog by remember { mutableStateOf(false) }
    var phoneNumberToCall by remember { mutableStateOf("") }
    var vendorNameToCall by remember { mutableStateOf("") }
    var currentChatAdId by remember { mutableStateOf<String?>(null) }
    var currentChatAdDetails by remember { mutableStateOf<ListingAd.Published?>(null) }
    val scope = rememberCoroutineScope()
    val indexState = remember { mutableStateOf(0) }

    LazyColumn(
        state = lazyListState,
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        item(contentType = { "StoreInformationItem" }) {
            StoreInformationItem(
                isVerifiedSeller = storeProfileInfo.isVerifiedSeller,
                storeProfileInfo = storeProfileInfo,
                onShareClick = {
                    shareUrl(context = context, storeId = storeProfileInfo.id)
                }
            )
        }
        stickyHeader(contentType = { "Filters" }) {
                storeProfileInfo.let {
                    if (it.adFiltersAllowed){
                        PrimaryFilterRoute(
                            categoryId = it.categoryId,
                            adType = it.adType,
                            onCategoryChange = {primaryFilters, allFilters, id ->
                                onCategoryChange(id)
                                updateFilters(primaryFilters, allFilters)
                            }
                        ) { filterItems, allFilters, itemId ->
                            onItemSelected(filterItems, allFilters,it.categoryId, itemId)
                            scope.launch {
                                indexState.value = lazyListState.firstVisibleItemIndex
                                lazyListState.animateScrollToItem(indexState.value)
                            }
                        }
                    }
                }
        }
        when {
            storeListingAds.loadState.refresh is LoadState.Loading -> item(
                contentType = { "Loading" }
            ) {
                IndeterminateLoading()
            }
            storeListingAds.loadState.append.endOfPaginationReached && storeListingAds.itemCount == 0 ->
                item(contentType = "EmptyScreen ") {
                    EmptyNoResultsScreen(onSearchClick = onEmptyStateActionClicked)
                }

            else -> items(
                count = storeListingAds.itemCount,
                contentType = storeListingAds.itemContentType { "Avito Ads" }
            ) { index ->
                val item = storeListingAds[index]
                item?.let { it ->
                    ListingCard(
                        listingAd = it,
                        onFavoriteClick = { listingAd ->
                            onFavoriteClick(listingAd)
                        },
                        onAdClick = { adId ->
                            navigateToAdView(adId)
                        },
                        showCTAButtons = false,
                        onPriceInquiry = { contactMethod ->
                            when(contactMethod){
                                is ContactMethod.Chat -> {
                                    ContactActionUtils.handleChatCTA(context, contactMethod.adId) { adId ->
                                        currentChatAdId = adId
                                        currentChatAdDetails = it
                                    }
                                }
                                is ContactMethod.WhatsApp -> {
                                    ContactActionUtils.handleWhatsAppCTA(context, contactMethod.phoneNumber)
                                }
                                is ContactMethod.PhoneCall -> {
                                    phoneNumberToCall = contactMethod.phoneNumber
                                    vendorNameToCall = it.sellerName ?: "Vendeur"
                                    showPhoneDialog = true
                                }
                            }
                        }
                    )
                }
            }
        }

        storeListingAds.apply {
            when{
                loadState.append is LoadState.Loading -> {
                    item(contentType = "Load more") { IndeterminateLoading() }
                }
                loadState.refresh is LoadState.Error -> {
                    showSnackBar(R.string.common_network_error_verify_and_try_later, Color.Red)
                }
                loadState.append is LoadState.Error -> {
                    showSnackBar(R.string.common_network_error_verify_and_try_later, Color.Red)
                }
            }
        }
    }

    // Chat handler for FirstMessageBottomSheet
    ChatHandler(
        account = account,
        adId = currentChatAdId,
        adDetails = currentChatAdDetails,
        onNavigateToAuthentication = navigateToAuthentication,
        onSendMessage = { message ->
            currentChatAdId?.let { adId ->
                onSendMessage(adId, message)
            }
        },
        onDismiss = {
            currentChatAdId = null
            currentChatAdDetails = null
        }
    )

    // Phone call confirmation dialog
    if (showPhoneDialog) {
        ShowWarningPhoneCallDialog(
            vendorName = vendorNameToCall,
            phoneNumber = phoneNumberToCall,
            onDismissRequest = { showPhoneDialog = false },
            onCallClick = {
                showPhoneDialog = false
                ContactActionUtils.launchPhoneCall(context, phoneNumberToCall)
            }
        )
    }
}

fun shareUrl(context: Context, storeId: String) {
    val sendIntent = Intent().apply {
        action = Intent.ACTION_SEND
        putExtra(Intent.EXTRA_TEXT, "https://www.avito.ma/fr/boutique?id=$storeId")
        type = "text/plain"
    }
    val shareIntent = Intent.createChooser(sendIntent,
        context.getString(R.string.share_choser_title))
    context.startActivity(shareIntent)
}
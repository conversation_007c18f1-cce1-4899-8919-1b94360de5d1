package se.scmv.morocco.shoppage.presentation.primaryFilter

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import se.scmv.morocco.domain.repositories.ConfigRepository
import javax.inject.Inject

@HiltViewModel
class PrimaryFilterViewModel @Inject constructor(
    private val configRepository: ConfigRepository
): ViewModel() {


    private val _primaryFilterUiState = MutableStateFlow(PrimaryFilterViewState())
    val primaryFilterUiState get() = _primaryFilterUiState.asStateFlow()


    private var _categoryId = ""

    fun getFilters(storeCategoryId: String, adType: String){
        _primaryFilterUiState.value = _primaryFilterUiState.value.copy(
            isLoading = true
        )
        viewModelScope.launch {
            val response = configRepository.getFilters(
                categoryId = storeCategoryId,
                type = adType
            )
            _primaryFilterUiState.value = _primaryFilterUiState.value.copy(
                isLoading = false,
                primaryFilters = response.primaryFilters,
                allFilters = response
            )
        }
    }

    private fun getFiltersCategories(){
        _primaryFilterUiState.value = _primaryFilterUiState.value.copy(
            isLoading = true
        )
        viewModelScope.launch {

                val response = configRepository.getFiltersCategories(categoryId = _categoryId)
                if (response != null){
                    _primaryFilterUiState.value = _primaryFilterUiState.value.copy(
                        isLoading = false,
                        categories = response
                    )
                }else{
                    _primaryFilterUiState.value = _primaryFilterUiState.value.copy(
                        isLoading = false,
                        isError = true,
                        errorMessage = "" //TODO handle error by using error message
                    )
                }
        }
    }

    fun updateCategoryId(id: String, adType: String){
        _categoryId = id
        getFiltersCategories()
        getFilters(storeCategoryId = id, adType = adType)
    }

}
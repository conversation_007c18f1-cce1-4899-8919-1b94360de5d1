package se.scmv.morocco.shoppage.presentation.shoppage

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.BottomAppBarDefaults
import androidx.compose.material3.BottomAppBarScrollBehavior
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Snackbar
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.material3.rememberBottomAppBarState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.State
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.paging.compose.LazyPagingItems
import androidx.paging.compose.collectAsLazyPagingItems
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import se.scmv.morocco.designsystem.components.ShowWarningPhoneCallDialog
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.domain.models.Account
import se.scmv.morocco.domain.models.ListingAd
import se.scmv.morocco.domain.models.filter.ListingCategoryFilters
import se.scmv.morocco.orion.presentation.FilterBottomSheetRootScreen
import se.scmv.morocco.shoppage.presentation.common.makePhoneCall
import se.scmv.morocco.shoppage.presentation.shoppage.uistate.FilterUiState
import se.scmv.morocco.shoppage.presentation.shoppage.uistate.ShopPageOneTimeEvents
import se.scmv.morocco.shoppage.presentation.shoppage.uistate.ShopPageScreenUiState
import se.scmv.morocco.shoppage.presentation.shoppage.uistate.ShowWarningDialogState

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ShopScreenRoute(
    onBackPressed: () -> Unit,
    navigateToAdView: (String) -> Unit,
    navigateToAuth: () -> Unit,
    shopPageViewModel: ShopPageViewModel = hiltViewModel()
) {
    val bottomAppBarState = rememberBottomAppBarState()
    val filters by shopPageViewModel.allFilters
    val state by shopPageViewModel.storeInfoState.collectAsState()
    val showWarningDialogState = shopPageViewModel.showWarningDialogState.collectAsState()
    val adsPagingItems = shopPageViewModel.ads.collectAsLazyPagingItems()
    val snackBarData by shopPageViewModel.snackBarData.collectAsState()
    val isBottomSheetIsShowing = shopPageViewModel.isBottomSheetIsShowing.value
    val filterUiState = shopPageViewModel.filterUiState.collectAsState()
    val account by shopPageViewModel.account.collectAsState()

    val scrollBehavior = BottomAppBarDefaults.exitAlwaysScrollBehavior(state = bottomAppBarState)
    ShopScreen(
        onCallAction = { shopPageViewModel.triggerEvent(it) },
        scrollBehavior = scrollBehavior,
        filters = filters,
        state = state,
        showWarningDialogState = showWarningDialogState,
        adsPagingItems = adsPagingItems,
        snackBarData = snackBarData,
        filterUiState = filterUiState,
        navigateToAdView = navigateToAdView,
        onAdFavoriteClick = { listingAd ->
            shopPageViewModel.triggerEvent(ShopPageOneTimeEvents.OnUpdateFavoriteAdState(listingAd))
        },
        showSnackBar = {resMessage, bgColor ->
            shopPageViewModel.showSnackBar(message = resMessage, backgroundColor = bgColor)
        },
        updateFiltersItem = {shopPageViewModel.updateFiltersItem(it)},
        updateAllFilters = {shopPageViewModel.updateAllFilters(it)},
        updateCurrentCategoryId = { shopPageViewModel.updateCurrentCategoryId(it) },
        updateBottomSheetShowing = {shopPageViewModel.updateBottomSheetShowing(it)},
        isBottomSheetIsShowing = isBottomSheetIsShowing,
        onWhatsAppClick = {shopPageViewModel.triggerEvent(ShopPageOneTimeEvents.OnWhatsAppActionClick(it))},
        snackBarShowing = {shopPageViewModel.snackBarShowing()},
        onFilterChange = {shopPageViewModel.triggerEvent(ShopPageOneTimeEvents.FilterChange)},
        onDismiss = {shopPageViewModel.triggerEvent(ShopPageOneTimeEvents.OnDissmisDialog)},
        onBackPressed = onBackPressed,
        navigateToAuthentication = { shopPageViewModel.navigateToAuthentication() },
        account = account,
        onSendMessage = { _, _ ->
            //CTA buttons not handling in listing shop page
        }
    )

    LaunchedEffect(Unit) {
        shopPageViewModel.navigateToAuthEvent.collectLatest {
            navigateToAuth()
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ShopScreen(
    scrollBehavior: BottomAppBarScrollBehavior,
    filters: List<ListingCategoryFilters.BaseFilters>,
    state: ShopPageScreenUiState,
    showWarningDialogState: State<ShowWarningDialogState>,
    adsPagingItems: LazyPagingItems<ListingAd.Published>,
    snackBarData: Pair<Int, Color>?,
    filterUiState: State<FilterUiState>,
    onAdFavoriteClick : (ListingAd.Published) -> Unit,
    showSnackBar : (Int, Color) -> Unit,
    navigateToAdView: (String) -> Unit,
    onCallAction: (ShopPageOneTimeEvents) -> Unit,
    updateFiltersItem: (List<ListingCategoryFilters.BaseFilters>) -> Unit,
    updateAllFilters : (ListingCategoryFilters. Filters?) -> Unit,
    updateCurrentCategoryId : (String) -> Unit,
    listState: LazyListState = rememberLazyListState(),
    snackBarShowing : () -> Unit,
    isBottomSheetIsShowing: Boolean,
    onWhatsAppClick: (String) -> Unit,
    onDismiss: () -> Unit,
    onFilterChange: () -> Unit,
    updateBottomSheetShowing : (Boolean) -> Unit,
    onBackPressed: () -> Unit,
    navigateToAuthentication: () -> Unit,
    account: Account,
    onSendMessage: (String, String) -> Unit
){
    val scope = rememberCoroutineScope()
    val context = LocalContext.current
    val snackBarHostState = remember { SnackbarHostState() }

    LaunchedEffect(snackBarData) {
        snackBarData?.let { (message, _) ->
            snackBarHostState.currentSnackbarData?.dismiss()
            snackBarHostState.showSnackbar(
                message = context.getString(message)
            )
            snackBarShowing()
        }
    }

    var selectedItemId by remember {
        mutableStateOf("")
    }

    Scaffold(
        containerColor = MaterialTheme.colorScheme.background,
        snackbarHost = {
            SnackbarHost(
                hostState = snackBarHostState,
                modifier = Modifier.padding(8.dp)
            ){ _ ->
                snackBarData?.let { ( message , color) ->
                    Snackbar(
                        containerColor = color
                    ){
                        Text(text = context.getString(message), color = Color.White)
                    }
                }
            }
        },
        topBar = {
            ShopPageTopBar{
                onBackPressed()
            }
        },
        bottomBar = {
            if (!isBottomSheetIsShowing){
                ShopPageBottomAppBar(
                    storeName = state.storeProfile.name,
                    isVerifiedSeller = state.storeProfile.isVerifiedSeller,
                    showWhatsAppIcon = state.storeProfile.phoneNumber.canWhatsApp(),
                    onWhatsappClick = {
                        onWhatsAppClick(showWarningDialogState.value.phoneNumber)
                    },
                    onShowPhoneNumber = { _, _ ->
                        onCallAction(ShopPageOneTimeEvents.OnCallActionClick)
                    },
                    scrollBehavior = scrollBehavior
                )
            }
        }
    )
    { paddingValues ->
        Box(
            modifier = Modifier
                .padding(paddingValues)
                .fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            if (showWarningDialogState.value.isVisible){
                ShowWarningPhoneCallDialog(
                    vendorName = showWarningDialogState.value.storeName,
                    phoneNumber = showWarningDialogState.value.phoneNumber,
                    onDismissRequest = {
                        onCallAction(ShopPageOneTimeEvents.OnDissmisDialog)
                        onDismiss()
                    }
                ){
                    onDismiss()
                    onCallAction(ShopPageOneTimeEvents.OnNavigateToMakeCall)
                    context.makePhoneCall(showWarningDialogState.value.phoneNumber)
                }
            }
            AdListing(
                modifier = Modifier.fillMaxSize(),
                storeProfileInfo = state.storeProfile,
                storeListingAds = adsPagingItems,
                lazyListState = listState,
                onFavoriteClick = { listingAd ->
                    onAdFavoriteClick(listingAd)
                },
                showSnackBar = { message, color ->
                    showSnackBar(message, color)
                },
                onCategoryChange = {
                    updateCurrentCategoryId(it)
                },
                navigateToAdView = { navigateToAdView(it) },
                updateFilters = {primaryFilter, allFilters ->
                    updateFiltersItem(primaryFilter)
                    updateAllFilters(allFilters)
                },
                onItemSelected = { primaryFilter, allFilters, categoryId, itemId ->
                    updateFiltersItem(primaryFilter)
                    updateAllFilters(allFilters)
                    scope.launch {
                        selectedItemId = itemId
                        updateBottomSheetShowing(true)
                    }
                },
                onEmptyStateActionClicked = { updateBottomSheetShowing(true) },
                navigateToAuthentication = navigateToAuthentication,
                account = account,
                onSendMessage = { adId, message -> onSendMessage(adId, message) }
            )
            AnimatedVisibility(
                visible = isBottomSheetIsShowing,
                enter = slideInVertically(
                    initialOffsetY = { fullHeight -> fullHeight },
                    animationSpec = tween(durationMillis = 500)
                ) + fadeIn(animationSpec = tween(durationMillis = 500)),
                exit = slideOutVertically(
                    targetOffsetY = { fullHeight -> fullHeight },
                    animationSpec = tween(durationMillis = 500)
                ) + fadeOut(animationSpec = tween(durationMillis = 500))
            ) {
                FilterBottomSheetRootScreen(
                    modifier = Modifier
                        .padding(
                            top = MaterialTheme.dimens.extraExtraBig
                        )
                        .align(Alignment.BottomCenter),
                    filters = filters,
                    isLoading = filterUiState.value is FilterUiState.Loading,
                    adsCount = {
                        when(filterUiState.value){
                            is FilterUiState.Counted -> {
                                (filterUiState.value as FilterUiState.Counted).count
                            }
                            FilterUiState.Loading -> {
                                0
                            }
                        }
                    },
                    onApplyFilterClick = {
                       onFilterChange()
                        updateBottomSheetShowing(false)
                    },
                ) {
                    updateBottomSheetShowing(false)
                }
            }
        }
    }
}




@Preview
@Composable
fun ShopPageHeaderPreview() {
    AvitoTheme {
        ShopPageTopBar {}
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Preview
@Composable
fun ShopPageBottomAppBarPreview() {
    AvitoTheme {
        ShopPageBottomAppBar(
            storeName = "Store name",
            isVerifiedSeller = true,
            onWhatsappClick = {

            },
            onShowPhoneNumber = { _, _ ->

            },
            scrollBehavior = null
        )
    }

}
@Preview
@Composable
fun StoreContactInfoPreview() {
    StoreContactInfo(
        address = "casablanca bd mohamed 6",
        storeWebSite = "www.avito.ma"
    )
}

@Preview
@Composable
fun DescriptionTextPreview() {
    DescriptionText(
        "Nos articles produits et équipements sont destinés aussi bien aux\n" +
                "professionnels qu aux particuliers conçus par les meilleurs équipementiers\n" +
                "ils sont à ce titre destinés à répondre à vos attentes les plus diverses\n" +
                "En outre il vous est possible tout en profitant d un rapport qualité-prix\n" +
                "exceptionnel d équiper vos foyers salles de sport ainsi que divers espaces de\n" +
                "compétition ou de jeux\n" +
                "A noter que notre société dispose de la gamme intégrale d article de\n" +
                "produit et d équipement sportifs\n" +
                "A l offre variée s ajoute l accueil chaleureux ainsi qu une démarche\n" +
                "qualité totale et la mise du client au centre de nos intérêts et préoccupations"
    )
}

fun String.canWhatsApp(): Boolean{
    return this.isNotBlank() && !this.startsWith("05") && !this.startsWith("08")
}


package se.scmv.morocco.shoppage.presentation.primaryFilter

import se.scmv.morocco.domain.models.filter.ListingCategoryFilters
import se.scmv.morocco.domain.models.filter.ListingFilterCategories

data class PrimaryFilterViewState(
    val isLoading: Boolean = false,
    val isError: Boolean = false,
    val errorMessage: String = "",
    val categories:  ListingFilterCategories? = null,
    val primaryFilters: List<ListingCategoryFilters.BaseFilters>? = null,
    val allFilters: ListingCategoryFilters.Filters? = null
)

package se.scmv.morocco.shop

import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.annotation.RequiresApi
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.ui.Modifier
import androidx.core.view.WindowCompat
import androidx.navigation.NavType
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import androidx.navigation.navArgument
import dagger.hilt.android.AndroidEntryPoint
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.shoppage.presentation.shoppage.ShopScreenRoute

@AndroidEntryPoint
class ShopPageActivity : ComponentActivity() {

    var storeId: String? = null

    @RequiresApi(Build.VERSION_CODES.N)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        WindowCompat.setDecorFitsSystemWindows(window, false)
        storeId = intent.getStringExtra(STORE_ID_EXTRA)
        enableEdgeToEdge()
        setContent {
            AvitoTheme  {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    contentColor = MaterialTheme.colorScheme.background
                ) {
                    // We use the navigation here so that we can use SavedStateHandle to get the
                    // storeId directly in ShopPageViewModel and get data in init{} block.
                    storeId?.let { id ->
                        val navController = rememberNavController()
                        NavHost(
                            navController = navController,
                            startDestination = SHOP_SCREEN_ROUTE,
                        ) {
                            composable(
                                route = SHOP_SCREEN_ROUTE,
                                arguments = listOf(
                                    navArgument(STORE_ID_EXTRA) {
                                        type = NavType.StringType
                                        defaultValue = id
                                    }
                                )
                            ) {
                                ShopScreenRoute(
                                    onBackPressed = { onBackPressedDispatcher.onBackPressed() },
                                    navigateToAdView = {},
                                    navigateToAuth = {}
                                )
                            }
                        }
                    }
                }
            }
        }
    }

    companion object{
        const val STORE_ID_EXTRA = "store_id"
        private const val SHOP_SCREEN_ROUTE = "SHOP_SCREEN_ROUTE/{$STORE_ID_EXTRA}"

        /**
         * Use this factory method to open this activity using the provided parameters.
         *
         * @param context context.
         * @param storeId the store id.
         */
        fun open(context: Context, storeId: String) {
            context.startActivity(
                Intent(context, ShopPageActivity::class.java).apply {
                    putExtra(STORE_ID_EXTRA, storeId)
                    if (context !is android.app.Activity) {
                        addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    }
                }
            )
        }
    }
}
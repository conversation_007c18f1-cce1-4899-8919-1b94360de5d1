package se.scmv.morocco.shoppage.presentation.primaryFilter

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.background
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.LocationOn
import androidx.compose.material3.AssistChip
import androidx.compose.material3.AssistChipDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.State
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import coil.compose.AsyncImage
import coil.decode.SvgDecoder
import coil.request.ImageRequest
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.domain.models.filter.ListingCategoryFilters
import se.scmv.morocco.shop.R


@Composable
fun PrimaryFilterRoute(
    modifier: Modifier = Modifier,
    categoryId: String,
    adType: String,
    onCategoryChange: (List<ListingCategoryFilters.BaseFilters>, ListingCategoryFilters.Filters?,String) -> Unit,
    primaryFilterViewModel: PrimaryFilterViewModel = hiltViewModel(),
    onItemSelected: (List<ListingCategoryFilters.BaseFilters>, ListingCategoryFilters.Filters?, id: String) -> Unit
){
    val state = primaryFilterViewModel.primaryFilterUiState.collectAsState()
    PrimaryFilterItem(
        categoryId = categoryId,
        adType = adType,
        state = state,
        updateCategoryId = { id, type ->
            primaryFilterViewModel.updateCategoryId(
                id = id,
                adType = type
            )
        },
        onCategoryChange = { primaryFilters, allFilters, id ->
            onCategoryChange(primaryFilters, allFilters, id)
        },
        getFilters = { storeId, adType ->
            primaryFilterViewModel.getFilters(storeCategoryId = storeId, adType = adType)
        },
    ) { baseFilters, filters, id ->
        onItemSelected(baseFilters, filters, id)
    }
}

@Composable
fun PrimaryFilterItem(
    categoryId: String,
    state: State<PrimaryFilterViewState>,
    adType: String,
    onCategoryChange: (List<ListingCategoryFilters.BaseFilters>, ListingCategoryFilters.Filters?,String) -> Unit,
    updateCategoryId: (String, String) -> Unit,
    getFilters : (String, String) -> Unit,
    onItemSelected: (List<ListingCategoryFilters.BaseFilters>, ListingCategoryFilters.Filters?, id:String) -> Unit
) {

    var selectedItem by remember {
        mutableStateOf("")
    }

    LaunchedEffect(categoryId) {
        updateCategoryId(
            categoryId,
            adType
        )
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(
                bottom = MaterialTheme.dimens.medium,
                start = MaterialTheme.dimens.medium,
                end = MaterialTheme.dimens.medium
            )
            .background(
                color = MaterialTheme.colorScheme.background
            )
    ) {
        var showChildItems by remember {
            mutableStateOf(false)
        }
        var itemId by remember {
            mutableStateOf("")
        }
        var showCategory by remember {
            mutableStateOf(true)
        }
        var showPrimaryFilter by remember {
            mutableStateOf(false)
        }
        /**
         * Check if there are children for the current category
         */
        if (!state.value.categories?.children.isNullOrEmpty() && showCategory) {
            LazyRow {
                state.value.categories?.children?.let {
                    items(items = it) { item ->
                        ChildrenCategoryItem(
                            label = item.name,
                            icon = item.icon,
                            id = item.category.id
                        ) {
                            onCategoryChange(state.value.primaryFilters.orEmpty(), state.value.allFilters,item.category.id)
                            item.children?.let { childItems ->
                                // Toggle visibility for child items if there are children
                                showChildItems = !(itemId == item.category.id && showChildItems)
                                // Hide primary filter if child items are present
                                if (showPrimaryFilter) {
                                    showPrimaryFilter = false
                                }
                            } ?: run {
                                getFilters(it,adType)
                                // Show primary filter and hide child items
                                showPrimaryFilter = true
                                showChildItems = false
                            }
                            selectedItem = itemId

                            // Update the current item ID and hide category
                            itemId = it
                            showCategory = false
                        }
                    }
                }

            }
        }
        AnimatedVisibility(
            visible = showChildItems
        ) {
            LazyRow {
                item {
                    AssistChip(
                        colors = AssistChipDefaults.assistChipColors(
                            containerColor = MaterialTheme.colorScheme.surfaceContainerHighest,
                            labelColor = MaterialTheme.colorScheme.onSurface,
                            leadingIconContentColor = MaterialTheme.colorScheme.onSurface
                        ),
                        onClick = {
                        showCategory = true
                        showChildItems = false
                    }, label = {
                        Text(
                            modifier = Modifier.padding(
                                vertical = MaterialTheme.dimens.medium
                            ),
                            text = stringResource(R.string.common_back),
                        )
                    },
                        leadingIcon = {
                            Icon(
                                imageVector = Icons.AutoMirrored.Default.ArrowBack,
                                contentDescription = null
                            )
                        },
                        border = AssistChipDefaults.assistChipBorder(enabled = false)
                    )
                }
                state.value.categories?.children?.filter {
                    it.category.id == itemId
                }?.first()?.children?.let {
                    items(items = it) {
                        ChildrenCategoryItem(
                            label = it.name,
                            icon = it.icon,
                            id = it.category.id
                        ) { categoryId ->
                            onCategoryChange(state.value.primaryFilters.orEmpty(), state.value.allFilters,categoryId)
                            getFilters(categoryId,adType)
                            showPrimaryFilter = true
                            showChildItems =false
                        }
                    }
                }
            }
        }

        if (!state.value.primaryFilters.isNullOrEmpty()) {
            AnimatedVisibility(
                visible = showPrimaryFilter
            ) {
                LazyRow {
                    item {
                        AssistChip(onClick = {
                            showPrimaryFilter = false
                            showCategory = true
                        }, label = {
                            Text(
                                modifier = Modifier.padding(
                                    vertical = MaterialTheme.dimens.medium
                                ),
                                text = stringResource(R.string.common_back),
                                )
                        },
                            leadingIcon = {
                                Icon(
                                    imageVector = Icons.AutoMirrored.Default.ArrowBack,
                                    contentDescription = null
                                )
                            },
                            border = AssistChipDefaults.assistChipBorder(enabled = false),
                            colors = AssistChipDefaults.assistChipColors(
                                containerColor = MaterialTheme.colorScheme.surfaceContainerHighest,
                                labelColor = MaterialTheme.colorScheme.onSurface,
                                leadingIconContentColor = MaterialTheme.colorScheme.onSurface
                            )
                        )
                    }
                    state.value.primaryFilters?.let {
                        items(items = it) {
                            ChildrenCategoryItem(
                                label = it.name!!,
                                icon = it.icon!!,
                                id = it.id
                            ) { id ->
                                onItemSelected(
                                    state.value.primaryFilters!!,
                                    state.value.allFilters,
                                    id
                                )
                            }
                        }
                    }
                    item {
                        AssistChip(
                            onClick = {
                            onItemSelected(
                                state.value.primaryFilters!!,
                                state.value.allFilters,
                                selectedItem
                            )
                        }, label = {
                            Text(
                                modifier = Modifier.padding(
                                    vertical = MaterialTheme.dimens.medium
                                ),
                                text = stringResource(R.string.more_filter_label)
                            )
                        },
                            leadingIcon = {
                                Icon(
                                    painter = painterResource(R.drawable.ic_filter),
                                    contentDescription = null
                                )
                            },
                            border = AssistChipDefaults.assistChipBorder(enabled = false),
                            colors = AssistChipDefaults.assistChipColors(
                                MaterialTheme.colorScheme.surfaceContainerHighest,
                                labelColor = MaterialTheme.colorScheme.onSurface,
                                leadingIconContentColor = MaterialTheme.colorScheme.onSecondary
                            )
                        )
                    }
                }
            }
        }

    }

}

@Composable
fun ChildrenCategoryItem(
    label: String,
    icon: Any,
    id: String,
    isPreviewMode: Boolean = false,
    onItemClick: (String) -> Unit
) {
    val isDarkMod = isSystemInDarkTheme()
    AssistChip(
        modifier = Modifier
            .padding(
            horizontal = MaterialTheme.dimens.small,
        ),
        border = AssistChipDefaults.assistChipBorder(false),
        colors = AssistChipDefaults.assistChipColors(
            containerColor = MaterialTheme.colorScheme.surfaceContainerHighest,
            labelColor = MaterialTheme.colorScheme.onSurface,
        ),
        label = {
            Text(
                modifier = Modifier.padding(
                    vertical = MaterialTheme.dimens.medium
                ),
                text = label,
                fontSize = MaterialTheme.typography.titleSmall.fontSize
            )
        },
        leadingIcon = {
            if (isPreviewMode) {
                Icon(
                    imageVector = Icons.Default.LocationOn,
                    contentDescription = null
                )
            } else {
                AsyncImage(
                    modifier = Modifier.size(20.dp),
                    model = ImageRequest.Builder(LocalContext.current)
                        .data(icon)
                        .decoderFactory(SvgDecoder.Factory())
                        .build(),
                    contentDescription = null,
                )
            }
        },
        onClick = {
            onItemClick(id)
        })

}

@Preview
@Composable
fun ChildrenCategoryItemPreview() {
    AvitoTheme {
        ChildrenCategoryItem(
            label = "Smart Phone",
            icon = "",
            id = "1",
            isPreviewMode = true
        ) {

        }
    }

}
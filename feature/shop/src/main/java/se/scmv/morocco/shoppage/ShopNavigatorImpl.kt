package se.scmv.morocco.shoppage

import android.content.Context
import dagger.hilt.android.qualifiers.ApplicationContext
import se.scmv.morocco.domain.navigation.ShopNavigator
import se.scmv.morocco.shop.ShopPageActivity
import javax.inject.Inject

class ShopNavigatorImpl @Inject constructor(
    @ApplicationContext private val appContext: Context
) : ShopNavigator {
    override fun openShop(shopId: String) {
        ShopPageActivity.open(appContext, shopId)

    }
}

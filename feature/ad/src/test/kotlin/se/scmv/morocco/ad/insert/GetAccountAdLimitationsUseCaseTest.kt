package se.scmv.morocco.ad.insert

import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Assert.assertNull
import org.junit.Before
import org.junit.Test
import se.scmv.morocco.ad.R
import se.scmv.morocco.designsystem.components.AvAlertType
import se.scmv.morocco.domain.models.AdLimitations
import se.scmv.morocco.domain.models.AdTypeKey
import se.scmv.morocco.domain.models.Category
import se.scmv.morocco.domain.models.NetworkAndBackendErrors
import se.scmv.morocco.domain.models.NetworkErrors
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.repositories.AdRepository

class BuildLapViewStateUseCaseTest {

    private lateinit var useCase: GetAccountAdLimitationsUseCase
    private lateinit var adRepository: AdRepository

    private val categoryId = "123"
    private val adTypeKey = AdTypeKey.SELL

    @Before
    fun setup() {
        adRepository = mockk()
        useCase = GetAccountAdLimitationsUseCase(adRepository)
    }

    @Test
    fun `invoke returns null when repository returns Failure`() = runTest {
        // Given
        coEvery { adRepository.getAdLimitations(any(), any()) } returns Resource.Failure(
            NetworkAndBackendErrors.Network(NetworkErrors.UNKNOWN)
        )

        // When
        val result = useCase(categoryId, adTypeKey, false)

        // Then
        assertNull(result)
    }

    @Test
    fun `case 1 - user has free insertions remaining`() = runTest {
        // Given
        val userAdCount = 1
        val freeLimit = 3
        val categoryFree = Category(id = "free-cat", name = "Free Category", icon = "", trackingName = "")
        val limitations = AdLimitations(
            userCategoryAdCount = userAdCount,
            configuredCategoryFreeLimit = freeLimit,
            configuredCategoryStoreLimit = 10,
            categoryFree = categoryFree,
            categoryStore = null
        )

        coEvery { adRepository.getAdLimitations(any(), any()) } returns Resource.Success(limitations)

        // When - not edit mode
        val result = useCase(categoryId, adTypeKey, isEdit = false)

        // Then
        assertEquals(R.string.ad_insert_lap_limitation1, result?.messageResId)
        assertEquals(AvAlertType.Info, result?.alertType)
        assertEquals(3, result?.messageArgs?.size)
        assertEquals("2", result?.messageArgs?.get(0)) // userAdCount + 1 because not edit mode
        assertEquals("3", result?.messageArgs?.get(1))
        assertEquals("Free Category", result?.messageArgs?.get(2))

        // When - edit mode
        val resultEdit = useCase(categoryId, adTypeKey, isEdit = true)

        // Then
        assertEquals("1", resultEdit?.messageArgs?.get(0)) // userAdCount because edit mode
    }

    @Test
    fun `case 1 - not shown when only 1 free insertion remains`() = runTest {
        // Given (remaining = 1)
        val userAdCount = 2
        val freeLimit = 3
        val limitations = AdLimitations(
            userCategoryAdCount = userAdCount,
            configuredCategoryFreeLimit = freeLimit,
            configuredCategoryStoreLimit = 10,
            categoryFree = Category(
                id = "free-cat",
                name = "Free Category",
                icon = "",
                trackingName = ""
            ),
            categoryStore = null
        )

        coEvery {
            adRepository.getAdLimitations(
                any(),
                any()
            )
        } returns Resource.Success(limitations)

        // When
        val result = useCase(categoryId, adTypeKey, isEdit = false)

        // Then
        assertNull(result)
    }

    @Test
    fun `case 2 - user reached free insertions limit`() = runTest {
        // Given
        val userAdCount = 3
        val freeLimit = 3
        val limitations = AdLimitations(
            userCategoryAdCount = userAdCount,
            configuredCategoryFreeLimit = freeLimit,
            configuredCategoryStoreLimit = 10,
            categoryFree = Category(id = "free-cat", name = "Free Category", icon = "", trackingName = ""),
            categoryStore = null
        )

        coEvery { adRepository.getAdLimitations(any(), any()) } returns Resource.Success(limitations)

        // When - not edit mode
        val result = useCase(categoryId, adTypeKey, isEdit = false)

        // Then
        assertEquals(R.string.ad_insert_lap_limitation2, result?.messageResId)
        assertEquals(AvAlertType.Info, result?.alertType)
        assertEquals(1, result?.messageArgs?.size)
        assertEquals("4", result?.messageArgs?.get(0)) // userAdCount + 1 because not edit mode

        // When - edit mode
        val resultEdit = useCase(categoryId, adTypeKey, isEdit = true)

        // Then
        assertEquals("3", resultEdit?.messageArgs?.get(0)) // userAdCount because edit mode
    }

    @Test
    fun `case 3 - category requires payment from first insertion`() = runTest {
        // Given
        val limitations = AdLimitations(
            userCategoryAdCount = 0,
            configuredCategoryFreeLimit = 0,  // Key condition for case 3
            configuredCategoryStoreLimit = 10,
            categoryFree = null,
            categoryStore = null
        )

        coEvery { adRepository.getAdLimitations(any(), any()) } returns Resource.Success(limitations)

        // When
        val result = useCase(categoryId, adTypeKey, isEdit = false)

        // Then
        assertEquals(R.string.ad_insert_lap_limitation3, result?.messageResId)
        assertEquals(AvAlertType.Warning, result?.alertType)
        assertEquals(0, result?.messageArgs?.size)
    }

    @Test
    fun `case 4 - user reached store limit`() = runTest {
        // Given
        val userAdCount = 10
        val storeLimit = 10
        val storeCategory =Category(id = "store-cat", name = "Store Category", icon = "", trackingName = "")
        val limitations = AdLimitations(
            userCategoryAdCount = userAdCount,
            configuredCategoryFreeLimit = 3,
            configuredCategoryStoreLimit = storeLimit,
            categoryFree = Category(id = "free-cat", name = "Free Category", icon = "", trackingName = ""),
            categoryStore = storeCategory
        )

        coEvery { adRepository.getAdLimitations(any(), any()) } returns Resource.Success(limitations)

        // When - not edit mode
        val result = useCase(categoryId, adTypeKey, isEdit = false)

        // Then
        assertEquals(R.string.ad_insert_lap_limitation4, result?.messageResId)
        assertEquals(AvAlertType.Info, result?.alertType)
        assertEquals(2, result?.messageArgs?.size)
        assertEquals("11", result?.messageArgs?.get(0)) // userAdCount + 1 because not edit mode
        assertEquals("Store Category", result?.messageArgs?.get(1))

        // When - edit mode
        val resultEdit = useCase(categoryId, adTypeKey, isEdit = true)

        // Then
        assertEquals("10", resultEdit?.messageArgs?.get(0)) // userAdCount because edit mode
    }

    @Test
    fun `null returned when no conditions met`() = runTest {
        // Given
        val limitations = AdLimitations(
            userCategoryAdCount = 2,
            configuredCategoryFreeLimit = 5,  // User hasn't reached free limit; remaining is 3 so no LAP message
            configuredCategoryStoreLimit = null, // No store limit configured
            categoryFree = Category(id = "free-cat", name = "Free Category", icon = "", trackingName = ""),
            categoryStore = null
        )

        coEvery { adRepository.getAdLimitations(any(), any()) } returns Resource.Success(limitations)

        // When
        val result = useCase(categoryId, adTypeKey, isEdit = false)

        // Then
        assertNull(result)
    }

    @Test
    fun `edge case - user at exactly free limit`() = runTest {
        // Given - user count is equal to free limit
        val limitations = AdLimitations(
            userCategoryAdCount = 3,
            configuredCategoryFreeLimit = 3,
            configuredCategoryStoreLimit = 10,
            categoryFree = Category(id = "free-cat", name = "Free Category", icon = "", trackingName = ""),
            categoryStore = null
        )

        coEvery { adRepository.getAdLimitations(any(), any()) } returns Resource.Success(limitations)

        // When
        val result = useCase(categoryId, adTypeKey, isEdit = false)

        // Then - should be case 2
        assertEquals(R.string.ad_insert_lap_limitation2, result?.messageResId)
    }

    @Test
    fun `edge case - no category name available`() = runTest {
        // Given
        val limitations = AdLimitations(
            userCategoryAdCount = 1,
            configuredCategoryFreeLimit = 3,
            configuredCategoryStoreLimit = 10,
            categoryFree = null,  // No category name
            categoryStore = null
        )

        coEvery { adRepository.getAdLimitations(any(), any()) } returns Resource.Success(limitations)

        // When
        val result = useCase(categoryId, adTypeKey, isEdit = false)

        // Then
        assertEquals(R.string.ad_insert_lap_limitation1, result?.messageResId)
        assertEquals("", result?.messageArgs?.get(2)) // Empty string for missing category name
    }
}
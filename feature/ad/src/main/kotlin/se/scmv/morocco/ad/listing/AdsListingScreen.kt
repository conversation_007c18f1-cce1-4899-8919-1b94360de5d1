package se.scmv.morocco.ad.listing

import android.annotation.SuppressLint
import android.util.Log
import android.view.View
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.pulltorefresh.PullToRefreshBox
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.paging.LoadState
import androidx.paging.compose.collectAsLazyPagingItems
import androidx.paging.compose.itemContentType
import com.google.ads.mediation.admob.AdMobAdapter
import com.google.android.gms.ads.AdListener
import com.google.android.gms.ads.AdSize
import com.google.android.gms.ads.LoadAdError
import com.google.android.gms.ads.admanager.AdManagerAdRequest
import com.google.android.gms.ads.admanager.AdManagerAdView
import kotlinx.coroutines.flow.collectLatest
import se.scmv.morocco.ad.R
import se.scmv.morocco.common.extensions.toBundle
import se.scmv.morocco.designsystem.components.AvHorizontalChipGroup
import se.scmv.morocco.designsystem.components.ChatHandler
import se.scmv.morocco.designsystem.components.ChipData
import se.scmv.morocco.designsystem.components.ListingCard
import se.scmv.morocco.designsystem.components.ScreenEmptyState
import se.scmv.morocco.designsystem.components.ScreenErrorState
import se.scmv.morocco.designsystem.components.ShowWarningPhoneCallDialog
import se.scmv.morocco.designsystem.components.listing.ExtendedDedNotification
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.designsystem.utils.ContactActionUtils
import se.scmv.morocco.designsystem.utils.IconUrlOrDrawable
import se.scmv.morocco.domain.models.Account
import se.scmv.morocco.domain.models.ContactMethod
import se.scmv.morocco.domain.models.ListingAd
import se.scmv.morocco.ui.getErrorAsUiText
import se.scmv.morocco.ui.isEmpty
import se.scmv.morocco.ui.isError

@SuppressLint("MissingPermission")
@OptIn(ExperimentalMaterial3Api::class, ExperimentalFoundationApi::class)
@Composable
fun AdsListingRoute(
    account: Account,
    navigateToFilters: (String?) -> Unit,
    navigateToAdView: (ListingAd) -> Unit,
    navigateToNewConstruction: (String) -> Unit,
    navigateToAuth: () -> Unit,
    navigateToChat: (String) -> Unit,
    notifyCategoryChanged: (String, String?) -> Unit,
    notifyCancelExtendedDeliveryClicked: () -> Unit,
    notifyCancelExtendedSearchClicked: () -> Unit,
    newItemsLoaded: () -> Unit,
    modifier: Modifier = Modifier,
    viewModel: AdsListingViewModel = hiltViewModel()
) {
    val context = LocalContext.current
    var showPhoneDialog by remember { mutableStateOf(false) }
    var phoneNumberToCall by remember { mutableStateOf("") }
    var vendorNameToCall by remember { mutableStateOf("") }
    var currentChatAdId by remember { mutableStateOf<String?>(null) }
    var currentChatAdDetails by remember { mutableStateOf<ListingAd?>(null) }
    var showLoadingProgress by remember { mutableStateOf(false) }
    val viewState = viewModel.viewState.collectAsStateWithLifecycle().value
    val adItems = viewModel.ads.collectAsLazyPagingItems()
    val bookmarks = viewModel.bookmarkUpdates.collectAsStateWithLifecycle().value

    PullToRefreshBox(
        modifier = modifier.fillMaxSize(),
        isRefreshing = adItems.loadState.refresh == LoadState.Loading && adItems.itemCount == 0,
        onRefresh = adItems::refresh
    ) {
        val context = LocalContext.current
        val topBannerAd = remember {
            AdManagerAdView(context).apply {
                setAdSize(AdSize.LARGE_BANNER)
                adUnitId = context.getString(R.string.dfp_adunit_listing_large)
                loadAd(AdManagerAdRequest.Builder().build())
            }
        }
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(bottom = 120.dp)
        ) {
            item(key = "categories") {
                val categoriesListState = rememberLazyListState()
                HorizontalCategoryItems(lazyListState = categoriesListState,
                    items = viewState.childrenCategories.ifEmpty { viewState.popularCategories },
                    canBackToParentCategories = viewState.canBackToParentCategories,
                            onBackClicked = viewModel::onBackToParentCategories,
                    onCategoryItemClick = viewModel::onCategoryClicked)
                        LaunchedEffect(viewState.childrenCategories, viewState.popularCategories) {
                            categoriesListState.scrollToItem(0)
                }
            }
            stickyHeader(key = "sticky_filters") {
                val filtersListState = rememberLazyListState()
                if (viewState.filters.isNotEmpty()) {
                    Box(modifier = Modifier.background(MaterialTheme.colorScheme.background)) {
                        AvHorizontalChipGroup(
                            modifier = Modifier.fillMaxWidth(),
                            state = filtersListState,
                            chips = viewState.filters,
                            onChipClicked = { navigateToFilters(it.id) },
                            staticChip = ChipData(
                                id = "static",
                                name = stringResource(R.string.common_filter),
                                selected = false,
                                icon = IconUrlOrDrawable.Drawable(R.drawable.ic_filter)
                            ),
                            onStaticChipClicked = { navigateToFilters(null) }
                        )
                    }
                }
                LaunchedEffect(viewState.popularCategories) {
                    filtersListState.scrollToItem(0)
                }
            }
            item(key = "top_banner") {
                AndroidView(
                    modifier = Modifier
                        .fillMaxWidth()
                        .wrapContentHeight()
                        .padding(top = MaterialTheme.dimens.small),
                    factory = { context -> topBannerAd },
                    update = { adView -> }
                )
            }
            if (adItems.isEmpty().not() && viewState.extendedDelivery) {
                item(key = "extended_delivery") {
                    ExtendedDedNotification(
                        message = stringResource(R.string.search_extended_to_delivery),
                        buttonText = R.string.cancel,
                        onClick = notifyCancelExtendedDeliveryClicked
                    )
                }
            }
            when {
                adItems.isEmpty() -> item(key = "empty_state") {
                    ScreenEmptyState(
                        modifier = Modifier
                            .padding(bottom = MaterialTheme.dimens.extraExtraBig)
                            .fillMaxWidth(),
                        title = R.string.common_oups,
                        description = R.string.listing_screen_empty_state_description,
                        actionText = R.string.common_search,
                        image = R.drawable.img_no_result_illustration,
                        onActionClicked = { navigateToFilters(null) }
                    )
                }

                adItems.isError() -> item(key = "error_state") {
                    ScreenErrorState(
                        modifier = Modifier.fillMaxSize(),
                        title = stringResource(R.string.common_oups),
                        description = adItems.getErrorAsUiText().getValue(context),
                        actionText = stringResource(R.string.common_refresh),
                        onActionClicked = adItems::retry
                    )
                }

                else -> {
                    items(
                        count = adItems.itemCount,
                        contentType = adItems.itemContentType()
                    ) { index ->
                        when (val ad = adItems[index]) {
                            is ListingAd.DfpBanner -> BannerAd(ad.paramsValues)

                            is ListingAd.ExtendedSearch -> ExtendedDedNotification(
                                message = ad.types.joinToString("\n") {
                                    context.getString(it.toMessage())
                                },
                                buttonText = R.string.extended_search_keep_current_search,
                                onClick = notifyCancelExtendedSearchClicked
                            )

                            is ListingAd.NewConstruction -> ListingCard(
                                listingAd = ad,
                                onFavoriteClick = { _ -> },
                                onAdClick = { externalLink ->
                                    navigateToNewConstruction(externalLink)
                                },
                                isShopSession = account.isShop(),
                                showCTAButtons = true,
                                onPriceInquiry = { contactMethod ->
                                    when (contactMethod) {
                                        is ContactMethod.Chat -> {
                                            ContactActionUtils.handleChatCTA(
                                                context,
                                                contactMethod.adId
                                            ) { adId ->
                                                viewModel.handleMessagingClick(adId, ad)
                                            }
                                        }

                                        is ContactMethod.WhatsApp -> {
                                            ContactActionUtils.handleWhatsAppCTA(
                                                context,
                                                contactMethod.phoneNumber
                                            )
                                        }

                                        is ContactMethod.PhoneCall -> {
                                            phoneNumberToCall = contactMethod.phoneNumber
                                            vendorNameToCall = "Vendeur"
                                            showPhoneDialog = true
                                        }
                                    }
                                }
                            )

                            is ListingAd.Premium -> ListingCard(
                                listingAd = ad,
                                onFavoriteClick = { ad -> viewModel.updateAdFavoriteStatus(ad) },
                                onAdClick = { adId -> navigateToAdView(ad) },
                                isShopSession = account.isShop(),
                                showCTAButtons = true,
                                onPriceInquiry = { contactMethod ->
                                    when (contactMethod) {
                                        is ContactMethod.Chat -> {
                                            ContactActionUtils.handleChatCTA(
                                                context,
                                                contactMethod.adId
                                            ) { adId ->
                                                viewModel.handleMessagingClick(adId, ad)
                                            }
                                        }

                                        is ContactMethod.WhatsApp -> {
                                            ContactActionUtils.handleWhatsAppCTA(
                                                context,
                                                contactMethod.phoneNumber
                                            )
                                        }

                                        is ContactMethod.PhoneCall -> {
                                            phoneNumberToCall = contactMethod.phoneNumber
                                            vendorNameToCall = ad.sellerName ?: "Vendeur"
                                            showPhoneDialog = true
                                        }
                                    }
                                }
                            )

                            is ListingAd.Published -> {
                                val adToDisplay = if (bookmarks.containsKey(ad.listId)) {
                                    ad.copy(isFavorite = bookmarks[ad.listId] == true)
                                } else {
                                    ad
                                }
                                ListingCard(
                                    listingAd = adToDisplay,
                                    onFavoriteClick = { publishedAd ->
                                        viewModel.updateAdFavoriteStatus(adToDisplay)
                                    },
                                    isShopSession = account.isShop(),
                                    onAdClick = { adId -> navigateToAdView(adToDisplay) },
                                    showCTAButtons = true,
                                    onPriceInquiry = { contactMethod ->
                                        when (contactMethod) {
                                            is ContactMethod.WhatsApp -> {
                                                ContactActionUtils.handleWhatsAppCTA(
                                                    context,
                                                    contactMethod.phoneNumber
                                                )
                                            }

                                            is ContactMethod.PhoneCall -> {
                                                phoneNumberToCall = contactMethod.phoneNumber
                                                vendorNameToCall =
                                                    adToDisplay.sellerName ?: "Vendeur"
                                                showPhoneDialog = true
                                            }

                                            is ContactMethod.Chat -> {
                                                ContactActionUtils.handleChatCTA(
                                                    context,
                                                    contactMethod.adId
                                                ) { adId ->
                                                    viewModel.handleMessagingClick(
                                                        adId,
                                                        adToDisplay
                                                    )
                                                }
                                            }
                                        }
                                    }
                                )
                            }

                            else -> {}
                        }
                    }
                }
            }
        }
    }

    // Chat handler for FirstMessageBottomSheet
    ChatHandler(
        account = account,
        adId = currentChatAdId,
        adDetails = currentChatAdDetails,
        onNavigateToAuthentication = navigateToAuth,
        onSendMessage = { message ->
            currentChatAdId?.let { adId ->
                viewModel.sendFirstMessage(adId, message)
            }
        },
        onDismiss = {
            currentChatAdId = null
            currentChatAdDetails = null
        }
    )

    // Phone call confirmation dialog
    if (showPhoneDialog) {
        ShowWarningPhoneCallDialog(
            vendorName = vendorNameToCall,
            phoneNumber = phoneNumberToCall,
            onDismissRequest = { showPhoneDialog = false },
            onCallClick = {
                showPhoneDialog = false
                ContactActionUtils.launchPhoneCall(context, phoneNumberToCall)
            }
        )
    }

    // Loading indicator for first message sending
    if (showLoadingProgress) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(bottom = MaterialTheme.dimens.default),
            contentAlignment = Alignment.Center
        ) {
            CircularProgressIndicator(
                modifier = Modifier
                    .size(MaterialTheme.dimens.mediumBig)
                    .padding(bottom = 32.dp),
                color = MaterialTheme.colorScheme.primary
            )
        }
    }

    LaunchedEffect(Unit) {
        viewModel.oneTimeEvents.collectLatest { event ->
            when (event) {
                is AdsListingOneTimeEvents.NotifyCategoryChanged -> notifyCategoryChanged(
                    event.categoryId, event.adTypeKey
                )

                is AdsListingOneTimeEvents.ShowHideProgress -> {
                    showLoadingProgress = event.isLoading
                }

                is AdsListingOneTimeEvents.NavigateToAuth -> navigateToAuth()
                is AdsListingOneTimeEvents.ShowFirstConversationBtmSheet -> {
                    currentChatAdId = event.adId
                    currentChatAdDetails = event.ad
                }

                is AdsListingOneTimeEvents.ShowConversation -> {
                    navigateToChat(event.conversationId)
                }
            }
        }
    }
}

@Composable
fun BannerAd(paramsValues: Map<String, String>) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .wrapContentHeight()
    ) {
        AndroidView(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight(),
            factory = { context ->
                AdManagerAdView(context).apply {
                    adUnitId = context.getString(R.string.dfp_adunit_listing)
                    setAdSizes(AdSize.MEDIUM_RECTANGLE, AdSize.FLUID)
                    val adRequest: AdManagerAdRequest = AdManagerAdRequest.Builder()
                        .addNetworkExtrasBundle(AdMobAdapter::class.java, paramsValues.toBundle())
                        .build()

                    adListener = object : AdListener() {
                        override fun onAdLoaded() {
                            <EMAIL> = View.VISIBLE
                            Log.d("TAG----", "onAdLoaded: ")
                        }

                        override fun onAdImpression() {
                            super.onAdImpression()
                            Log.d("TAG----", "onAdImpression: ")
                        }

                        override fun onAdFailedToLoad(loadAdError: LoadAdError) {
                            <EMAIL> = View.GONE
                            Log.d("TAG----", "onAdFailedToLoad: ")
                        }

                        override fun onAdClosed() {
                            <EMAIL> = View.GONE
                            Log.d("TAG----", "onAdClosed: ")
                        }
                    }

                    loadAd(adRequest)
                }
            },
            update = {}
        )
    }
}
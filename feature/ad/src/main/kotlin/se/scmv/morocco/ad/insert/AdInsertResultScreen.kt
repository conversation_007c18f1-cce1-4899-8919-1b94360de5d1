package se.scmv.morocco.ad.insert

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import se.scmv.morocco.ad.R
import se.scmv.morocco.designsystem.components.AvPrimaryButton
import se.scmv.morocco.designsystem.components.AvTopAppBar
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.Blue50
import se.scmv.morocco.designsystem.theme.dimens

@Composable
fun AdInsertResultScreen(
    accountName: String,
    navigateBack: () -> Unit,
    navigateToAccountAds: () -> Unit,
    modifier: Modifier = Modifier
) {
    Scaffold(
        modifier = modifier,
        topBar = {
            AvTopAppBar(onNavigationIconClicked = navigateBack)
        }
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(it)
                .padding(horizontal = MaterialTheme.dimens.screenPaddingHorizontal)
                .padding(top = MaterialTheme.dimens.extraBig)
                .padding(bottom = MaterialTheme.dimens.big),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.big)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Blue50, MaterialTheme.shapes.small)
                    .padding(MaterialTheme.dimens.bigger),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = stringResource(R.string.ad_insert_result_title, accountName),
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary
                )
                Spacer(modifier = Modifier.height(MaterialTheme.dimens.medium))
                Text(
                    text = stringResource(R.string.ad_insert_result_subtitle),
                    style = MaterialTheme.typography.bodyLarge,
                    fontWeight = FontWeight.Light,
                    color = MaterialTheme.colorScheme.primary
                )
            }

            Image(
                modifier = Modifier
                    .fillMaxWidth()
                    .aspectRatio(1.5f),
                painter = painterResource(R.drawable.img_ad_insert_success),
                contentDescription = "Ad inserted visual",
            )

            Column(
                modifier = Modifier.fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = stringResource(R.string.ad_insert_result_descriptiom),
                    style = MaterialTheme.typography.labelLarge,
                    fontWeight = FontWeight.Thin,
                    textAlign = TextAlign.Center
                )
                Spacer(modifier = Modifier.height(MaterialTheme.dimens.medium))
                Text(
                    text = stringResource(R.string.ad_insert_result_subdescriptiom),
                    style = MaterialTheme.typography.labelMedium,
                )
            }
            Spacer(modifier = Modifier.weight(1f))
            AvPrimaryButton(
                modifier = Modifier.fillMaxWidth(),
                text = "Mes annonces",
                onClick = navigateToAccountAds
            )
        }
    }
}

@Preview
@Composable
private fun AdInsertResultScreenPreview() {
    AvitoTheme {
        AdInsertResultScreen(accountName = "Jhon doe", navigateBack = {}, navigateToAccountAds = {})
    }
}
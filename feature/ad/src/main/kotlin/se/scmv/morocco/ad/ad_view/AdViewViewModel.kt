package se.scmv.morocco.ad.ad_view

import android.util.Log
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.toRoute
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import se.scmv.morocco.ad.R
import se.scmv.morocco.ad.ad_view.state.AdViewOneTimeEvents
import se.scmv.morocco.ad.ad_view.state.AdViewUiEvents
import se.scmv.morocco.ad.ad_view.state.AdViewViewState
import se.scmv.morocco.ad.navigation.AdViewRoute
import se.scmv.morocco.analytics.AnalyticsHelper
import se.scmv.morocco.analytics.impl.trackTikTokPurchase
import se.scmv.morocco.analytics.impl.trackTikTokViewContent
import se.scmv.morocco.analytics.models.AnalyticsEvent
import se.scmv.morocco.analytics.models.AnalyticsEvent.ParamKeys.ELEMENT_NAME
import se.scmv.morocco.analytics.models.AnalyticsEvent.Types.ELEMENT_CLICKED
import se.scmv.morocco.analytics.models.AnalyticsEvent.Types.ELEMENT_DISPLAYED
import se.scmv.morocco.analytics.models.Param
import se.scmv.morocco.authentication.presentation.AuthenticationActivity
import se.scmv.morocco.common.lang.LocaleManager
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.domain.models.Account
import se.scmv.morocco.domain.models.AdDetails
import se.scmv.morocco.domain.models.AdTouchingPoint
import se.scmv.morocco.domain.models.CachedAdDetails
import se.scmv.morocco.domain.models.CarCheckConfig
import se.scmv.morocco.domain.models.Category
import se.scmv.morocco.domain.models.City
import se.scmv.morocco.domain.models.LoanSimulatorConfig
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.repositories.AccountRepository
import se.scmv.morocco.domain.repositories.AdRepository
import se.scmv.morocco.domain.repositories.AdViewRepository
import se.scmv.morocco.domain.repositories.ConfigRepository
import se.scmv.morocco.domain.shared.BookmarkAdEvent
import se.scmv.morocco.domain.shared.BookmarkAdsEventManager
import se.scmv.morocco.domain.usecases.GetAndFilterTouchingPointsUseCase
import se.scmv.morocco.ui.asUiText
import se.scmv.morocco.ui.renderFailure
import se.scmv.morocco.ui.renderSuccess
import javax.inject.Inject

@HiltViewModel
class AdViewViewModel @Inject constructor(
    private val adViewRepository: AdViewRepository,
    private val adRepository: AdRepository,
    private val getAndFilterTouchingPointsUseCase: GetAndFilterTouchingPointsUseCase,
    private val analyticsHelper: AnalyticsHelper,
    private val configRepository: ConfigRepository,
    private val accountRepository: AccountRepository,
    private val bookmarkAdsEventManager: BookmarkAdsEventManager,
    savedStateHandle: SavedStateHandle
) : ViewModel() {

    private val route = savedStateHandle.toRoute<AdViewRoute>()

    private val _viewState: MutableStateFlow<AdViewViewState> = MutableStateFlow(
        AdViewViewState.Loading
    )
    val viewState = _viewState.asStateFlow()

    private val _oneTimeEvents = MutableSharedFlow<AdViewOneTimeEvents>()
    val oneTimeEvents = _oneTimeEvents.asSharedFlow()

    private val _cachedAdDetails = MutableStateFlow<CachedAdDetails?>(null)
    val cachedAdDetails: StateFlow<CachedAdDetails?> = _cachedAdDetails

    private val _touchingPoints = MutableStateFlow(emptyList<AdTouchingPoint>())
    val touchingPoints: StateFlow<List<AdTouchingPoint>> = _touchingPoints.asStateFlow()

    private val _loanConfig = MutableStateFlow<Pair<LoanSimulatorConfig, Int>?>(null)
    val loanConfig = _loanConfig.asStateFlow()

    // StateFlow for car check config
    private val _carCheckConfig = MutableStateFlow<CarCheckConfig?>(null)
    val carCheckConfig: StateFlow<CarCheckConfig?> = _carCheckConfig

    var isSubmitting by mutableStateOf(false)

    var submissionResult by mutableStateOf<Boolean?>(null)

    private val _isConnectedShop = mutableStateOf(false)
    val isConnectedShop = _isConnectedShop

    private val _isAccountAd = mutableStateOf(false)
    val isAccountAd = _isAccountAd

    init {
        viewModelScope.launch {
            _isConnectedShop.value = isConnectedShop()
        }
        loadAdView(route.adId)
    }

    fun saveCachedAdDetails(adId: String, cachedAdDetails: CachedAdDetails) {
        viewModelScope.launch {
            adViewRepository.saveCachedAdDetails(adId, cachedAdDetails)
        }
    }

    private fun loadAdView(adId: String) {
        viewModelScope.launch {
            _oneTimeEvents.emit(AdViewOneTimeEvents.ShowHideProgress(true))

            when (val result = adViewRepository.getAdDetails(adId)) {
                is Resource.Success -> {
                    val data = result.data
                    _viewState.value = AdViewViewState.Success(details = data)
                    with(data.ad) {
                        _isAccountAd.value = isAccountAd(seller?.getSellerAccountId())
                        recordAdViewEvent(this)
                        category?.let {
                            getAdTouchingPoints(category = it, city = cityArea)
                            getLoanSimulatorConfig(
                                categoryId = it.id,
                                adPrice = price.getCurrentPrice(),
                                typeKey = type?.key?.name
                            )
                            fetchCarCheckConfig(
                                adCategoryId = it.id,
                                type = type?.key?.name,
                                cityId = cityArea?.id
                            )
                        }
                    }
                }

                is Resource.Failure -> {
                    _viewState.value = AdViewViewState.Error(result.error.asUiText())
                }
            }

            _oneTimeEvents.emit(AdViewOneTimeEvents.ShowHideProgress(false))
        }
    }

    fun sendFirstMessage(adId: String?, text: String) {
        if (adId.isNullOrBlank()) {
            renderFailure(UiText.FromRes(R.string.common_unexpected_error_verify_and_try_later))
            return
        }

        viewModelScope.launch {
            _oneTimeEvents.emit(AdViewOneTimeEvents.ShowHideProgress(true))

            val result = adViewRepository.sendFirstMessageToSeller(adId, text)

            _oneTimeEvents.emit(AdViewOneTimeEvents.ShowHideProgress(false))

            when (result) {
                is Resource.Success -> {
                    renderSuccess(UiText.FromRes(R.string.message_sent_successfully))
                }

                is Resource.Failure -> {
                    renderFailure(result.error.asUiText())
                }
            }
        }
    }

    private fun getLoanSimulatorConfig(categoryId: String, adPrice: Int?, typeKey: String?) {
        viewModelScope.launch {
            val configResult = configRepository.getLoanSimulatorConfig(categoryId, typeKey)
            configResult?.let {
                val price: Int = adPrice ?: 0
                _loanConfig.update { _ -> it to price }
            } ?: run {
                // If the config is not available we just log this info,
                // because the loan simulator config is already null in the viewState.
                Log.i(
                    "AdViewLoanSimulator: ",
                    "Loan simulator unavailable for this category"
                )
            }
        }
    }

    private fun fetchCarCheckConfig(adCategoryId: String, type: String?, cityId: String?) {
        viewModelScope.launch {
            try {
                val config = configRepository.getCarCheckConfig(adCategoryId, type, cityId)
                _carCheckConfig.value = config
            } catch (_: Exception) {
            } finally {
            }
        }
    }

    private fun getAdTouchingPoints(
        category: Category,
        city: City?
    ) {
        viewModelScope.launch {
            val tps: List<AdTouchingPoint> = getAndFilterTouchingPointsUseCase(category, city)
            if (tps.isNotEmpty()) {
                _touchingPoints.update { tps }
            }
        }
    }

    private fun recordAdViewEvent(ad: AdDetails.Details?) {
        val properties = mutableSetOf<Param>()

        properties.add(
            Param(
                key = AnalyticsEvent.ParamKeys.SCREEN_NAME,
                value = AnalyticsEvent.ScreensNames.AD_VIEW
            )
        )

        ad?.let {
            it.adId?.let { id ->
                properties.add(Param(AnalyticsEvent.ParamKeys.AD_ID, id))
            }
            it.listId?.let { id ->
                properties.add(
                    Param(
                        AnalyticsEvent.ParamKeys.AD_LIST_ID,
                        id
                    )
                )
            }
            it.type?.let { type ->
                properties.add(Param(AnalyticsEvent.ParamKeys.AD_TYPE, type.key.toString()))
            }
            it.price?.let { price ->
                properties.add(
                    Param(
                        AnalyticsEvent.ParamKeys.AD_PRICE,
                        price.getCurrentPrice().toString()
                    )
                )
            }
            it.category?.parent?.trackingName?.let { name ->
                properties.add(Param(AnalyticsEvent.ParamKeys.CATEGORY_NAME, name))
            }
            it.category?.parent?.id?.let { id ->
                properties.add(
                    Param(
                        AnalyticsEvent.ParamKeys.CATEGORY_ID,
                        id
                    )
                )
            }
            it.category?.trackingName?.let { name ->
                properties.add(
                    Param(
                        AnalyticsEvent.ParamKeys.SUB_CATEGORY_NAME,
                        name
                    )
                )
            }
            it.category?.id?.let { id ->
                properties.add(
                    Param(
                        AnalyticsEvent.ParamKeys.SUB_CATEGORY_ID,
                        id
                    )
                )
            }
            it.cityArea?.let { city ->
                properties.add(Param(AnalyticsEvent.ParamKeys.CITY, city.trackingName.toString()))
                properties.add(Param(AnalyticsEvent.ParamKeys.CITY_ID, city.id.toString()))
            }

            it.cityArea?.area?.let { area ->
                properties.add(Param(AnalyticsEvent.ParamKeys.AREA, area.trackingName.toString()))
                properties.add(Param(AnalyticsEvent.ParamKeys.AREA_ID, area.id.toString()))
            }

            it.seller?.getSellerTrackingType()?.let { type ->
                properties.add(Param(AnalyticsEvent.ParamKeys.SELLER_TYPE, type))
            }
            it.seller?.getNullableName()?.let { name ->
                properties.add(Param(AnalyticsEvent.ParamKeys.SELLER_NAME, name))
            }
            it.seller?.getSellerAccountId().let { id ->
                properties.add(
                    Param(
                        AnalyticsEvent.ParamKeys.SELLER_ID,
                        id.toString()
                    )
                )
            }
            it.media?.mediaCount?.let { count ->
                properties.add(
                    Param(
                        AnalyticsEvent.ParamKeys.PICTURE_COUNT,
                        count.toString()
                    )
                )
            }
            it.seller?.hasPhone()?.let { hasPhone ->
                properties.add(
                    Param(
                        AnalyticsEvent.ParamKeys.HAS_PHONE,
                        hasPhone.toString()
                    )
                )
            }
            it.seller?.phoneVerified()?.let { isVerified ->
                properties.add(
                    Param(
                        AnalyticsEvent.ParamKeys.IS_PHONE_VERIFIED,
                        isVerified.toString()
                    )
                )
            }
            it.listTimeString?.let { date ->
                properties.add(
                    Param(
                        AnalyticsEvent.ParamKeys.PUBLISH_DATE,
                        date
                    )
                )
            }
        }

        analyticsHelper.logEvent(
            AnalyticsEvent(
                name = AnalyticsEvent.Types.SCREEN_VIEW,
                properties = properties
            )
        )
    }

    private fun logAnalyticsEventForAdview(
        ad: AdDetails.Details?,
        elementValue: String,
        elementKey: String = AnalyticsEvent.ParamKeys.LEAD_TYPE,
        eventName: String = AnalyticsEvent.ParamKeys.LEAD
    ) {
        val properties = mutableSetOf<Param>()

        properties.add(
            Param(
                key = AnalyticsEvent.ParamKeys.LANG,
                value = LocaleManager.getCurrentLanguage()
            )
        )

        properties.add(
            Param(
                key = AnalyticsEvent.ParamKeys.SCREEN_NAME,
                value = AnalyticsEvent.ScreensNames.AD_VIEW
            )
        )

        properties.add(
            Param(
                key = AnalyticsEvent.ParamKeys.ELEMENT_SOURCE,
                value = AnalyticsEvent.ScreensNames.AD_VIEW
            )
        )

        properties.add(
            Param(
                key = elementKey,
                value = elementValue
            )
        )

        ad?.let {
            it.adId?.let { id ->
                properties.add(Param(AnalyticsEvent.ParamKeys.AD_ID, id))
            }
            it.listId?.let { id ->
                properties.add(
                    Param(
                        AnalyticsEvent.ParamKeys.AD_LIST_ID,
                        id
                    )
                )
            }
            it.type?.let { type ->
                properties.add(Param(AnalyticsEvent.ParamKeys.AD_TYPE, type.key.toString()))
            }
            it.price?.let { price ->
                properties.add(
                    Param(
                        AnalyticsEvent.ParamKeys.AD_PRICE,
                        price.getCurrentPrice().toString()
                    )
                )
            }
            it.category?.parent?.trackingName?.let { name ->
                properties.add(Param(AnalyticsEvent.ParamKeys.CATEGORY_NAME, name))
            }
            it.category?.parent?.id?.let { id ->
                properties.add(
                    Param(
                        AnalyticsEvent.ParamKeys.CATEGORY_ID,
                        id
                    )
                )
            }
            it.category?.trackingName?.let { name ->
                properties.add(
                    Param(
                        AnalyticsEvent.ParamKeys.SUB_CATEGORY_NAME,
                        name
                    )
                )
            }
            it.category?.id?.let { id ->
                properties.add(
                    Param(
                        AnalyticsEvent.ParamKeys.SUB_CATEGORY_ID,
                        id
                    )
                )
            }
            it.cityArea?.let { city ->
                properties.add(Param(AnalyticsEvent.ParamKeys.CITY, city.trackingName))
                properties.add(Param(AnalyticsEvent.ParamKeys.CITY_ID, city.id))
            }

            it.cityArea?.area?.let { area ->
                properties.add(Param(AnalyticsEvent.ParamKeys.AREA, area.trackingName))
                properties.add(Param(AnalyticsEvent.ParamKeys.AREA_ID, area.id))
            }

            it.seller?.getSellerTrackingType()?.let { type ->
                properties.add(Param(AnalyticsEvent.ParamKeys.SELLER_TYPE, type))
            }
            it.seller?.getSellerAccountId().let { id ->
                properties.add(
                    Param(
                        AnalyticsEvent.ParamKeys.SELLER_ID,
                        id.toString()
                    )
                )
            }
        }

        analyticsHelper.logEvent(
            AnalyticsEvent(
                name = eventName,
                properties = properties
            )
        )
    }

    private fun recordTouchingPointClick(touchingPoint: AdTouchingPoint) {
        viewModelScope.launch {
            adViewRepository.recordTouchingPoint(
                campaignId = touchingPoint.id,
                clickOrImpression = "click_android"
            )
        }
    }

    fun reportAd(
        adListId: String,
        email: String,
        reason: String,
        message: String
    ) {
        submissionResult = null

        viewModelScope.launch {
            _oneTimeEvents.emit(AdViewOneTimeEvents.ShowHideProgressAdReport(true))

            val result = adViewRepository.reportAd(
                adListId = adListId,
                email = email,
                reason = reason,
                message = message
            )

            when (result) {
                is Resource.Success -> {
                    _oneTimeEvents.emit(AdViewOneTimeEvents.ShowHideProgressAdReport(false))
                    renderSuccess(UiText.FromRes(R.string.ad_reported_successfully))
                }

                is Resource.Failure -> {
                    _oneTimeEvents.emit(AdViewOneTimeEvents.ShowHideProgressAdReport(false))
                    renderFailure(result.error.asUiText())
                }
            }
        }
    }


    fun sendLead(
        resellerId: String,
        campaignId: String,
        name: String,
        phoneNumber: String,
        option1: String,
        option2: Boolean
    ) {
        isSubmitting = true
        submissionResult = null

        viewModelScope.launch {
            _oneTimeEvents.emit(AdViewOneTimeEvents.ShowHideProgress(true))


            val result = adViewRepository.sendCarInspectionLead(
                resellerId = resellerId,
                campaignId = campaignId,
                name = name,
                phone = phoneNumber,
                option1 = option1,
                option2 = option2
            )

            isSubmitting = false
            when (result) {
                is Resource.Success -> {
                    _oneTimeEvents.emit(AdViewOneTimeEvents.ShowHideProgress(false))
                    _oneTimeEvents.emit(AdViewOneTimeEvents.CarCheckSuccess)
                    onEvent(AdViewUiEvents.OnCarCheckSubmit)
                }

                is Resource.Failure -> {
                    _oneTimeEvents.emit(AdViewOneTimeEvents.ShowHideProgress(false))
                    renderFailure(result.error.asUiText())
                }
            }
        }
    }

    private fun handleShopClick(ad: AdDetails.Details) {
        viewModelScope.launch {
            val account = accountRepository.currentAccount.first()
            if (account !is Account.Connected) {
                _oneTimeEvents.emit(
                    AdViewOneTimeEvents.OpenLogin(
                        AuthenticationActivity.REQUEST_SIGN_IN_MESSAGING
                    )
                )
                return@launch
            } else {
                ad.seller?.getSellerAccountId()
                    ?.let { AdViewOneTimeEvents.OpenEcommerce(ad, it) }?.let {
                        _oneTimeEvents.emit(
                            it
                        )
                    }
            }

        }
    }

    private suspend fun isConnectedShop(): Boolean {
        val account = accountRepository.currentAccount.first()
        return if (account is Account.Connected) {
            account.isShop()
        } else {
            false
        }
    }

    private suspend fun isAccountAd(id: String?): Boolean {
        val account = accountRepository.currentAccount.first()
        return if (account is Account.Connected) {
            return account.connectedContact().accountId == id
        } else {
            false
        }
    }

    fun onEvent(event: AdViewUiEvents) {

        when (event) {
            is AdViewUiEvents.OnTouchingPointClicked -> recordTouchingPointClick(event.touchingPoint)
            AdViewUiEvents.OnRefreshClicked -> {
                // Reset to loading state and reload
                _viewState.value = AdViewViewState.Loading
                loadAdView(route.adId)
            }
            else -> {
                // This shouldn't happen, but as a security.
                val ad = (_viewState.value as? AdViewViewState.Success)?.details?.ad ?: run {
                    return
                }
                when (event) {
                    AdViewUiEvents.OnFavoriteBtnClicked -> {
                        updateAdFavoriteStatus(ad)
                        if (ad.isInMyFavorites == true)
                            logAnalyticsEventForAdview(ad, AnalyticsEvent.ParamValues.UNSAVE_AD)
                        else
                            logAnalyticsEventForAdview(ad, AnalyticsEvent.ParamValues.SAVE_AD)
                    }

                    is AdViewUiEvents.OnShareBtnClicked -> {
                        logAnalyticsEventForAdview(ad, AnalyticsEvent.ParamValues.SHARE_AD)
                    }

                    is AdViewUiEvents.OnCallBtnClicked -> {
                        logAnalyticsEventForAdview(ad, AnalyticsEvent.ParamValues.CALL)
                        analyticsHelper.trackTikTokPurchase(
                            contentId = ad.listId?: "",
                            contentType = ad.category?.id ?: ""
                        )
                    }

                    is AdViewUiEvents.OnBoostBtnClicked -> {
                        logAnalyticsEventForAdview(ad, AnalyticsEvent.ParamValues.BOOSTER)
                    }

                    is AdViewUiEvents.ShowPhone -> {
                        logAnalyticsEventForAdview(ad, AnalyticsEvent.ParamValues.SHOW_PHONE)
                    }

                    is AdViewUiEvents.OnWhatsappBtnClicked -> {
                        logAnalyticsEventForAdview(ad, AnalyticsEvent.ParamValues.WHATSAPP)
                        analyticsHelper.trackTikTokPurchase(
                            contentId = ad.listId?: "",
                            contentType = ad.category?.id ?: ""
                        )
                    }

                    AdViewUiEvents.OnMessagingBtnClicked -> {
                        handleMessagingClick(ad)
                        logAnalyticsEventForAdview(ad, AnalyticsEvent.ParamValues.CHAT)
                        analyticsHelper.trackTikTokPurchase(
                            contentId = ad.listId?: "",
                            contentType = ad.category?.id ?: ""
                        )
                    }

                    AdViewUiEvents.OnReportAd -> {
                        logAnalyticsEventForAdview(ad, AnalyticsEvent.ParamValues.REPORT_AD)
                    }

                    AdViewUiEvents.OnCarCheckSubmit -> {
                        logAnalyticsEventForAdview(
                            ad,
                            AnalyticsEvent.ParamValues.SUBMIT_INSPECTION_FORM,
                            ELEMENT_NAME,
                            ELEMENT_CLICKED
                        )
                    }

                    AdViewUiEvents.OnCarInspectionCtaClick -> {
                        logAnalyticsEventForAdview(
                            ad,
                            AnalyticsEvent.ParamValues.CAR_INSPECTION_CTA,
                            ELEMENT_NAME,
                            ELEMENT_CLICKED
                        )
                    }

                    AdViewUiEvents.OnDownloadInspectionReport -> {
                        logAnalyticsEventForAdview(
                            ad,
                            AnalyticsEvent.ParamValues.DOWNLOAD_INSPECTION_REPORT,
                            ELEMENT_NAME,
                            ELEMENT_CLICKED
                        )
                    }

                    AdViewUiEvents.OnPreviewInspectionReport -> {
                        logAnalyticsEventForAdview(
                            ad,
                            AnalyticsEvent.ParamValues.PREVIEW_INSPECTION_REPORT,
                            ELEMENT_NAME,
                            ELEMENT_CLICKED
                        )
                    }

                    AdViewUiEvents.OnCarInspectionReportDisplayed -> {
                        logAnalyticsEventForAdview(
                            ad,
                            AnalyticsEvent.ParamValues.CAR_INSPECTION_REPORT,
                            ELEMENT_NAME,
                            ELEMENT_DISPLAYED
                        )
                    }

                    AdViewUiEvents.OnCarInspectionCtaDisplayed -> {
                        logAnalyticsEventForAdview(
                            ad,
                            AnalyticsEvent.ParamValues.CAR_INSPECTION_CTA,
                            ELEMENT_NAME,
                            ELEMENT_DISPLAYED
                        )
                    }

                    AdViewUiEvents.OnShopBtnClicked -> {
                        handleShopClick(ad)
                        logAnalyticsEventForAdview(ad, AnalyticsEvent.ParamValues.BUY)
                    }

                    is AdViewUiEvents.UserConnected -> {
//                        handleUserConnected(event)
                    }

                    is AdViewUiEvents.OnScreenView -> {
                        analyticsHelper.trackTikTokViewContent(
                            contentId = ad.listId ?: "",
                            contentType = ad.category?.id ?: ""
                        )
                    }

                    else -> {}
                }
            }
        }
    }

    private fun handleMessagingClick(ad: AdDetails.Details) {
        viewModelScope.launch {
            val account = accountRepository.currentAccount.first()
            if (account !is Account.Connected) {
                _oneTimeEvents.emit(
                    AdViewOneTimeEvents.OpenLogin(
                        AuthenticationActivity.REQUEST_SIGN_IN_MESSAGING
                    )
                )
                return@launch
            }

            _oneTimeEvents.emit(AdViewOneTimeEvents.ShowHideProgress(true))

            val result = ad.adId?.let {
                adViewRepository.getConversationIdWithAd(it)
            }

            _oneTimeEvents.emit(AdViewOneTimeEvents.ShowHideProgress(false))

            when (result) {
                is Resource.Success -> {
                    val data = result.data

                    if (data.found) {
                        _oneTimeEvents.emit(AdViewOneTimeEvents.ShowConversationBtmSheet(data.conversationId!!))
                    } else {
                        _oneTimeEvents.emit(AdViewOneTimeEvents.ShowFirstConversationBtmSheet(ad))
                    }
                }

                is Resource.Failure -> {
                    val error = result.error
                    _oneTimeEvents.emit(AdViewOneTimeEvents.ShowFirstConversationBtmSheet(ad))
                }

                null -> {
                    _oneTimeEvents.emit(AdViewOneTimeEvents.ShowFirstConversationBtmSheet(ad))
                }
            }
        }
    }

    private fun updateAdFavoriteStatus(ad: AdDetails.Details) {
        viewModelScope.launch {
            val account = accountRepository.currentAccount.first()
            if (account !is Account.Connected) {
                _oneTimeEvents.emit(
                    AdViewOneTimeEvents.OpenLogin(
                        AuthenticationActivity.REQUEST_SIGN_IN_MESSAGING
                    )
                )
                return@launch
            }
            val isFavorites = ad.isInMyFavorites == true
            val result = if (isFavorites) {
                accountRepository.unBookmarkAd(ad.listId)
            } else {
                accountRepository.bookmarkAd(ad.listId)
            }
            when (result) {
                is Resource.Success -> {
                    if (isFavorites) {
                        renderSuccess(UiText.FromRes(R.string.bookmarked_ads_screen_delete_ad_success))
                    } else {
                        renderSuccess(UiText.FromRes(R.string.bookmarked_ads_screen_bookmark_ad_success))
                    }
                    bookmarkAdsEventManager.sendEvent(
                        event = BookmarkAdEvent(listId = ad.listId, isFavorite = isFavorites.not())
                    )
                    true
                }

                is Resource.Failure -> {
                    renderFailure(result.error.asUiText())
                    false
                }
            }.also {
                if (_viewState.value is AdViewViewState.Success) {
                    val currentDetails = (_viewState.value as AdViewViewState.Success).details

                    // Update state by toggling the 'isInMyFavorites' property.
                    _viewState.update {
                        AdViewViewState.Success(
                            details = currentDetails.copy(
                                ad = currentDetails.ad.copy(
                                    isInMyFavorites = currentDetails.ad.isInMyFavorites?.not()
                                )
                            )
                        )
                    }
                }
                _oneTimeEvents.emit(
                    AdViewOneTimeEvents.UpdateFavoriteStatus(
                        adId = ad.adId!!,
                        isFavorite = ad.isInMyFavorites?.not() == true,
                        updated = it
                    )
                )
            }
        }
    }

    fun isVacationLocation(categoryId: String?): Boolean {
        return categoryId == LOCATION_VACANCE_ID.toString()
    }

    fun isEnchere(categoryId: String?): Boolean {
        return categoryId == ENCHERE_ID_1.toString() ||
                categoryId == ENCHERE_ID_2.toString() ||
                categoryId == ENCHERE_ID_3.toString() ||
                categoryId == ENCHERE_ID_4.toString()
    }

    companion object {
        const val ENCHERE_ID_1 = 7930
        const val ENCHERE_ID_2 = 7920
        const val ENCHERE_ID_3 = 7910
        const val ENCHERE_ID_4 = 7900
        const val LOCATION_VACANCE_ID = 1030
    }
}
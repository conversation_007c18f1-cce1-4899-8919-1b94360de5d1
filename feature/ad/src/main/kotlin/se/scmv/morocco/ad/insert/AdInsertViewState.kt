package se.scmv.morocco.ad.insert

import androidx.compose.runtime.Stable
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList
import se.scmv.morocco.ad.R
import se.scmv.morocco.ad.navigation.VasRoute
import se.scmv.morocco.designsystem.components.AvStepperHeaderStep
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.orion.components.OrionUiComponent

@Stable
data class AdInsertViewState(
    val steps: ImmutableList<Step> = persistentListOf(),
    val isLoading: Boolean = false,
    val error: UiText? = null
)

@Stable
data class Step(
    val tipsTitle: String,
    val tipsContent: ImmutableList<String>,
    val infoUrl: String,
    val components: ImmutableList<OrionUiComponent>
)

val headerSteps = listOf(
    AvStepperHeaderStep(icon = R.drawable.ic_ad_insert_step1),
    AvStepperHeaderStep(icon = R.drawable.ic_ad_insert_step2),
    AvStepperHeaderStep(icon = R.drawable.ic_ad_insert_step3),
    AvStepperHeaderStep(icon = R.drawable.ic_ad_insert_step4)
).toImmutableList()

sealed interface AdInsertOneTimeEvents {
    @JvmInline
    value class ScrollToStep(val index: Int) : AdInsertOneTimeEvents

    data class ScrollCurrentStepToIndex(
        val stepIndex: Int,
        val itemIndex: Int,
    ) : AdInsertOneTimeEvents

    data object ShowCloseConfirmationDialog: AdInsertOneTimeEvents

    data class NavigateToInsertResultScreen(val from: VasRoute.From): AdInsertOneTimeEvents

    data class NavigateToVas(val route: VasRoute) : AdInsertOneTimeEvents
}


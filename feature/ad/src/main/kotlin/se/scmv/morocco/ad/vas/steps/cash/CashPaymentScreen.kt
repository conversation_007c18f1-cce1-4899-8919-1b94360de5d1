package se.scmv.morocco.ad.vas.steps.cash

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Card
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.layer.drawLayer
import androidx.compose.ui.graphics.rememberGraphicsLayer
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Visibility
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.filterIsInstance
import se.scmv.morocco.ad.R
import se.scmv.morocco.ad.vas.master.CashPaymentViewState
import se.scmv.morocco.ad.vas.master.VasMasterOneTimeEvents
import se.scmv.morocco.common.lang.LocaleManager
import se.scmv.morocco.designsystem.components.AvAlert
import se.scmv.morocco.designsystem.components.AvAlertType
import se.scmv.morocco.designsystem.components.AvSecondaryButton
import se.scmv.morocco.designsystem.theme.Blue300
import se.scmv.morocco.designsystem.theme.White
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.domain.models.VasPackage
import se.scmv.morocco.orion.components.vas.displayedName

@Composable
fun CashPaymentScreen(
    modifier: Modifier = Modifier,
    state: CashPaymentViewState,
    chosenVasPackage: VasPackage?,
    paymentCode: String?,
    oneTimeEvents: SharedFlow<VasMasterOneTimeEvents>,
    onCashStep1BtnClicked: () -> Unit,
    onCashReceiptRecorded: (ImageBitmap) -> Unit
) {
    val price = chosenVasPackage?.price
    val priceUnit = chosenVasPackage?.priceUnit?.let {
        stringResource(it.displayedName())
    }
    // Should not be null because we can't reach this step if we don't had a success in the previous step .
    val summaryPrice = if (price != null && priceUnit != null) {
        "$price $priceUnit"
    } else ""

    val graphicsLayer = rememberGraphicsLayer()
    // Should not be null because we can't reach this step if we don't had a success in the previous step .
    CashPaymentContent(
        modifier = modifier
            .verticalScroll(rememberScrollState())
            .drawWithContent {
                // call record to capture the content in the graphics layer
                graphicsLayer.record {
                    // draw the contents of the composable into the graphics layer
                    <EMAIL>()
                }
                // draw the graphics layer on the visible canvas
                drawLayer(graphicsLayer)
            }
            .background(MaterialTheme.colorScheme.background, MaterialTheme.shapes.small)
            .padding(MaterialTheme.dimens.big),
        cashPaymentCode = paymentCode.orEmpty(),
        summaryPrice = summaryPrice,
        state = state,
        onStep1BtnClicked = onCashStep1BtnClicked
    )

    LaunchedEffect(Unit) {
        oneTimeEvents
            .filterIsInstance<VasMasterOneTimeEvents.RecordCashReceipt>()
            .collectLatest {
                val bitmap = graphicsLayer.toImageBitmap()
               onCashReceiptRecorded(bitmap)
            }
    }
}

@Composable
private fun CashPaymentContent(
    modifier: Modifier = Modifier,
    cashPaymentCode: String,
    summaryPrice: String,
    state: CashPaymentViewState,
    onStep1BtnClicked: () -> Unit
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.big)
    ) {
        Text(
            text = stringResource(R.string.cash_checkout_screen_title),
            style = MaterialTheme.typography.bodyLarge,
            fontWeight = FontWeight.Black
        )

        CashPaymentSummary(price = summaryPrice)

        Text(
            modifier = Modifier.padding(top = MaterialTheme.dimens.big),
            text = stringResource(state.cashProviderTitle),
            style = MaterialTheme.typography.bodyLarge,
            fontWeight = FontWeight.Medium
        )

        ConstraintLayout(modifier = Modifier.fillMaxWidth()) {
            val (
                step1Icon, step1Badge, step1Description, step1Action, step1Arrow,
                paymentCode,
                step2Badge, step2Description, step2Arrow, drawingHelper, step2Icon,
                step3Badge, step3Description, bankingAppInfo, bankingAppWarning
            ) = createRefs()

            Image(
                modifier = Modifier
                    .size(77.dp)
                    .constrainAs(step1Icon) {
                        top.linkTo(parent.top, margin = 20.dp)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    },
                painter = painterResource(state.step1Icon),
                contentDescription = null
            )
            Text(
                modifier = Modifier
                    .size(27.dp)
                    .background(MaterialTheme.colorScheme.primary, CircleShape)
                    .constrainAs(step1Badge) {
                        top.linkTo(step1Icon.bottom, margin = 10.dp)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    },
                text = "1",
                color = White,
                textAlign = TextAlign.Center,
            )
            Text(
                modifier = Modifier
                    .constrainAs(step1Description) {
                        top.linkTo(step1Badge.bottom)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    },
                text = stringResource(state.step1Description),
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Light
            )
            AvSecondaryButton(
                modifier = Modifier.constrainAs(step1Action) {
                    top.linkTo(step1Description.bottom, margin = 10.dp)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                },
                text = stringResource(state.step1Action),
                onClick = onStep1BtnClicked
            )
            Image(
                modifier = Modifier
                    .constrainAs(step1Arrow) {
                        top.linkTo(step1Action.top)
                        start.linkTo(step1Description.end, margin = -(10.dp))
                        bottom.linkTo(paymentCode.top)
                    }
                    .graphicsLayer {
                        rotationX = if (LocaleManager.isFr()) 15f else -15f
                    },
                painter = painterResource(R.drawable.ic_arc_right_down),
                contentDescription = null,
                colorFilter = ColorFilter.tint(color = MaterialTheme.colorScheme.onBackground)
            )
            Text(
                modifier = Modifier
                    .background(
                        color = Blue300,
                        shape = RoundedCornerShape(
                            topStart = MaterialTheme.dimens.bigger,
                            topEnd = MaterialTheme.dimens.bigger,
                            bottomEnd = MaterialTheme.dimens.bigger,
                        )
                    )
                    .padding(
                        horizontal = MaterialTheme.dimens.default,
                        vertical = MaterialTheme.dimens.bigger
                    )
                    .constrainAs(paymentCode) {
                        top.linkTo(step1Action.bottom, margin = 70.dp)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    },
                text = cashPaymentCode,
                color = MaterialTheme.colorScheme.primary,
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Light
            )
            Text(
                modifier = Modifier
                    .size(27.dp)
                    .background(MaterialTheme.colorScheme.primary, CircleShape)
                    .constrainAs(step2Badge) {
                        top.linkTo(paymentCode.bottom, margin = 10.dp)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    },
                text = "2",
                color = White,
                textAlign = TextAlign.Center
            )
            Text(
                modifier = Modifier
                    .constrainAs(step2Description) {
                        top.linkTo(step2Badge.bottom)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    },
                text = stringResource(R.string.cash_checkout_screen_step_2),
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Light
            )
            Image(
                modifier = Modifier
                    .constrainAs(step2Arrow) {
                        top.linkTo(step2Description.bottom)
                        start.linkTo(step2Description.start)
                        bottom.linkTo(drawingHelper.top)
                    }
                    .graphicsLayer {
                        rotationX = if (LocaleManager.isFr()) 30f else -30f
                        rotationY = 180F
                    },
                painter = painterResource(R.drawable.ic_arc_right_down),
                contentDescription = null,
                colorFilter = ColorFilter.tint(color = MaterialTheme.colorScheme.onBackground)
            )
            Spacer(
                modifier = Modifier
                    .size(1.dp)
                    .constrainAs(drawingHelper) {
                        top.linkTo(step2Icon.top)
                        end.linkTo(step2Icon.start)
                        bottom.linkTo(step2Icon.bottom)
                    }
            )
            Image(
                modifier = Modifier
                    .size(77.dp)
                    .constrainAs(step2Icon) {
                        top.linkTo(step2Description.bottom, margin = 70.dp)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    },
                painter = painterResource(R.drawable.img_cash_payment_step3_icon),
                contentDescription = null
            )
            Text(
                modifier = Modifier
                    .size(27.dp)
                    .background(MaterialTheme.colorScheme.primary, CircleShape)
                    .constrainAs(step3Badge) {
                        top.linkTo(step2Icon.bottom, margin = 10.dp)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    },
                text = "3",
                color = White,
                textAlign = TextAlign.Center
            )
            Text(
                modifier = Modifier.constrainAs(step3Description) {
                    top.linkTo(step3Badge.bottom)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                },
                text = stringResource(R.string.cash_checkout_screen_step_3),
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Light
            )
            AvAlert(
                modifier = Modifier.constrainAs(bankingAppInfo) {
                    top.linkTo(step3Description.bottom, margin = 16.dp)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    visibility = if (state.isBankingAppVisible) {
                        Visibility.Visible
                    } else Visibility.Gone
                },
                text = stringResource(R.string.cash_checkout_screen_notif),
                type = AvAlertType.Info
            )
            AvAlert(
                modifier = Modifier.constrainAs(bankingAppWarning) {
                    top.linkTo(bankingAppInfo.bottom, margin = 12.dp, goneMargin = 16.dp)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                },
                text = stringResource(R.string.cash_checkout_screen_warning),
                type = AvAlertType.Warning
            )
        }
    }
}

@Composable
private fun CashPaymentSummary(
    modifier: Modifier = Modifier,
    price: String
) {
    Card {
        Column(
            modifier = modifier
                .fillMaxWidth()
                .padding(horizontal = MaterialTheme.dimens.default)
                .padding(vertical = MaterialTheme.dimens.large),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.large)
        ) {
            listOf(
                stringResource(R.string.cash_checkout_screen_merchant_label)
                        to stringResource(R.string.cash_checkout_screen_merchant_value),
                stringResource(R.string.cash_checkout_screen_description_label)
                        to stringResource(R.string.cash_checkout_screen_description_value),
                stringResource(R.string.cash_checkout_screen_price_label)
                        to price,
            ).forEach {
                Row(
                    horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(text = it.first, style = MaterialTheme.typography.bodyMedium)
                    Text(
                        text = it.second,
                        style = MaterialTheme.typography.bodyLarge,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
    }
}
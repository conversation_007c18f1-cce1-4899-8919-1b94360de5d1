package se.scmv.morocco.ad.ad_view.components

import android.content.res.Configuration
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Divider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import se.scmv.morocco.ad.R
import se.scmv.morocco.designsystem.theme.dimens


@Composable
fun AvitoDisclaimer(click: () -> Unit) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = MaterialTheme.dimens.default)
            .padding(vertical = MaterialTheme.dimens.medium),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {

        Divider(color = Color(0xFFE6E6E6), thickness = 0.2.dp)

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = MaterialTheme.dimens.none) // Add horizontal padding to the text
                .padding(bottom = MaterialTheme.dimens.medium, top = MaterialTheme.dimens.large),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                painter = painterResource(R.drawable.ic_safety), // Using a system alert icon as a placeholder for the shield
                contentDescription = "Warning",
                tint = Color(0xFFB32E3F),
                modifier = Modifier.size(MaterialTheme.dimens.bigger)
            )
            Spacer(modifier = Modifier.width(MaterialTheme.dimens.medium))
            Text(
                text = stringResource(R.string.never_send_money),
                textAlign = TextAlign.Start,
                fontSize = 12.sp,
                color = Color(0xFF58606B)
            )
        }

        // Report Abuse Button
        Row(
            modifier = Modifier
                .clip(RoundedCornerShape(MaterialTheme.dimens.big))
                .border(0.5.dp, Color(0xFFB32E3F), RoundedCornerShape(MaterialTheme.dimens.big))
                .padding(
                    horizontal = MaterialTheme.dimens.default,
                    vertical = MaterialTheme.dimens.medium
                )
                .clickable {
                    click()
                },
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                painter = painterResource(R.drawable.warning_ic),
                contentDescription = stringResource(R.string.report_abuse),
                tint = Color(0xFFB32E3F),
                modifier = Modifier.size(MaterialTheme.dimens.default)
            )
            Spacer(modifier = Modifier.width(MaterialTheme.dimens.medium))
            Text(
                text = stringResource(R.string.report_abuse),
                color = Color(0xFFB32E3F),
                fontSize = 14.sp
            )
        }
    }
}

@Preview(
    showBackground = true,
    uiMode = Configuration.UI_MODE_NIGHT_YES or Configuration.UI_MODE_TYPE_NORMAL,
    showSystemUi = true
)
@Composable
fun AvitoDisclaimerPreview() {
    AvitoDisclaimer {

    }
}

package se.scmv.morocco.ad.ad_view.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.BottomAppBar
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import se.scmv.morocco.ad.R
import se.scmv.morocco.designsystem.theme.dimens

@Preview(showBackground = true)
@Composable
fun BottomAppBarPreview() {
    MaterialTheme {
        BottomAppBar(
            containerColor = MaterialTheme.colorScheme.surface,
            modifier = Modifier
                .height(56.dp) // Reduced height
                .shadow(
                    elevation = MaterialTheme.dimens.betweenSmallMedium,
                    ambientColor = Color.Black,
                    spotColor = Color.Black
                )
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = MaterialTheme.dimens.default),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Row(
                    modifier = Modifier.weight(1f),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column(
                        verticalArrangement = Arrangement.Center
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier
                                .height(IntrinsicSize.Min)
                                .background(
                                    color = Color(0xFFfc942d),
                                    shape = RoundedCornerShape(MaterialTheme.dimens.small)
                                )
                                .padding(
                                    horizontal = MaterialTheme.dimens.small,
                                    vertical = MaterialTheme.dimens.none
                                ) // Smaller padding
                        ) {
                            Icon(
                                painter = painterResource(R.drawable.ic_shop),
                                contentDescription = "Verified Shop",
                                tint = Color.White,
                                modifier = Modifier
                                    .size(MaterialTheme.dimens.default)
                                    .padding(1.dp) // Smaller icon
                            )
                            Spacer(modifier = Modifier.width(MaterialTheme.dimens.tiny))
                            Text(
                                text = "Boutique",
                                style = TextStyle(
                                    fontSize = 10.sp,
                                    color = Color.White,
                                    lineHeight = 14.sp
                                ),
                                fontSize = 10.sp,
                                color = Color.White,
                                modifier = Modifier
                                    .padding(MaterialTheme.dimens.tiny)
                                    .height(IntrinsicSize.Min) // Ensures the row is only as tall as needed

                            )
                        }

                        Row(verticalAlignment = Alignment.CenterVertically) {
                            Text(
                                text = "Test test",
                                maxLines = 1,
                                overflow = TextOverflow.Ellipsis,
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Medium
                            )
                            Spacer(modifier = Modifier.width(MaterialTheme.dimens.tiny))
                            Icon(
                                painter = painterResource(R.drawable.verified_check_icon),
                                contentDescription = "Verified Seller",
                                tint = Color(0xFFFFA500),
                                modifier = Modifier.size(MaterialTheme.dimens.default)
                            )
                        }
                    }
                }

                // Action Buttons (WhatsApp, Chat, Call)
                Row(horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium)) {
                    CircularIconButton(
                        icon = painterResource(id = R.drawable.ic_whatsapp),
                        iconColor = Color(0xFF64c571),
                        backgroundColor = Color(0xFFedf9f0),
                        onClick = { /* Handle WhatsApp */ }
                    )

                    CircularIconButton(
                        icon = painterResource(id = R.drawable.ic_message),
                        iconColor = Color(0xFF2563EB),
                        backgroundColor = Color(0xFFEAF0FF),
                        onClick = { /* Handle Chat */ }
                    )

                    CircularIconButton(
                        icon = painterResource(id = R.drawable.ic_call_grid),
                        iconColor = Color.White,
                        backgroundColor = Color(0xFF2563EB),
                        onClick = { /* Handle Call */ }
                    )
                }
            }
        }
    }
}
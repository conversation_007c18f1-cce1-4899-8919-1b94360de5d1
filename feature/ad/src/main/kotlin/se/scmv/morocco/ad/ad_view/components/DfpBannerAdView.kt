package se.scmv.morocco.ad.ad_view.components

import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.viewinterop.AndroidView
import com.google.ads.mediation.admob.AdMobAdapter
import com.google.android.gms.ads.AdListener
import com.google.android.gms.ads.AdSize
import com.google.android.gms.ads.LoadAdError
import com.google.android.gms.ads.admanager.AdManagerAdRequest
import com.google.android.gms.ads.admanager.AdManagerAdView
import se.scmv.morocco.common.extensions.toBundle

@Composable
fun DfpBannerAdView(
    adUnitId: String,
    adTitle: String,
    categoryId: String,
    modifier: Modifier = Modifier
) {
    AndroidView(
        modifier = modifier,
        factory = { context ->
            AdManagerAdView(context).apply {
                setAdUnitId(adUnitId)
                setAdSizes(AdSize.LARGE_BANNER)

                val dfpValues = HashMap<String, String>()
                dfpValues["categoryId"] = categoryId

                val adRequestBuilder = AdManagerAdRequest.Builder()
                    .setContentUrl("site:www.avito.ma $adTitle")

                if (categoryId.toInt() > 0) {
                    adRequestBuilder.addNetworkExtrasBundle(
                        AdMobAdapter::class.java,
                        dfpValues.toBundle()
                    )
                }

                val adRequest: AdManagerAdRequest = adRequestBuilder.build()

                adListener = object : AdListener() {
                    override fun onAdLoaded() {
                        <EMAIL> = View.VISIBLE
                    }

                    override fun onAdFailedToLoad(loadAdError: LoadAdError) {
                        <EMAIL> = View.GONE
                    }

                    override fun onAdClosed() {
                        <EMAIL> = View.GONE
                    }
                }

                loadAd(adRequest)
            }
        },
        update = {}
    )
}

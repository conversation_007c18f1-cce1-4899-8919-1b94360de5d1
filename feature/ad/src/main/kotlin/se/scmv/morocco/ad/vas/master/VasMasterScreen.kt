package se.scmv.morocco.ad.vas.master

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import androidx.activity.compose.ManagedActivityResultLauncher
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Scaffold
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.core.content.ContextCompat
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import se.scmv.morocco.ad.R
import se.scmv.morocco.ad.vas.master.VasMasterOneTimeEvents.CloseVas
import se.scmv.morocco.ad.vas.master.VasMasterOneTimeEvents.GoToPage
import se.scmv.morocco.ad.vas.master.VasMasterOneTimeEvents.OpenAccountAdsScreen
import se.scmv.morocco.ad.vas.master.VasMasterOneTimeEvents.OpenSupportedBankAppsBtmSheet
import se.scmv.morocco.ad.vas.master.VasMasterOneTimeEvents.OpenUrl
import se.scmv.morocco.ad.vas.master.VasMasterOneTimeEvents.PrintCashReceipt
import se.scmv.morocco.ad.vas.steps.avitokens.ShopSuccessPaymentScreen
import se.scmv.morocco.ad.vas.steps.bankapps.SupportedBankAppsBtmSheet
import se.scmv.morocco.ad.vas.steps.cash.CashPaymentScreen
import se.scmv.morocco.ad.vas.steps.paymentmethod.ChoosePaymentMethodScreen
import se.scmv.morocco.ad.vas.steps.vaspackage.ChooseVasPackageScreen
import se.scmv.morocco.designsystem.components.AvStepper
import se.scmv.morocco.designsystem.components.AvStepperButtonState
import se.scmv.morocco.designsystem.components.AvTopAppBar
import se.scmv.morocco.designsystem.components.IndeterminateLoading
import se.scmv.morocco.designsystem.components.ScreenErrorState
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.domain.models.Account
import se.scmv.morocco.ui.SnackBarController
import se.scmv.morocco.ui.SnackBarEvent
import se.scmv.morocco.ui.SnackBarType

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun VasMasterRoute(
    account: Account.Connected,
    closeVas: () -> Unit,
    openUrl: (String, Boolean) -> Unit,
    navigateToAccountAds: () -> Unit,
    navigateToAdInsertImageStep: () -> Unit,
    modifier: Modifier = Modifier,
    viewModel: VasMasterViewModel = hiltViewModel<VasMasterViewModel>().apply {
        setAccount(account)
    }
) {
    val sharedState = viewModel.viewState.collectAsStateWithLifecycle()
    val pagerState = rememberPagerState { headerSteps.size }

    when (val state = sharedState.value) {
        VasMasterViewState.Loading -> IndeterminateLoading()
        is VasMasterViewState.Success -> {
            val footerBtnState = remember {
                derivedStateOf {
                    val derivedState = sharedState.value as VasMasterViewState.Success
                    AvStepperButtonState(
                        text = derivedState.footerBtnText(account.isShop()),
                        loading = derivedState.isLoading,
                        enable = derivedState.canSubmit()
                    )
                }
            }

            AvStepper(
                modifier = modifier,
                pagerState = pagerState,
                steps = headerSteps,
                footerLeftContent = null,
                onBackClicked = viewModel::onBackClicked,
                footerBtnState = footerBtnState,
                onCloseClicked = closeVas,
                onNextClicked = viewModel::onNextClicked
            ) { pageIndex ->
                when (pageIndex) {
                    PAGE_INDEX_CHOOSE_VAS_PACKAGE -> ChooseVasPackageScreen(
                        modifier = Modifier.fillMaxSize(),
                        state = state.step1State,
                        chosenVasPackageId = state.chosenVasPackage?.id,
                        chosenExecutionSlotsDay = state.chosenExecutionSlotsDay,
                        chosenExecutionSlotsTimeId = state.chosenExecutionSlotsTimeId,
                        onPackageSelected = viewModel::onVasPackageChanged,
                        onExeSlotDaySelected = viewModel::onExeSlotDaySelected,
                        onExeSlotTimeSelected = viewModel::onExeSlotTimeSelected,
                        onAddImageClicked = navigateToAdInsertImageStep
                    )

                    PAGE_INDEX_CHOOSE_PAYMENT_METHOD -> ChoosePaymentMethodScreen(
                        modifier = Modifier.fillMaxSize(),
                        state = state.step2State,
                        chosenVasPack = state.chosenVasPack,
                        chosenVasPackage = state.chosenVasPackage,
                        chosenPaymentMethod = state.chosenPaymentMethod,
                        onChangePackBtnClicked = viewModel::onBackClicked,
                        onPaymentMethodChanged = viewModel::onPaymentMethodChanged
                    )

                    PAGE_INDEX_CHOOSE_SUMMARY -> when (account) {
                        is Account.Connected.Shop -> ShopSuccessPaymentScreen(
                            modifier = Modifier.fillMaxSize(),
                        )

                        is Account.Connected.Private -> state.step3State?.let {
                            CashPaymentScreen(
                                modifier = Modifier.fillMaxSize(),
                                state = it,
                                chosenVasPackage = state.chosenVasPackage,
                                paymentCode = state.paymentCodeOrLink.orEmpty(),
                                oneTimeEvents = viewModel.oneTimeEvents,
                                onCashStep1BtnClicked = viewModel::onCashStep1BtnClicked,
                                onCashReceiptRecorded = viewModel::onCashReceiptRecorded
                            )
                        }
                    }
                }
            }
        }

        is VasMasterViewState.Error -> Scaffold(
            topBar = { AvTopAppBar(onNavigationIconClicked = closeVas) }
        ) {
            ScreenErrorState(
                modifier = modifier
                    .fillMaxSize()
                    .padding(it),
                title = stringResource(R.string.common_oups),
                description = state.message.getValue(LocalContext.current),
                actionText = stringResource(R.string.common_refresh),
                onActionClicked = {
                    viewModel.onRefresh()
                }
            )
        }
    }

    val scope = rememberCoroutineScope()

    val context = LocalContext.current
    val externalStoragePermissionLauncher = rememberWriteToExternalStoragePermissionLauncher(
        scope = scope,
        onGranted = {}
    )

    var showSupportedBankAppsBtmSheet by remember { mutableStateOf(false) }
    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)
    if (showSupportedBankAppsBtmSheet) {
        SupportedBankAppsBtmSheet(
            sheetState = sheetState,
            onDismiss = {
                scope.launch {
                    sheetState.hide()
                    showSupportedBankAppsBtmSheet = false
                }
            }
        )
    }
    LaunchedEffect(Unit) {
        viewModel.oneTimeEvents.collectLatest { event ->
            when (event) {
                is GoToPage -> pagerState.animateScrollToPage(event.index)
                is PrintCashReceipt -> {
                    checkExternalStoragePermission(
                        context = context,
                        externalStoragePermissionLauncher = externalStoragePermissionLauncher
                    ) {
                        viewModel.saveImageToDeviceStorage(
                            context = context,
                            imageBitmap = event.bitmap
                        )
                    }
                }

                OpenAccountAdsScreen -> navigateToAccountAds()
                is OpenUrl -> openUrl(event.url, event.isCreditCardPayment)
                OpenSupportedBankAppsBtmSheet -> showSupportedBankAppsBtmSheet = true
                CloseVas -> closeVas()
                else -> Unit
            }
        }
    }
}

inline fun checkExternalStoragePermission(
    context: Context,
    externalStoragePermissionLauncher: ManagedActivityResultLauncher<String, Boolean>,
    onGranted: () -> Unit
) {
    when {
        ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.WRITE_EXTERNAL_STORAGE
        ) == PackageManager.PERMISSION_GRANTED || Build.VERSION.SDK_INT >= Build.VERSION_CODES.R -> onGranted()

        else -> externalStoragePermissionLauncher.launch(Manifest.permission.WRITE_EXTERNAL_STORAGE)
    }
}

@Composable
fun rememberWriteToExternalStoragePermissionLauncher(
    scope: CoroutineScope,
    onGranted: () -> Unit
): ManagedActivityResultLauncher<String, Boolean> {
    return rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            onGranted()
        } else {
            scope.launch {
                SnackBarController.showSnackBar(
                    SnackBarEvent(
                        message = UiText.FromRes(R.string.common_storage_permissions_required),
                        type = SnackBarType.WARNING
                    )
                )
            }
        }
    }
}

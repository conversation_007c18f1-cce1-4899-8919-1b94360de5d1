package se.scmv.morocco.ad.vas.steps.bankapps

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import se.scmv.morocco.domain.models.BankApp
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.repositories.ConfigRepository
import javax.inject.Inject

@HiltViewModel
class SupportedBankAppsViewModel @Inject constructor(
    configRepository: ConfigRepository
) : ViewModel() {

    private val _bankApps = MutableStateFlow(listOf<BankApp>())
    val bankApps = _bankApps.asStateFlow()

    init {
        viewModelScope.launch {
            when(val result = configRepository.getSupportedBankApps()) {
                is Resource.Success -> _bankApps.update { result.data }
                is Resource.Failure -> {
                    // Shouldn't happen
                }
            }
        }
    }
}
package se.scmv.morocco.ad.insert

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import kotlinx.collections.immutable.ImmutableList
import kotlinx.coroutines.flow.collectLatest
import se.scmv.morocco.ad.R
import se.scmv.morocco.ad.insert.AdInsertOneTimeEvents.NavigateToInsertResultScreen
import se.scmv.morocco.ad.insert.AdInsertOneTimeEvents.NavigateToVas
import se.scmv.morocco.ad.insert.AdInsertOneTimeEvents.ScrollCurrentStepToIndex
import se.scmv.morocco.ad.insert.AdInsertOneTimeEvents.ScrollToStep
import se.scmv.morocco.ad.insert.AdInsertOneTimeEvents.ShowCloseConfirmationDialog
import se.scmv.morocco.ad.navigation.VasRoute
import se.scmv.morocco.designsystem.components.AvConfirmationAlertDialog
import se.scmv.morocco.designsystem.components.AvProgressBar
import se.scmv.morocco.designsystem.components.AvStepper
import se.scmv.morocco.designsystem.components.AvTopAppBar
import se.scmv.morocco.designsystem.components.ScreenErrorState
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.domain.models.Account

// I know like this we can't preview the different states of the screen,
// but to reduce the number of recompositions, because this feature is a bit complicated and critical for the app.
@Composable
fun AdInsertRoute(
    modifier: Modifier = Modifier,
    account: Account.Connected,
    navigateBack: () -> Unit,
    navigateToInsertResultScreen: (VasRoute.From) -> Unit,
    navigateToVas: (VasRoute) -> Unit,
    viewModel: AdInsertViewModel = hiltViewModel<AdInsertViewModel>().apply {
        setAccount(account)
    }
) {
    val state = viewModel.viewState.collectAsStateWithLifecycle().value
    var showCloseConfirmDialog by remember { mutableStateOf(false) }
    val keyboardController = LocalSoftwareKeyboardController.current
    when {
        state.steps.isEmpty() && state.error != null -> Scaffold(
            topBar = { AvTopAppBar(onNavigationIconClicked = navigateBack) }
        ) {
            ScreenErrorState(
                modifier = modifier
                    .padding(it)
                    .fillMaxSize(),
                title = stringResource(R.string.common_oups),
                description = state.error.getValue(LocalContext.current),
                actionText = stringResource(R.string.common_refresh),
                onActionClicked = viewModel::onRefreshClicked
            )
        }

        state.steps.isNotEmpty() -> {
            val pagerState = rememberPagerState { state.steps.size }
            val stepsScrollState = List(state.steps.size) { rememberLazyListState() }
            val currentStep by remember { derivedStateOf { state.steps[pagerState.currentPage] } }
            AvStepper(
                pagerState = pagerState,
                steps = headerSteps,
                footerLeftContent = {
                    FooterLeftContent(
                        modifier = Modifier.weight(4f),
                        tipsTitle = currentStep.tipsTitle,
                        tipsContent = currentStep.tipsContent,
                        infoUrl = currentStep.infoUrl,
                    )
                },
                onBackClicked = viewModel::onBackClicked,
                onCloseClicked = { showCloseConfirmDialog = true },
                onNextClicked = viewModel::onNextClicked
            ) { pageIndex ->
                LazyColumn(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(
                            color = MaterialTheme.colorScheme.background,
                            shape = MaterialTheme.shapes.small
                        ),
                    state = stepsScrollState[pageIndex],
                    contentPadding = PaddingValues(
                        // Because the first header component will add a spacer of MaterialTheme.dimens.default .
                        top = MaterialTheme.dimens.small,
                        start = MaterialTheme.dimens.big,
                        end = MaterialTheme.dimens.big,
                        bottom = MaterialTheme.dimens.big
                    ),
                ) {
                    items(state.steps[pageIndex].components) { group ->
                        group.Display(modifier = Modifier.fillMaxWidth())
                    }
                }
            }
            BackHandler(onBack = viewModel::onBackClicked)
            LaunchedEffect(Unit) {
                viewModel.oneTimeEvents.collectLatest {
                    when (it) {
                        is ScrollToStep -> {
                            keyboardController?.hide()
                            pagerState.animateScrollToPage(it.index)
                        }

                        is ScrollCurrentStepToIndex -> {
                            stepsScrollState[it.stepIndex].animateScrollToItem(it.itemIndex)
                        }

                        is NavigateToVas -> navigateToVas(it.route)

                        ShowCloseConfirmationDialog -> {
                            showCloseConfirmDialog = true
                        }

                        is NavigateToInsertResultScreen -> navigateToInsertResultScreen(it.from)
                    }
                }
            }
        }
    }
    if (showCloseConfirmDialog) {
        AvConfirmationAlertDialog(
            title = stringResource(R.string.ad_insert_cancel_dialog_title),
            description = stringResource(R.string.ad_insert_cancel_dialog_description),
            onConfirm = {
                showCloseConfirmDialog = false
                navigateBack()
            },
            onCancel = { showCloseConfirmDialog = false },
        )
    }
    if (state.isLoading) {
        AvProgressBar(text = stringResource(R.string.common_loading))
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun FooterLeftContent(
    modifier: Modifier = Modifier,
    tipsTitle: String,
    tipsContent: ImmutableList<String>,
    infoUrl: String,
) {
    var showInfosBtmSheet by remember { mutableStateOf(false) }
    TextButton(
        modifier = modifier,
        onClick = { showInfosBtmSheet = true }
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium)
        ) {
            Icon(
                painter = painterResource(R.drawable.ic_light_bulb),
                contentDescription = null,
                tint = MaterialTheme.colorScheme.primary
            )
            Text(text = tipsTitle, style = MaterialTheme.typography.bodyLarge)
        }
    }
    if (showInfosBtmSheet) {
        ModalBottomSheet(
            onDismissRequest = { showInfosBtmSheet = false },
            sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)
        ) {
            AdInsertHelpBottomSheet(
                title = tipsTitle,
                content = tipsContent.joinToString(separator = "\n\n") { "• $it" },
                termsAndConditionUrl = infoUrl,
            )
        }
    }
}
package se.scmv.morocco.ad.insert

import kotlinx.collections.immutable.persistentListOf
import se.scmv.morocco.ad.R
import se.scmv.morocco.designsystem.components.AvAlertType
import se.scmv.morocco.domain.models.AdTypeKey
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.repositories.AdRepository
import se.scmv.morocco.orion.components.AdInsertLapLimitation
import javax.inject.Inject

// case 1: Show an informational LAP when exactly 2 free insertions remain before hitting the free limit
// case 2: User reaches the limit of his free ads and now need to pay for his current ad.
// case 3: The case of a category where the user needs to pay from the first insertion like car rental.
// case 4: User reaches the limit to open a shop

class GetAccountAdLimitationsUseCase @Inject constructor(
    private val adRepository: AdRepository
) {
    suspend operator fun invoke(
        categoryId: String,
        adTypeKey: AdTypeKey,
        isEdit: Boolean
    ): AdInsertLapLimitation? {
        val result = adRepository.getAdLimitations(categoryId, adTypeKey)
        return when (result) {
            is Resource.Success -> {
                val userAdCount = result.data.userCategoryAdCount
                val configuredCategoryFreeLimit = result.data.configuredCategoryFreeLimit
                val configuredCategoryStoreLimit = result.data.configuredCategoryStoreLimit
                val categoryFree = result.data.categoryFree
                val categoryStore = result.data.categoryStore
                when {
                    // CASE 3:
                    configuredCategoryFreeLimit == 0 -> AdInsertLapLimitation(
                        messageResId = R.string.ad_insert_lap_limitation3,
                        messageArgs = persistentListOf(),
                        alertType = AvAlertType.Warning,
                        isLimit = false
                    )

                    // CASE 1:
                    // Show the informational LAP message only when the user has exactly 2 free insertions remaining
                    configuredCategoryFreeLimit != null &&
                            userAdCount < configuredCategoryFreeLimit &&
                            (configuredCategoryFreeLimit - userAdCount == 2) -> AdInsertLapLimitation(
                        messageResId = R.string.ad_insert_lap_limitation1,
                        messageArgs = persistentListOf(
                            "${if (isEdit) userAdCount else userAdCount + 1}",
                            "$configuredCategoryFreeLimit",
                            categoryFree?.name.orEmpty()
                        ),
                        alertType = AvAlertType.Info,
                        isLimit = false
                    )

                    // CASE 2:
                    // User has reached or exceeded the free limit but is still below the store limit
                    configuredCategoryFreeLimit != null &&
                            userAdCount >= configuredCategoryFreeLimit &&
                            (configuredCategoryStoreLimit == null || userAdCount < configuredCategoryStoreLimit)
                        -> AdInsertLapLimitation(
                        messageResId = R.string.ad_insert_lap_limitation2,
                        messageArgs = persistentListOf(
                            "${if (isEdit) userAdCount else userAdCount + 1}"
                        ),
                        alertType = AvAlertType.Info,
                        isLimit = true
                    )

                    // CASE 4:
                    configuredCategoryStoreLimit != null && userAdCount >= configuredCategoryStoreLimit -> AdInsertLapLimitation(
                        messageResId = R.string.ad_insert_lap_limitation4,
                        messageArgs = persistentListOf(
                            "${if (isEdit) userAdCount else userAdCount + 1}",
                            categoryStore?.name.orEmpty()
                        ),
                        alertType = AvAlertType.Info,
                        isLimit = true
                    )

                    else -> null
                }
            }

            is Resource.Failure -> null
        }
    }
}
package se.scmv.morocco.ad.ad_view.components

import android.content.Intent
import android.net.Uri
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Card
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import se.scmv.morocco.ad.R
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.domain.models.AdTouchingPoint
import se.scmv.morocco.info_center.AvWebViewActivity


@Composable
fun TouchingPointAdItem(
    ad: AdTouchingPoint,
    modifier: Modifier = Modifier,
    onTouchingPointClicked: (AdTouchingPoint) -> Unit = {}
) {
    val context = LocalContext.current

    Card(
        modifier = modifier
            .fillMaxWidth()
            .clip(MaterialTheme.shapes.medium)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable {
                    onTouchingPointClicked(ad)
                    if (!ad.campaignData.redirectLink.isNullOrEmpty()) {
                        AvWebViewActivity.open(context, ad.campaignData.redirectLink)
                    }
                },
            verticalAlignment = Alignment.CenterVertically
        ) {
            AsyncImage(
                model = ad.campaignData.clientLogo,
                contentDescription = ad.campaignData.description,
                modifier = Modifier
                    .size(50.dp)
                    .clip(MaterialTheme.shapes.small),
                contentScale = ContentScale.Crop,
                placeholder = painterResource(id = R.drawable.ic_no_image),
                error = painterResource(id = R.drawable.ic_no_image)
            )
            Spacer(modifier = Modifier.width(MaterialTheme.dimens.regular))
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = ad.campaignData.title,
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.SemiBold,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )
                if (ad.campaignData.description.isNotEmpty()) {
                    Text(
                        text = ad.campaignData.description,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }
            Spacer(modifier = Modifier.width(MaterialTheme.dimens.medium))

            Image(
                painter = painterResource(R.drawable.touching_point_call_to_action),
                contentDescription = "Avito Logo",
                modifier = Modifier
                    .size(32.dp)
            )


        }
    }
}

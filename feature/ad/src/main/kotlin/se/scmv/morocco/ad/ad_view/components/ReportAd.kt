package se.scmv.morocco.ad.ad_view.components

import android.util.Patterns
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringArrayResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.toImmutableList
import se.scmv.morocco.ad.R
import se.scmv.morocco.designsystem.components.AvDropdown
import se.scmv.morocco.designsystem.components.AvPrimaryButton
import se.scmv.morocco.designsystem.components.AvTextField
import se.scmv.morocco.designsystem.components.DropdownData
import se.scmv.morocco.designsystem.components.avOutlinedTextFieldColors
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.dimens

@Composable
fun ReportAdContent(
    onDismissRequest: () -> Unit,
    onSendReport: (String, String, String) -> Unit,
    showHideProgressAdReport: Boolean
) {
    var email by remember { mutableStateOf("") }
    var isEmailValid by remember { mutableStateOf(true) }
    var selectedReason by remember { mutableStateOf<DropdownData?>(null) }
    var isReasonSelected by remember { mutableStateOf(true) }
    var message by remember { mutableStateOf("") }
    var isMessageValid by remember { mutableStateOf(true) }
    var isSubmitButtonClicked by remember { mutableStateOf(false) } // Track if submit was clicked

    val reportReasonsArray = stringArrayResource(R.array.report_ad_reason)
    val reasonOptions: ImmutableList<DropdownData> = remember(reportReasonsArray) {
        reportReasonsArray.drop(1).map { item ->
            val parts = item.split("|", limit = 2)
            if (parts.size == 2) {
                DropdownData(id = parts[0], name = parts[1])
            } else {
                DropdownData(id = "", name = item)
            }
        }.toImmutableList()
    }

    val isReportEnabled by remember(
        isSubmitButtonClicked,
        isEmailValid,
        isReasonSelected,
        isMessageValid
    ) {
        derivedStateOf { isSubmitButtonClicked && isEmailValid && isReasonSelected && isMessageValid }
    }

    Column(
        modifier = Modifier
            .padding(MaterialTheme.dimens.default)
            .fillMaxWidth()
    ) {
        Text(
            stringResource(R.string.report_problem_question),
            fontWeight = FontWeight.Bold,
            fontSize = 16.sp,
        )

        Spacer(modifier = Modifier.height(MaterialTheme.dimens.default))

        AvTextField(
            value = email,
            placeholder = R.string.common_email_field_required,
            onValueChanged = {
                email = it
                if (isSubmitButtonClicked) {
                    isEmailValid = Patterns.EMAIL_ADDRESS.matcher(it).matches()
                } else {
                    isEmailValid = true
                }
            },
            title = R.string.email_label,
            trailingIcon = {
                Icon(
                    modifier = Modifier.size(MaterialTheme.dimens.bigger),
                    painter = painterResource(id = R.drawable.ic_email_outline),
                    contentDescription = null,
                )
            },
            modifier = Modifier.fillMaxWidth(),
            keyboardOptions = KeyboardOptions.Default.copy(keyboardType = KeyboardType.Email),
            required = true,
            error = if (!isEmailValid && isSubmitButtonClicked) stringResource(R.string.invalid_email) else null,
            colors = avOutlinedTextFieldColors
        )

        Spacer(modifier = Modifier.height(MaterialTheme.dimens.regular))

        AvDropdown(
            modifier = Modifier.fillMaxWidth(),
            items = reasonOptions,
            selectedItem = selectedReason,
            title = stringResource(R.string.reason_label),
            required = true,
            onItemSelected = {
                selectedReason = it
                if (isSubmitButtonClicked) { //Validate only after click
                    isReasonSelected = it != null
                } else {
                    isReasonSelected = true
                }
            },
            error = if (!isReasonSelected && isSubmitButtonClicked) stringResource(R.string.reason_required) else null,
        )

        Spacer(modifier = Modifier.height(MaterialTheme.dimens.regular))

        AvTextField(
            value = message,
            placeholder = R.string.message_required,
            onValueChanged = {
                message = it
                if (isSubmitButtonClicked) { // Validate only after click
                    isMessageValid = it.isNotBlank()
                } else {
                    isMessageValid = true
                }
            },
            title = R.string.message_label,
            trailingIcon = {
                Icon(
                    modifier = Modifier.size(MaterialTheme.dimens.bigger),
                    painter = painterResource(id = R.drawable.ic_outlined_chat),
                    contentDescription = null,
                )
            },
            modifier = Modifier
                .fillMaxWidth()
                .height(120.dp),
            maxLines = 4,
            required = true,
            error = if (!isMessageValid && isSubmitButtonClicked) stringResource(R.string.message_required) else null,
            colors = avOutlinedTextFieldColors
        )

        Spacer(modifier = Modifier.height(MaterialTheme.dimens.bigger))

        AvPrimaryButton(
            onClick = {
                isSubmitButtonClicked = true // Set flag on click
                val currentEmailValid = Patterns.EMAIL_ADDRESS.matcher(email).matches()
                val currentReasonSelected = selectedReason != null
                val currentMessageValid = message.isNotBlank()

                isEmailValid = currentEmailValid
                isReasonSelected = currentReasonSelected
                isMessageValid = currentMessageValid

                if (isReportEnabled && selectedReason != null) {
                    onSendReport(email, selectedReason!!.id, message)
                    onDismissRequest()
                }
            },
            modifier = Modifier
                .fillMaxWidth()
                .height(50.dp),
            text = stringResource(R.string.send_report_button_text),
            enabled = true,
            loading = showHideProgressAdReport
        )
    }
}

@Preview(showBackground = true)
@Composable
fun ReportAdContentPreview() {
    AvitoTheme {
        ReportAdContent(
            onDismissRequest = {},
            onSendReport = { x, y, w -> },
            showHideProgressAdReport = true
        )
    }
}
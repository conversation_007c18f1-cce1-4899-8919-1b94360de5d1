package se.scmv.morocco.ad.ad_view


import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.rizzi.bouquet.ResourceType
import com.rizzi.bouquet.VerticalPDFReader
import com.rizzi.bouquet.rememberVerticalPdfReaderState
import se.scmv.morocco.ad.R
import se.scmv.morocco.designsystem.components.ScreenErrorState

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PDFViewerScreen(
    pdfUrl: String,
    navigateBack: () -> Unit
) {
    val pdfState = rememberVerticalPdfReaderState(
        resource = ResourceType.Remote(pdfUrl),
        isZoomEnable = true
    )

    var hasError by remember { mutableStateOf(false) }
    var errorMessage by remember { mutableStateOf<String?>(null) }
    var loading by remember { mutableStateOf(true) }

    // Track errors
    LaunchedEffect(pdfState.error) {
        pdfState.error?.let {
            hasError = true
            errorMessage = it.message ?: "Unknown error occurred"
        }
    }

    // Track loading state
    LaunchedEffect(pdfState.loadPercent) {
        loading = pdfState.loadPercent < 100
    }

    // Cleanup when leaving screen
    DisposableEffect(Unit) {
        onDispose {
            pdfState.close()  // Ensure PDF is released from memory
        }
    }
    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    if (pdfState.isLoaded) {
                        Text(
                            text = "${pdfState.currentPage} / ${pdfState.pdfPageCount}",
                            maxLines = 1,
                            fontWeight = FontWeight.Bold,
                            style = TextStyle(fontSize = 14.sp),
                            modifier = Modifier
                                .padding(start = 8.dp, end = 8.dp)
                        )
                    }
                },
                navigationIcon = {
                    IconButton(onClick = navigateBack) {
                        Icon(Icons.AutoMirrored.Default.ArrowBack, contentDescription = "Back")
                    }
                }
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            when {
                hasError -> ScreenErrorState(
                    title = errorMessage ?: "Failed to load PDF",
                    description = "",
                    actionText = stringResource(R.string.common_refresh),
                    onActionClicked = { hasError = false }
                )

                else -> {
                    // Display PDF reader along with the loader
                    Box(modifier = Modifier.fillMaxSize()) {
                        VerticalPDFReader(
                            state = pdfState,
                            modifier = Modifier.fillMaxSize()
                        )

                        // Show loading progress only if still loading
                        if (!pdfState.isLoaded) {
                            Column(
                                modifier = Modifier
                                    .fillMaxSize(),
                                horizontalAlignment = Alignment.CenterHorizontally,
                                verticalArrangement = Arrangement.Center
                            ) {
                                CircularProgressIndicator(
                                    progress = pdfState.loadPercent / 129f,
                                    modifier = Modifier.size(48.dp)
                                )
                                Text(
                                    text = "${pdfState.loadPercent} ..",
                                    style = MaterialTheme.typography.bodyMedium,
                                    modifier = Modifier.padding(top = 8.dp)
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

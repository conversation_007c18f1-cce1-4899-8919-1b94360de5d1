package se.scmv.morocco.ad.vas.steps.vaspackage

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.util.fastMap
import coil.compose.AsyncImage
import kotlinx.collections.immutable.toImmutableList
import se.scmv.morocco.ad.R
import se.scmv.morocco.ad.vas.master.ChooseVasPackageViewState
import se.scmv.morocco.designsystem.components.AvListingAdParams
import se.scmv.morocco.designsystem.components.DropdownData
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.domain.models.AdParam
import se.scmv.morocco.domain.models.VasPack
import se.scmv.morocco.domain.models.VasPackage
import se.scmv.morocco.domain.models.VasPackageExecutionSlotsTime
import se.scmv.morocco.orion.components.vas.VasPacks

@Composable
fun ChooseVasPackageScreen(
    modifier: Modifier = Modifier,
    state: ChooseVasPackageViewState,
    chosenVasPackageId: String?,
    chosenExecutionSlotsDay: String?,
    chosenExecutionSlotsTimeId: String?,
    onPackageSelected: (VasPack, VasPackage) -> Unit,
    onExeSlotDaySelected: (String) -> Unit,
    onExeSlotTimeSelected: (String) -> Unit,
    onAddImageClicked: () -> Unit,
) {
    Column(
        modifier = modifier
            .verticalScroll(rememberScrollState())
            .background(MaterialTheme.colorScheme.background, MaterialTheme.shapes.small)
            .padding(MaterialTheme.dimens.big),
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.big)
    ) {
        Column(
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small)
        ) {
            Text(
                text = stringResource(R.string.choose_vas_package_screen_title),
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Bold
            )
            Text(
                text = stringResource(R.string.choose_vas_package_screen_description),
                style = MaterialTheme.typography.labelMedium,
            )
        }
        with(state.adToBoost) {
            ChooseVasPackageAdDetails(
                imageUrl = imageUrl,
                title = title,
                description = description,
                categoryLocation = stringResource(
                    R.string.choose_vas_package_screen_ad_category_location,
                    categoryName,
                    city.name,
                    city.area?.name.orEmpty()
                ),
                adParams = params
            )
        }
        with(state.vasPacks) {
            VasPacks(
                packs = packs.toImmutableList(),
                isAdToBoostHasImage = state.adToBoost.imageUrl != null,
                selectedPackageId = chosenVasPackageId.orEmpty(),
                exeSlotsDays = executionSlots?.days?.toImmutableList(),
                exeSlotsTimes = executionSlots?.times
                    ?.fastMap { DropdownData(id = it.id, name = it.displayedTime()) }
                    ?.toImmutableList(),
                selectedExeSlotsDay = chosenExecutionSlotsDay
                    ?: executionSlots?.days?.firstOrNull().orEmpty(),
                selectedExeSlotsTime = chosenExecutionSlotsTimeId ?: executionSlots?.times?.let {
                    VasPackageExecutionSlotsTime.findCurrentOrFirstTimeSlot(it)
                }?.id,
                onPackageSelected = onPackageSelected,
                onExeSlotDaySelected = onExeSlotDaySelected,
                onExeSlotTimeSelected = onExeSlotTimeSelected,
                onAddImageClicked = onAddImageClicked
            )
        }
    }
}

@Composable
private fun ChooseVasPackageAdDetails(
    modifier: Modifier = Modifier,
    imageUrl: String?,
    title: String,
    description: String,
    categoryLocation: String,
    adParams: List<AdParam>
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.big)
    ) {
        val imageModifier = Modifier
            .fillMaxWidth()
            .aspectRatio(1.6f)
            .clip(MaterialTheme.shapes.small)
            .border(0.5.dp, MaterialTheme.colorScheme.outline, MaterialTheme.shapes.small)
        imageUrl?.let { image ->
            AsyncImage(
                modifier = imageModifier,
                model = image,
                contentDescription = "Principal Image",
                contentScale = ContentScale.Crop,
                placeholder = painterResource(R.drawable.img_no_image_placeholder),
                error = painterResource(R.drawable.img_no_image_placeholder),
            )
        } ?: run {
            Image(
                modifier = imageModifier,
                painter = painterResource(R.drawable.img_no_image_placeholder),
                contentDescription = "Principal Image",
                contentScale = ContentScale.Crop,
            )
        }
        Column(
            modifier = modifier,
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Bold
            )
            Text(
                text = description,
                style = MaterialTheme.typography.labelMedium,
            )
        }
        AvListingAdParams(modifier.fillMaxWidth(), params = adParams)
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small)
        ) {
            Image(painter = painterResource(R.drawable.ic_location), contentDescription = null)
            Text(text = categoryLocation, style = MaterialTheme.typography.labelSmall)
        }
        HorizontalDivider()
    }
}
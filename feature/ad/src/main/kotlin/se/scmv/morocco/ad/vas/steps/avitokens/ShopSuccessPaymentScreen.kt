package se.scmv.morocco.ad.vas.steps.avitokens

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import se.scmv.morocco.ad.R
import se.scmv.morocco.designsystem.theme.Green500
import se.scmv.morocco.designsystem.theme.dimens

@Composable
fun ShopSuccessPaymentScreen(modifier: Modifier = Modifier) {
    Box(
        modifier = modifier
            .background(MaterialTheme.colorScheme.background, MaterialTheme.shapes.small)
            .padding(top = MaterialTheme.dimens.extraBig)
            .padding(MaterialTheme.dimens.big),
    ) {
        Image(
            modifier = Modifier.fillMaxSize(),
            painter = painterResource(R.drawable.img_confetti),
            contentDescription = "Confetti illustration"
        )
        Column(
            modifier = Modifier.fillMaxSize(),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Image(
                painter = painterResource(R.drawable.ic_payment_success),
                contentDescription = "Success icon"
            )
            Spacer(Modifier.height(MaterialTheme.dimens.big))
            Text(
                text = stringResource(R.string.shop_payment_success_screen_description),
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Bold,
                color = Green500,
                textAlign = TextAlign.Center
            )
        }
    }
}

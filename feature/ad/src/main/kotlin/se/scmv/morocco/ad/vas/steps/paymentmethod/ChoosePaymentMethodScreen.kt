package se.scmv.morocco.ad.vas.steps.paymentmethod

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedCard
import androidx.compose.material3.RadioButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import coil.ImageLoader
import coil.compose.AsyncImage
import coil.compose.rememberAsyncImagePainter
import coil.decode.GifDecoder
import coil.request.ImageRequest
import se.scmv.morocco.ad.R
import se.scmv.morocco.ad.vas.master.ChoosePaymentMethodViewState
import se.scmv.morocco.ad.vas.master.ShopAccountInfo
import se.scmv.morocco.designsystem.components.AvSecondaryButton
import se.scmv.morocco.designsystem.theme.Blue50
import se.scmv.morocco.designsystem.theme.Brown500
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.domain.models.PaymentMethodType
import se.scmv.morocco.domain.models.VasPack
import se.scmv.morocco.domain.models.VasPackage
import se.scmv.morocco.orion.components.vas.displayedName
import java.util.Locale

@Composable
fun ChoosePaymentMethodScreen(
    modifier: Modifier = Modifier,
    state: ChoosePaymentMethodViewState,
    chosenVasPack: VasPack?,
    chosenVasPackage: VasPackage?,
    chosenPaymentMethod: PaymentMethodType?,
    onChangePackBtnClicked: () -> Unit,
    onPaymentMethodChanged: (PaymentMethodType) -> Unit
) {
    Column(
        modifier = modifier
            .verticalScroll(rememberScrollState())
            .background(MaterialTheme.colorScheme.background, MaterialTheme.shapes.small)
            .padding(top = MaterialTheme.dimens.extraBig)
            .padding(MaterialTheme.dimens.big),
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.big)
    ) {
        state.shopAccountInfo?.let { info ->
            ShopAccountInfo(shopAccountInfo = info)
        }

        if (chosenVasPack != null && chosenVasPackage != null) {
            ChosenVasPackageInfo(
                imageUrl = chosenVasPack.image,
                title = chosenVasPack.title,
                packDuration = String.format(
                    Locale.getDefault(),
                    "%s %s",
                    chosenVasPackage.durationDays,
                    stringResource(R.string.days)
                ),
                packPrice = String.format(
                    Locale.getDefault(),
                    "%s %s",
                    chosenVasPackage.price,
                    stringResource(chosenVasPackage.priceUnit.displayedName()),
                ),
                onChangeBtnClicked = onChangePackBtnClicked
            )
        }

        HorizontalDivider()

        Column(
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small)
        ) {
            Text(
                text = stringResource(R.string.choose_vas_payment_method_screen_vas_title),
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Bold
            )
            Text(
                text = stringResource(R.string.choose_vas_payment_method_screen_vas_description),
                style = MaterialTheme.typography.bodyMedium,
            )
        }

        Column(
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.large)
        ) {
            state.paymentMethods.forEach {
                PaymentMethod(
                    icon = it.icon,
                    name = it.title,
                    description = it.description,
                    selected = chosenPaymentMethod == it.type,
                    onClick = { onPaymentMethodChanged(it.type) }
                )
            }
        }
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
private fun ShopAccountInfo(
    modifier: Modifier = Modifier,
    shopAccountInfo: ShopAccountInfo
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.big)
    ) {
        with(shopAccountInfo) {
            Card(
                shape = CircleShape,
                elevation = CardDefaults.elevatedCardElevation(defaultElevation = 2.dp)
            ) {
                imageUrl?.let {
                    AsyncImage(
                        model = it,
                        contentDescription = null,
                        contentScale = ContentScale.Crop,
                        error = painterResource(id = R.drawable.img_user_placeholder)
                    )
                } ?: Image(
                    modifier = Modifier.size(60.dp),
                    painter = painterResource(id = R.drawable.img_user_placeholder),
                    contentDescription = null,
                )
            }
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(text = name, style = MaterialTheme.typography.titleSmall)
                if (isVerified) {
                    Icon(
                        painter = painterResource(R.drawable.ic_verified),
                        contentDescription = null,
                        tint = Brown500
                    )
                }
            }
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium)
            ) {
                dates.forEach {
                    FlowRow(
                        modifier = Modifier.fillMaxWidth(),
                        verticalArrangement = Arrangement.Center,
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = stringResource(it.first),
                            style = MaterialTheme.typography.bodyMedium
                        )
                        Text(
                            text = it.second,
                            style = MaterialTheme.typography.bodyLarge,
                        )
                    }
                }
            }
            ShopSubscription(points = storePoints, membership = storeMembership)
        }
    }
}

@Composable
private fun ChosenVasPackageInfo(
    imageUrl: String?,
    title: String,
    packDuration: String,
    packPrice: String,
    onChangeBtnClicked: () -> Unit
) {
    OutlinedCard(
        elevation = CardDefaults.elevatedCardElevation(defaultElevation = 0.2.dp),
        border = CardDefaults.outlinedCardBorder(enabled = false)
    ) {
        Column(
            modifier = Modifier.fillMaxWidth(),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.large)
        ) {
            imageUrl?.let { image ->
                Image(
                    modifier = Modifier
                        .fillMaxWidth()
                        .aspectRatio(1.6f),
                    painter = rememberAsyncImagePainter(
                        model = ImageRequest.Builder(LocalContext.current)
                            .data(data = image)
                            .build(),
                        imageLoader = ImageLoader.Builder(LocalContext.current)
                            .components {
                                // TODO Upgrade to coil3 and uncomment this code.
                                /*if (VERSION.SDK_INT >= 28) {
                                    add(AnimatedImageDecoder.Factory())
                                } else {
                                    add(GifDecoder.Factory())
                                }*/
                                add(GifDecoder.Factory())
                            }
                            .build()
                    ),
                    contentDescription = "Chosen pack Image",
                    contentScale = ContentScale.Crop,
                )
            } ?: Image(
                modifier = Modifier
                    .fillMaxWidth()
                    .aspectRatio(1.6f),
                contentDescription = "Chosen pack placeholder Image",
                contentScale = ContentScale.Crop,
                painter = painterResource(R.drawable.img_no_image_placeholder),
            )
            Column(
                modifier = Modifier
                    .padding(top = MaterialTheme.dimens.large)
                    .padding(bottom = MaterialTheme.dimens.big)
                    .padding(horizontal = MaterialTheme.dimens.large)
                    .fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.large)
            ) {
                Text(text = title, style = MaterialTheme.typography.labelLarge)
                HorizontalDivider()
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column(
                        horizontalAlignment = Alignment.Start,
                        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small)
                    ) {
                        Text(
                            text = stringResource(R.string.choose_vas_payment_method_screen_vas_duration),
                            style = MaterialTheme.typography.labelSmall
                        )
                        Text(
                            text = packDuration,
                            style = MaterialTheme.typography.labelMedium,
                            fontWeight = FontWeight.Bold
                        )
                    }
                    Column(
                        horizontalAlignment = Alignment.End,
                        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small)
                    ) {
                        Text(
                            text = stringResource(R.string.choose_vas_payment_method_screen_vas_total_price),
                            style = MaterialTheme.typography.labelSmall
                        )
                        Text(
                            text = packPrice,
                            style = MaterialTheme.typography.labelMedium,
                            fontWeight = FontWeight.Bold
                        )
                    }
                }
                AvSecondaryButton(
                    modifier = Modifier.fillMaxWidth(),
                    text = stringResource(R.string.common_change),
                    onClick = onChangeBtnClicked
                )
            }
        }
    }
}

@Composable
private fun ShopSubscription(
    modifier: Modifier = Modifier,
    points: Int,
    membership: String,
) {
    Row(
        modifier = modifier
            .clip(MaterialTheme.shapes.medium)
            .border(1.dp, Blue50, MaterialTheme.shapes.medium),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium)
    ) {
        Text(
            modifier = Modifier.padding(start = MaterialTheme.dimens.default),
            text = "$points",
            style = MaterialTheme.typography.headlineLarge,
            color = MaterialTheme.colorScheme.primary
        )
        Text(
            text = stringResource(R.string.common_avitokens),
            style = MaterialTheme.typography.bodyLarge,
        )
        Text(
            modifier = Modifier
                .background(color = MaterialTheme.colorScheme.primary.copy(alpha = 0.2f))
                .padding(MaterialTheme.dimens.default),
            text = membership,
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.primary,
            fontWeight = FontWeight.Bold
        )
    }
}

@Composable
private fun PaymentMethod(
    icon: Int,
    name: Int,
    description: Int,
    selected: Boolean,
    onClick: () -> Unit
) {
    OutlinedCard(
        onClick = onClick,
        modifier = Modifier.fillMaxWidth(),
        border = BorderStroke(
            1.dp,
            if (selected) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.outline
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(MaterialTheme.dimens.large),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.large)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.large)
                ) {
                    Image(
                        modifier = Modifier.height(MaterialTheme.dimens.bigger),
                        painter = painterResource(icon),
                        contentDescription = "Payment method icon"
                    )
                    Text(
                        text = stringResource(name),
                        style = MaterialTheme.typography.bodyLarge,
                        fontWeight = FontWeight.Medium
                    )
                }
                Text(
                    text = stringResource(description),
                    style = MaterialTheme.typography.bodyMedium
                )
            }
            RadioButton(selected = selected, onClick = null)
        }
    }
}
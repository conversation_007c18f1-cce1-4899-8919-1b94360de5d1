package se.scmv.morocco.ad.ad_view.components

import android.content.res.Configuration
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import se.scmv.morocco.ad.R
import se.scmv.morocco.designsystem.theme.dimens

@Composable
fun DeliveryItem(
    onClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .background(color = Color(0xFFFFA500))
            .padding(horizontal = MaterialTheme.dimens.default, vertical = MaterialTheme.dimens.betweenSmallMedium)
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.padding(vertical = MaterialTheme.dimens.none)
        ) {
            Image(
                painter = painterResource(id = R.drawable.ic_delivery),
                contentDescription = null
            )

            Spacer(modifier = Modifier.width(MaterialTheme.dimens.medium))

            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = stringResource(id = R.string.delivery),
                    fontSize = 15.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color.White
                )

                Spacer(modifier = Modifier.height(MaterialTheme.dimens.small))

                Text(
                    text = stringResource(id = R.string.delivery_label),
                    style = MaterialTheme.typography.bodyMedium.copy(lineHeight = 16.sp),
                    color = Color.White,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.widthIn(max = 250.dp)
                )
            }

            Spacer(modifier = Modifier.width(MaterialTheme.dimens.medium))
            Box(
                modifier = Modifier
                    .background(Color.White, shape = CircleShape)
                    .padding(MaterialTheme.dimens.medium)
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_arrow_right_orange),
                    contentDescription = null,
                    tint = Color(0xFFF48B29),

                    )
            }

        }
    }
}

@Preview(showBackground = true)
@Composable
fun DeliveryItemPreview() {
    MaterialTheme {
        DeliveryItem(
            onClick = { }
        )
    }
}

@Preview(showBackground = true)
@Composable
fun EnchereItemPreview() {
    MaterialTheme {
        EnchereItem(
            onClick = { }
        )
    }
}

@Composable
fun EnchereItem(
    onClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .background(color = Color(0xFF3AA4FF))
            .padding(horizontal = MaterialTheme.dimens.default, vertical = MaterialTheme.dimens.betweenSmallMedium)
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.padding(vertical = MaterialTheme.dimens.none)
        ) {
            Image(
                painter = painterResource(id = R.drawable.ic_auctions),
                contentDescription = null
            )

            Spacer(modifier = Modifier.width(MaterialTheme.dimens.medium))

            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = stringResource(id = R.string.public_auctions),
                    fontSize = 15.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color.White
                )

                Spacer(modifier = Modifier.height(MaterialTheme.dimens.small))

                Text(
                    text = stringResource(id = R.string.public_auction_notice),
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color.White,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.widthIn(max = 250.dp)
                )
            }

        }
    }
}


@Preview(showBackground = true,
    uiMode = Configuration.UI_MODE_NIGHT_YES or Configuration.UI_MODE_TYPE_NORMAL
)
@Composable
fun OrderBottomSheetPreview() {
    OrderBottomSheet(
        imageUrl = "",
        title = "LENOVO X1 CARBON G7 8G",
        price = "4200 DH",
        total = 4230,
        onConfirm = {}
    )
}


@Composable
fun OrderBottomSheet(
    imageUrl: String,
    title: String,
    price: String,
    total: Int,
    onConfirm: () -> Unit
) {
    Column(
        modifier = Modifier.fillMaxWidth()
    ) {
        AsyncImage(
            model = imageUrl,
            contentDescription = "Ad Image",
            modifier = Modifier
                .fillMaxWidth()
                .height(250.dp),
            contentScale = ContentScale.Crop
        )

        // Padded content section
        Column(modifier = Modifier.padding(MaterialTheme.dimens.default)) {
            Text(
                title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = MaterialTheme.dimens.medium),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    price,
                    fontWeight = FontWeight.Bold,
                    fontSize = 14.sp,
                    color = Color(0xFF1976D2),
                    modifier = Modifier.padding(vertical = MaterialTheme.dimens.medium)
                )
                Row(
                    modifier = Modifier
                        .background(
                            color = Color(0xFFE8F0FE),
                            shape = RoundedCornerShape(MaterialTheme.dimens.default)
                        )
                        .padding(horizontal = MaterialTheme.dimens.default, vertical = MaterialTheme.dimens.medium),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        painter = painterResource(R.drawable.ic_hand_coin),
                        contentDescription = null,
                        tint = Color(0xFF1976D2),
                        modifier = Modifier.size(MaterialTheme.dimens.big)
                    )
                    Spacer(modifier = Modifier.width(MaterialTheme.dimens.medium))

                    Text(
                        text = stringResource(R.string.cash_on_delivery),
                        color = Color(0xFF1976D2),
                        fontSize = 14.sp
                    )
                }


            }

            Spacer(modifier = Modifier.height(MaterialTheme.dimens.default))
            Text(stringResource(R.string.delivery_info), fontWeight = FontWeight.SemiBold)

            Spacer(modifier = Modifier.height(MaterialTheme.dimens.regular))
            OrderRow(stringResource(R.string.delivery_price_label), price)
            OrderRow(
                stringResource(R.string.delivery_fee),
                stringResource(R.string.delivery_fee_value)
            )
            Spacer(modifier = Modifier.height(MaterialTheme.dimens.medium))
            OrderRow(
                stringResource(R.string.total),
                stringResource(R.string.common_price_with_currency, total),
                bold = true
            )

            Spacer(modifier = Modifier.height(MaterialTheme.dimens.bigger))

            Button(onClick = onConfirm, modifier = Modifier.fillMaxWidth()) {
                Text(stringResource(R.string.order))
            }
        }
    }
}

@Composable
fun OrderRow(label: String, value: String, bold: Boolean = false) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = MaterialTheme.dimens.small),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(label, fontWeight = if (bold) FontWeight.Bold else FontWeight.Normal)
        Text(value, fontWeight = if (bold) FontWeight.Bold else FontWeight.Normal)
    }
}


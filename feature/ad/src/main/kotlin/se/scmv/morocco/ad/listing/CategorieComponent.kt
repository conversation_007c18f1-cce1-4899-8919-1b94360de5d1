package se.scmv.morocco.ad.listing

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import coil.decode.SvgDecoder
import coil.request.ImageRequest
import se.scmv.morocco.designsystem.R
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.designsystem.utils.IconUrlOrDrawable

@Composable
fun HorizontalCategoryItems(
    lazyListState: LazyListState,
    items: List<PopularCategory>,
    canBackToParentCategories: Boolean,
    onBackClicked: () -> Unit,
    onCategoryItemClick: (PopularCategory) -> Unit
) {
    LazyRow(state = lazyListState) {
        if (canBackToParentCategories) {
            item {
                CategoryItem(
                    name = stringResource(R.string.common_back),
                    icon = IconUrlOrDrawable.Drawable(R.drawable.ic_arrow_back),
                    onClick = onBackClicked
                )
            }
        }
        items(items.filter { it.enabled }) { item ->
            CategoryItem(
                name = item.name,
                icon = IconUrlOrDrawable.Url(item.icon),
            ) {
                onCategoryItemClick(item)
            }
        }
    }
}

@Composable
fun CategoryItem(
    name: String,
    icon: IconUrlOrDrawable,
    onClick: () -> Unit
) {
    Column(
        modifier = Modifier
            .width(75.dp)
            .padding(horizontal = MaterialTheme.dimens.small),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            modifier = Modifier
                .size(50.dp)
                .clip(shape = CircleShape)
                .background(MaterialTheme.colorScheme.tertiaryContainer)
                .clickable {
                    onClick()
                },
            contentAlignment = Alignment.Center
        ) {
            when(icon) {
                is IconUrlOrDrawable.Url -> {
                    AsyncImage(
                        model = ImageRequest.Builder(LocalContext.current)
                            .data(icon.url)
                            .decoderFactory(SvgDecoder.Factory())
                            .build(),
                        contentDescription = null,
                        modifier = Modifier
                            .size(40.dp)
                            .clip(shape = CircleShape),
                        contentScale = ContentScale.Crop,
                    )
                }
                is IconUrlOrDrawable.Drawable -> {
                    Icon(
                        modifier = Modifier.size(MaterialTheme.dimens.big),
                        painter = painterResource(icon.id),
                        contentDescription = null
                    )
                }
            }
        }

        Text(
            text = name,
            color = MaterialTheme.colorScheme.onSurface,
            maxLines = 2,
            textAlign = TextAlign.Center,
            lineHeight = 13.sp,
            fontSize = 10.sp,
        )
    }
}
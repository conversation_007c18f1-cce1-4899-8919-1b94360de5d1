package se.scmv.morocco.ad.listing

import androidx.compose.runtime.Stable
import kotlinx.collections.immutable.PersistentList
import kotlinx.collections.immutable.persistentListOf
import se.scmv.morocco.ad.R
import se.scmv.morocco.designsystem.components.ChipData
import se.scmv.morocco.domain.models.ExtendedSearchType
import se.scmv.morocco.domain.models.ListingAd

@Stable
data class AdsListingViewState(
    val popularCategories: PersistentList<PopularCategory> = persistentListOf(),
    val childrenCategories: PersistentList<PopularCategory> = persistentListOf(),
    val canBackToParentCategories: Boolean = false,
    val filters: PersistentList<ChipData> = persistentListOf(),
    val extendedDelivery: Boolean = false,
)

@Stable
data class PopularCategory(
    val id: String,
    val name: String,
    val icon: String,
    val adTypeKey: String?,
    val enabled: Boolean = true
)

sealed interface AdsListingOneTimeEvents {
    data class NotifyCategoryChanged(
        val categoryId: String,
        val adTypeKey: String?
    ) : AdsListingOneTimeEvents

    data object NavigateToAuth : AdsListingOneTimeEvents

    data class ShowHideProgress(val isLoading: Boolean) : AdsListingOneTimeEvents
    data class ShowFirstConversationBtmSheet(val adId: String, val ad: ListingAd) : AdsListingOneTimeEvents
    data class ShowConversation(val conversationId: String) : AdsListingOneTimeEvents
}

fun ExtendedSearchType.toMessage(): Int = when (this) {
    ExtendedSearchType.TO_WHOLE_CITY -> R.string.search_extended_to_whole_city
    ExtendedSearchType.TO_NEARBY_CITIES -> R.string.search_extended_to_nearby_cities
    ExtendedSearchType.TO_WHOLE_COUNTRY -> R.string.search_extended_to_whole_country
    ExtendedSearchType.TO_BIGGER_PRICE_RANGE -> R.string.search_extended_to_bigger_price
}
package se.scmv.morocco.ad.insert

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.style.TextDecoration
import se.scmv.morocco.ad.R
import se.scmv.morocco.designsystem.theme.Black
import se.scmv.morocco.designsystem.theme.Blue50
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.info_center.AvWebViewActivity

@Composable
fun AdInsertHelpBottomSheet(
    modifier: Modifier = Modifier,
    title: String,
    content: String,
    termsAndConditionUrl: String,
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = MaterialTheme.dimens.screenPaddingHorizontal)
            .padding(bottom = MaterialTheme.dimens.large),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.large)
    ) {
        Text(
            text = title,
            style = MaterialTheme.typography.bodyLarge
        )
        Text(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    color = Blue50,
                    shape = MaterialTheme.shapes.small
                )
                .padding(MaterialTheme.dimens.large),
            text = content,
            color = Black,
            style = MaterialTheme.typography.labelMedium
        )
        val context = LocalContext.current
        Text(
            modifier = Modifier.clickable {
                AvWebViewActivity.open(
                    context = context,
                    title = context.getString(R.string.ad_insert_terms_and_conditions),
                    url = termsAndConditionUrl
                )
            },
            text = stringResource(R.string.ad_insert_terms_and_conditions),
            style = MaterialTheme.typography.labelLarge,
            textDecoration = TextDecoration.Underline,
            fontStyle = FontStyle.Italic
        )
    }
}
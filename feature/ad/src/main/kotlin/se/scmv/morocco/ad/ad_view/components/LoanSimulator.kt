package se.scmv.morocco.ad.ad_view.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowDropDown
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.Icon
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import se.scmv.morocco.ad.R
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.domain.models.LoanSimulatorConfig
import se.scmv.morocco.domain.models.LoanSimulatorInput
import se.scmv.morocco.domain.models.LoanSimulatorOutput
import se.scmv.morocco.domain.usecases.CalculateLoanUseCase
import java.text.NumberFormat
import java.util.Locale
import kotlin.math.roundToLong

@Composable
fun LoanSimulatorUI(
    config: LoanSimulatorConfig,
    adPrice: Double,
    calculateLoanUseCase: CalculateLoanUseCase,
    onPreApprovalClick: (String) -> Unit
) {
    var goodPriceState by remember {
        mutableStateOf(
            TextFieldValue(
                adPrice.roundToLong().toString()
            )
        )
    }
    var personalContributionAmountState by remember { mutableStateOf(TextFieldValue("0")) }
    var selectedDuration by remember { mutableStateOf(config.defaultDuration) }
    var expandedDurationDropdown by remember { mutableStateOf(false) }
    var loanOutput by remember { mutableStateOf(LoanSimulatorOutput(0, 0.0, 0, 0.0)) }
    var personalContributionError by remember { mutableStateOf<String?>(null) }

    val percentageFormatter = remember { NumberFormat.getPercentInstance(Locale.getDefault()) }

    val personalContributionLimitMessage = stringResource(id = R.string.personal_contribution_limit)

    fun updateLoanCalculation() {
        val goodPrice = goodPriceState.text.toDoubleOrNull() ?: 0.0
        val personalContributionPrice = personalContributionAmountState.text.toDoubleOrNull() ?: 0.0

        if (personalContributionPrice > goodPrice) {
            personalContributionError = personalContributionLimitMessage
            return
        } else {
            personalContributionError = null
        }

        val input = LoanSimulatorInput(
            goodPrice = goodPrice,
            personalContributionPrice = personalContributionPrice,
            loanDuration = selectedDuration
        )
        loanOutput = calculateLoanUseCase(input = input)
    }

    LaunchedEffect(goodPriceState.text, personalContributionAmountState.text, selectedDuration) {
        updateLoanCalculation()
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(horizontal =  MaterialTheme.dimens.default)
            .background(MaterialTheme.colorScheme.background)
    ) {
        Text(
            text = stringResource(R.string.loan_calculator),
            fontSize = 16.sp,
            fontWeight = FontWeight.SemiBold,
            modifier = Modifier.fillMaxWidth(),
            textAlign = TextAlign.Start,
            color = MaterialTheme.colorScheme.onBackground
        )

        Spacer(modifier = Modifier.height(MaterialTheme.dimens.regular))

        Column(modifier = Modifier.fillMaxWidth()) {
            LoanDetailCard(
                title = stringResource(R.string.monthly_payment),
                value = loanOutput.monthlyPayment.toString() + stringResource(R.string.currency_mad),
                modifier = Modifier.fillMaxWidth(),
                backgroundColor = Color(0xFFeaf0ff),
                titleColor = Color(0xFF1d72db)
            )
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceAround
            ) {
                LoanDetailCard(
                    title = stringResource(R.string.interest),
                    value = loanOutput.interestAmount.toString() + stringResource(R.string.currency_mad),
                    modifier = Modifier.weight(1f)
                )
                Spacer(modifier = Modifier.width(MaterialTheme.dimens.medium))
                LoanDetailCard(
                    title = stringResource(R.string.loan_amount),
                    value = loanOutput.loanAmount.roundToLong().toString() + stringResource(R.string.currency_mad),
                    modifier = Modifier.weight(1f),
                    titleColor = Color(0xFF1d72db)
                )
            }
        }

        Spacer(modifier = Modifier.height(MaterialTheme.dimens.regular))

        // Property Price
        Column(modifier = Modifier.fillMaxWidth()) {
            Text(
                stringResource(R.string.property_price),
                fontSize = 14.sp,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.onBackground
            )
            Spacer(modifier = Modifier.height(MaterialTheme.dimens.small))
            TextField(
                value = goodPriceState,
                onValueChange = { newValue ->
                    goodPriceState = newValue
                },
                trailingIcon = {
                    Text(stringResource(R.string.currency_mad), color = Color.LightGray, fontSize = 14.sp)
                },
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                modifier = Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
                    .border(1.dp, Color.LightGray, RoundedCornerShape(MaterialTheme.dimens.small)),
                colors = TextFieldDefaults.colors(
                    focusedTextColor = MaterialTheme.colorScheme.onBackground,
                    unfocusedTextColor = MaterialTheme.colorScheme.onBackground,
                    cursorColor = MaterialTheme.colorScheme.primary,
                    focusedIndicatorColor = Color.Transparent,
                    unfocusedIndicatorColor = Color.Transparent,
                    disabledIndicatorColor = Color.Transparent,
                    errorIndicatorColor = Color.Transparent,
                    unfocusedContainerColor = Color.Transparent,
                    focusedContainerColor = Color.Transparent,
                    disabledContainerColor = Color.Transparent,
                    errorContainerColor = Color.Transparent
                ),
                shape = RoundedCornerShape(MaterialTheme.dimens.small)
            )

        }


        Spacer(modifier = Modifier.height(MaterialTheme.dimens.regular))

        // Personal Contribution
        Column(modifier = Modifier.fillMaxWidth()) {
            Text(
                "Apport personnel",
                fontSize = 14.sp,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.onBackground
            )
            Spacer(modifier = Modifier.height(MaterialTheme.dimens.small))
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                TextField(
                    value = personalContributionAmountState,
                    onValueChange = { newValue ->
                        personalContributionAmountState = newValue
                    },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    modifier = Modifier
                        .weight(1f)
                        .wrapContentHeight()
                        .border(1.dp, Color.LightGray, RoundedCornerShape(MaterialTheme.dimens.small)),
                    colors = TextFieldDefaults.colors(
                        focusedTextColor = MaterialTheme.colorScheme.onBackground,
                        unfocusedTextColor = MaterialTheme.colorScheme.onBackground,
                        cursorColor = MaterialTheme.colorScheme.primary,
                        focusedIndicatorColor = Color.Transparent,
                        unfocusedIndicatorColor = Color.Transparent,
                        disabledIndicatorColor = Color.Transparent,
                        errorIndicatorColor = Color.Transparent,
                        unfocusedContainerColor = Color.Transparent,
                        focusedContainerColor = Color.Transparent,
                        disabledContainerColor = Color.Transparent,
                        errorContainerColor = Color.Transparent
                    ),
                    trailingIcon = {
                        Text(stringResource(R.string.currency_mad), color = Color.LightGray, fontSize = 14.sp)
                    },
                    shape = RoundedCornerShape(MaterialTheme.dimens.small),
                    textStyle = LocalTextStyle.current.copy(fontSize = 16.sp),
                    placeholder = {
                        Text(stringResource(R.string.amount), color = MaterialTheme.colorScheme.onSurfaceVariant)
                    }
                )

                Spacer(modifier = Modifier.width(MaterialTheme.dimens.regular))

                Box(
                    modifier = Modifier
                        .width(82.dp)
                        .align(Alignment.CenterVertically),
                    contentAlignment = Alignment.CenterEnd
                ) {
                    Text(
                        percentageFormatter.format(loanOutput.personalContributionPercentage / 100),
                        color = MaterialTheme.colorScheme.onBackground,
                        modifier = Modifier.padding(end = 22.dp)
                    )
                }


            }
            if (personalContributionError != null) {
                Text(
                    text = personalContributionError!!,
                    color = MaterialTheme.colorScheme.error,
                    style = MaterialTheme.typography.bodySmall,
                    modifier = Modifier.padding(start = MaterialTheme.dimens.default)
                )
            }
        }

        Spacer(modifier = Modifier.height(MaterialTheme.dimens.regular))

        // Loan Duration and Interest
        Column(modifier = Modifier.fillMaxWidth()) {
            Text(
                stringResource(R.string.loan_duration),
                fontSize = 14.sp,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.onBackground
            )
            Spacer(modifier = Modifier.height(MaterialTheme.dimens.small))
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                Box(
                    modifier = Modifier
                        .weight(1f)
                        .clickable {
                            expandedDurationDropdown = !expandedDurationDropdown
                        }
                        .border(1.dp, Color.LightGray)
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceBetween,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(
                                vertical = MaterialTheme.dimens.regular,
                                horizontal = MaterialTheme.dimens.default
                            ) // Add padding for touch area
                    ) {
                        Text(
                            text = "$selectedDuration ${stringResource(R.string.years)}",
                            color = MaterialTheme.colorScheme.onBackground
                        )
                        Icon(
                            Icons.Default.ArrowDropDown,
                            contentDescription = "Dropdown",
                            tint = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
                Spacer(modifier = Modifier.width(MaterialTheme.dimens.medium))

                Box(
                    modifier = Modifier
                        .width(82.dp)
                        .align(Alignment.CenterVertically),
                    contentAlignment = Alignment.CenterEnd
                ) {
                    Text(
                        "${config.interestPercentage}%",
                        color = MaterialTheme.colorScheme.onBackground,
                        modifier = Modifier.padding(end = 22.dp)
                    )
                }
            }
            DropdownMenu(
                expanded = expandedDurationDropdown,
                onDismissRequest = { expandedDurationDropdown = false }
            ) {
                config.loanDurations.forEach { duration ->
                    DropdownMenuItem(
                        onClick = {
                            selectedDuration = duration
                            expandedDurationDropdown = false
                        },
                        text = {
                            Text(
                                "$duration ${stringResource(R.string.years)}",
                                color = MaterialTheme.colorScheme.onBackground
                            )
                        }
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(MaterialTheme.dimens.default))

        // Disclaimer
        Text(
            text = stringResource(R.string.simulation_disclaimer),
            fontSize = 12.sp,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Spacer(modifier = Modifier.height(MaterialTheme.dimens.regular))

        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    stringResource(R.string.powered_by),
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                )
                Image(
                    painter = painterResource(R.drawable.loan_simulator_avito_logo),
                    contentDescription = ""
                )

            }

            Button(
                onClick = { onPreApprovalClick(config.redirectionUrl) },
                enabled = personalContributionError == null,
                shape = RoundedCornerShape(MaterialTheme.dimens.medium),
                colors = ButtonDefaults.buttonColors(containerColor = MaterialTheme.colorScheme.primary)
            ) {
                Text(stringResource(R.string.get_preapproval), color = MaterialTheme.colorScheme.onPrimary)
            }
        }


    }
}

@Composable
fun LoanDetailCard(
    title: String,
    value: String,
    modifier: Modifier = Modifier,
    backgroundColor: Color = CardDefaults.cardColors().containerColor,
    titleColor: Color = MaterialTheme.colorScheme.onSurfaceVariant

) {
    Card(
        modifier = modifier
            .padding(vertical = MaterialTheme.dimens.small),
        shape = RoundedCornerShape(MaterialTheme.dimens.medium),
        colors = CardDefaults.cardColors(containerColor = backgroundColor),
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = MaterialTheme.dimens.small),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = value,
                fontWeight = FontWeight.Bold,
                fontSize = 16.sp,
                textAlign = TextAlign.Center,
                color = titleColor,
                modifier = Modifier.fillMaxWidth()
            )
            Text(
                text = title,
                fontSize = 12.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun PreviewLoanSimulatorUI() {
    val sampleConfig = LoanSimulatorConfig(
        loanDurations = listOf(10, 15, 20, 25, 30),
        defaultDuration = 25,
        interestPercentage = 4.2,
        redirectionUrl = "https://example.com/pre-approval"
    )
    val sampleUseCase = CalculateLoanUseCase(sampleConfig)
    LoanSimulatorUI(
        config = sampleConfig,
        adPrice = 1900000.0,
        calculateLoanUseCase = sampleUseCase,
        onPreApprovalClick = {}
    )
}

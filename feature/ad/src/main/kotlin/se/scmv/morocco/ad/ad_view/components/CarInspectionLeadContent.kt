package se.scmv.morocco.ad.ad_view.components

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowDropDown
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Checkbox
import androidx.compose.material3.CheckboxDefaults
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlinx.coroutines.delay
import se.scmv.morocco.ad.R
import se.scmv.morocco.common.lang.LocaleManager
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.domain.models.CarCheckConfig

@Composable
fun CarInspectionLeadContent(
    carCheckConfig: CarCheckConfig,
    onDismissRequest: () -> Unit = {},
    onTermsClicked: (url: String) -> Unit = {},
    onSendLead: (String, String, Boolean, Boolean) -> Unit = { _, _, _, _ -> },
    showCarInspectionSent: Boolean
) {
    val lang = LocaleManager.getCurrentLanguage()
    var name by remember { mutableStateOf("") }
    var phoneNumber by remember { mutableStateOf("") }
    var checkbox1 by remember { mutableStateOf(true) }
    var checkbox2 by remember { mutableStateOf(true) }
    val selectedCountry = remember { mutableStateOf<Country?>(null) }
    val phoneErrorLength = stringResource(id = R.string.phone_invalid_length)
    val phoneErrorStart = stringResource(id = R.string.phone_should_start_with_0)

    val localeData = carCheckConfig.carToCheck.inspectionRequestForm.locales[lang]
    var phoneNumberError by remember { mutableStateOf<String?>(null) }
    val urlTerms = stringResource(id = R.string.url_term_of_services)

    val isFormValid = name.isNotBlank()
            && phoneNumber.length == 10
            && phoneNumber.startsWith("0")
            && phoneNumberError == null
            && checkbox1

    Column(
        modifier = Modifier
            .padding(horizontal = MaterialTheme.dimens.default)
            .padding(bottom = 26.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = localeData?.title.toString(),
                fontWeight = FontWeight.SemiBold,
                fontSize = 16.sp
            )
            IconButton(onClick = onDismissRequest) {
                Icon(imageVector = Icons.Default.Close, contentDescription = "Close")
            }
        }

        Row(verticalAlignment = Alignment.CenterVertically) {
            Image(
                painter = painterResource(id = R.drawable.ic_park_local),
                contentDescription = "Location",
                modifier = Modifier.size(MaterialTheme.dimens.bigger)
            )
            Spacer(modifier = Modifier.width(MaterialTheme.dimens.small))
            Text(
                text = localeData?.serviceAvailableCities.toString(),
                fontSize = 14.sp,
                fontWeight = FontWeight.SemiBold
            )
        }

        OutlinedTextField(
            value = name,
            onValueChange = { name = it },
            label = { Text(stringResource(R.string.your_name), color = Color(0xFF58606B)) },
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = MaterialTheme.dimens.default)
        )

        OutlinedTextField(
            value = phoneNumber,
            onValueChange = { input ->
                // Only allow digits, you can also add more validation here
                if (input.all { it.isDigit() }) {
                    phoneNumber = input
                }

                phoneNumberError = when {
                    input.length != 10 -> phoneErrorLength
                    !input.startsWith("0") -> phoneErrorStart
                    else -> null
                }

            },
            label = { Text(stringResource(R.string.your_phone), color = Color(0xFF58606B)) },
            leadingIcon = {
                CountryField(
                    modifier = Modifier.padding(start = MaterialTheme.dimens.medium),
                    country = selectedCountry.value,
                    onCountrySelected = { selectedCountry.value = it }
                )
            },
            keyboardOptions = KeyboardOptions.Default.copy(keyboardType = KeyboardType.Phone),
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = MaterialTheme.dimens.medium),
            supportingText = phoneNumberError?.let {
                {
                    Text(
                        text = it,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.error
                    )
                }
            },
            isError = phoneNumberError != null
        )



        Row(
            modifier = Modifier
                .padding(top = MaterialTheme.dimens.default, start = MaterialTheme.dimens.none),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = checkbox1,
                modifier = Modifier
                    .size(MaterialTheme.dimens.bigger)
                    .padding(MaterialTheme.dimens.none),
                onCheckedChange = { checkbox1 = it },
                colors = CheckboxDefaults.colors(checkedColor = Color(0xFFE57373))
            )
            Text(
                text = stringResource(R.string.i_accept_terms),
                modifier = Modifier.padding(start = MaterialTheme.dimens.medium),
                fontSize = 14.sp
            )
            Text(
                text = stringResource(R.string.terms_and_conditions),
                fontSize = 14.sp,
                textDecoration = TextDecoration.Underline,
                modifier = Modifier.clickable {
                    onTermsClicked(urlTerms)
                }
            )
        }

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = MaterialTheme.dimens.medium),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = checkbox2,
                modifier = Modifier
                    .size(MaterialTheme.dimens.bigger)
                    .padding(
                        horizontal = MaterialTheme.dimens.none,
                        vertical = MaterialTheme.dimens.medium
                    ),
                onCheckedChange = { checkbox2 = it },
                colors = CheckboxDefaults.colors(checkedColor = Color(0xFFE57373))
            )
            Text(
                text = stringResource(R.string.notify_me_updates),
                modifier = Modifier.padding(start = MaterialTheme.dimens.medium),
                fontSize = 14.sp
            )
        }

        if (showCarInspectionSent) {
            Card(
                colors = CardDefaults.cardColors(containerColor = Color(0xFFE8F5E9)), // Light green background
                shape = RoundedCornerShape(MaterialTheme.dimens.medium),
                elevation = CardDefaults.cardElevation(defaultElevation = MaterialTheme.dimens.none),
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = MaterialTheme.dimens.default)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.padding(MaterialTheme.dimens.default)
                ) {
                    Image(
                        painter = painterResource(R.drawable.ic_success),
                        contentDescription = "Check Icon",
                        modifier = Modifier.size(32.dp)
                    )

                    Spacer(modifier = Modifier.width(MaterialTheme.dimens.medium))
                    Text(
                        text = stringResource(R.string.lead_sent),
                        color = Color(0xFF2E7D32),
                        fontSize = 14.sp
                    )
                }
            }

            LaunchedEffect(showCarInspectionSent) {
                if (showCarInspectionSent) {
                    delay(1500) // Wait 1.5 seconds
                    onDismissRequest() // Dismiss bottom sheet
                }
            }

        } else {
            Button(
                enabled = isFormValid,
                onClick = {
                    val cleanedPhoneNumber = phoneNumber.removePrefix("0")
                    val fullPhoneNumber =
                        "${selectedCountry.value?.phonePrefix ?: "+212"}$cleanedPhoneNumber"
                    onSendLead(name, fullPhoneNumber, checkbox1, checkbox2)

                },

                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = MaterialTheme.dimens.default),
                colors = ButtonDefaults.buttonColors(containerColor = Color(0xFFE57373))
            ) {

                Text(text = localeData?.ctaButton?.text.toString(), color = Color.White)
            }
        }
    }
}


data class Country(
    val flag: String,
    val displayName: String,
    val phonePrefix: String
)


@Composable
fun CountryField(
    modifier: Modifier = Modifier,
    country: Country? = null,
    onCountrySelected: (Country) -> Unit
) {
    val context = LocalContext.current

    val countriesList = remember {
        context.resources.getStringArray(R.array.country_list)
            .mapNotNull { item ->
                val parts = item.split("|")
                if (parts.size == 3) {
                    val flag = parts[0].trim()
                    val displayName = parts[1].trim()
                    val phonePrefix = parts[2].trim()
                    Country(
                        flag = flag,
                        displayName = displayName,
                        phonePrefix = phonePrefix
                    )
                } else {
                    null
                }
            }
    }

    var expanded by remember { mutableStateOf(false) }

    val defaultCountry = remember {
        country ?: countriesList.firstOrNull()
    }

    var selectedCountry by remember { mutableStateOf(defaultCountry) }

    Box(modifier = modifier.wrapContentSize()) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .clickable { expanded = true }
                .padding(MaterialTheme.dimens.medium)
        ) {
            Text(
                text = selectedCountry?.flag.orEmpty(),
                fontSize = 14.sp
            )
            Spacer(modifier = Modifier.width(MaterialTheme.dimens.small))
            Icon(
                imageVector = Icons.Default.ArrowDropDown,
                contentDescription = null
            )
        }

        DropdownMenu(
            expanded = expanded,
            onDismissRequest = { expanded = false }
        ) {
            countriesList.forEach { countryItem ->
                DropdownMenuItem(
                    onClick = {
                        selectedCountry = countryItem
                        onCountrySelected(countryItem)
                        expanded = false
                    },
                    text = {
                        Row(verticalAlignment = Alignment.CenterVertically) {
                            Text(text = countryItem.flag, fontSize = 16.sp)
                            Spacer(modifier = Modifier.width(MaterialTheme.dimens.medium))
                            Text(text = countryItem.displayName, fontSize = 14.sp)
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(text = "(${countryItem.phonePrefix})", fontSize = 14.sp)
                        }
                    }
                )
            }
        }
    }
}


@OptIn(ExperimentalLayoutApi::class)
@Composable
fun CarInspectedScreen(
    onOpenOrDownloadPdf: (download: Boolean) -> Unit = {},
    carCheckConfig: CarCheckConfig,
    reportTime: String,
    modifier: Modifier = Modifier.padding(horizontal = MaterialTheme.dimens.default)
) {
    val lang = LocaleManager.getCurrentLanguage()
    val carChecked = carCheckConfig.carChecked
    val localeContent = carChecked.locales?.get(lang)

    Surface(
        modifier = modifier
            .fillMaxWidth()
            .padding(top = MaterialTheme.dimens.default),
        shape = RoundedCornerShape(MaterialTheme.dimens.betweenSmallMedium),
        border = BorderStroke(0.8.dp, Color.LightGray),
        color = MaterialTheme.colorScheme.surface
    ) {
        Column {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = MaterialTheme.dimens.default)
                    .padding(top = MaterialTheme.dimens.default),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = localeContent?.title.toString(),
                    fontSize = 14.sp,
                    fontWeight = FontWeight.SemiBold
                )
            }

            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = MaterialTheme.dimens.default)
                    .padding(top = MaterialTheme.dimens.betweenSmallMedium),
            ) {
                Text(
                    text = localeContent?.dateLabel.toString(),
                    fontSize = 12.sp
                )

                Text(
                    text = reportTime,
                    color = Color(0xFF58606B),
                    fontSize = 12.sp,
                    modifier = Modifier.padding(start = MaterialTheme.dimens.tiny)
                )
            }


            Image(
                painter = painterResource(id = R.drawable.ic_expertisee_avito),
                contentDescription = "Expertise Logo",
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(MaterialTheme.dimens.default)
            )

            FlowRow(
                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium),
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium),
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = MaterialTheme.dimens.default)
                    .padding(bottom = MaterialTheme.dimens.large)
            ) {
                localeContent?.tags?.forEach { tag ->
                    Text(
                        text = tag,
                        color = Color(0xFF212b36),
                        modifier = Modifier
                            .clip(RoundedCornerShape(MaterialTheme.dimens.betweenSmallMedium))
                            .background(Color(0xFFf2f2f2))
                            .padding(
                                horizontal = MaterialTheme.dimens.betweenSmallMedium,
                                vertical = 3.dp
                            ),
                        fontSize = 11.sp
                    )
                }
            }

            Box(
                modifier = Modifier.fillMaxWidth(),
                contentAlignment = Alignment.BottomCenter
            ) {
                Image(
                    painter = painterResource(id = R.drawable.car_wave_ic),
                    contentDescription = "Background",
                    contentScale = ContentScale.FillWidth,
                    modifier = Modifier.fillMaxWidth()
                )

                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = MaterialTheme.dimens.medium),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth(0.7f)
                            .padding(bottom = MaterialTheme.dimens.medium)
                            .clip(RoundedCornerShape(MaterialTheme.dimens.bigger))
                            .background(Color(0xFFFFEDEE))
                            .border(
                                BorderStroke(1.dp, Color(0xFFFF4C59)),
                                RoundedCornerShape(MaterialTheme.dimens.bigger)
                            )
                            .clickable { onOpenOrDownloadPdf(false) }
                            .padding(MaterialTheme.dimens.medium),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = localeContent?.reportButtonText.toString(),
                            color = Color(0xFFFF4C59),
                            fontWeight = FontWeight.Bold,
                            fontSize = 14.sp,
                            textAlign = TextAlign.Center
                        )
                    }

                    Box(
                        modifier = Modifier
                            .fillMaxWidth(0.7f)
                            .padding(bottom = 30.dp)
                            .clip(RoundedCornerShape(MaterialTheme.dimens.bigger))
                            .background(Color(0xFFFF4C59))
                            .clickable { onOpenOrDownloadPdf(true) }
                            .padding(MaterialTheme.dimens.medium),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = localeContent?.downloadButtonText.toString(),
                            color = Color.White,
                            fontWeight = FontWeight.Bold,
                            fontSize = 14.sp,
                            textAlign = TextAlign.Center
                        )
                    }
                }
            }
        }
    }
}


@OptIn(ExperimentalLayoutApi::class)
@Composable
fun CarInspectionScreen(
    onInspectClicked: () -> Unit = {},
    carCheckConfig: CarCheckConfig,
    modifier: Modifier = Modifier
        .padding(horizontal = MaterialTheme.dimens.default)

) {
    val lang = LocaleManager.getCurrentLanguage()
    val carToCheck = carCheckConfig.carToCheck
    val localeContent = carToCheck?.locales?.get(lang)

    Surface(
        modifier = modifier
            .fillMaxWidth()
            .padding(top = MaterialTheme.dimens.default),
        shape = RoundedCornerShape(MaterialTheme.dimens.betweenSmallMedium),
        border = BorderStroke(0.8.dp, Color.LightGray),
        color = MaterialTheme.colorScheme.surface
    ) {
        Column {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = MaterialTheme.dimens.default)
                    .padding(top = MaterialTheme.dimens.default),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = localeContent?.title.toString(),
                    fontSize = 14.sp,
                    fontWeight = FontWeight.SemiBold
                )
            }

            Text(
                text = localeContent?.description.toString(),
                color = Color(0xFF58606B),
                fontSize = 12.sp,
                modifier = Modifier.padding(
                    top = MaterialTheme.dimens.medium,
                    start = MaterialTheme.dimens.default,
                    end = MaterialTheme.dimens.default,
                    bottom = MaterialTheme.dimens.medium
                )
            )

            FlowRow(
                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium),
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium),
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = MaterialTheme.dimens.default)
            ) {
                localeContent?.tags?.forEach { tag ->
                    Text(
                        text = tag,
                        color = Color(0xFF212b36),
                        modifier = Modifier
                            .clip(RoundedCornerShape(MaterialTheme.dimens.betweenSmallMedium))
                            .background(Color(0xFFf2f2f2))
                            .padding(
                                horizontal = MaterialTheme.dimens.betweenSmallMedium,
                                vertical = 3.dp
                            ),
                        fontSize = 11.sp
                    )
                }
            }

            Box(
                modifier = Modifier.fillMaxWidth(),
                contentAlignment = Alignment.BottomCenter
            ) {
                Image(
                    painter = painterResource(id = R.drawable.car_wave_ic),
                    contentDescription = "Background",
                    contentScale = ContentScale.FillWidth,
                    modifier = Modifier.fillMaxWidth()
                )

                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 120.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Box(
                        contentAlignment = Alignment.Center
                    ) {
                        Image(
                            painter = painterResource(id = R.drawable.car_check),
                            contentDescription = "Car",
                            modifier = Modifier.fillMaxWidth(0.6f)
                        )
                    }
                }

                Box(
                    modifier = Modifier
                        .fillMaxWidth(0.7f)
                        .padding(bottom = 30.dp)
                        .clip(RoundedCornerShape(MaterialTheme.dimens.bigger))
                        .background(Color(0xFFFF4C59))
                        .clickable { onInspectClicked() }
                        .padding(MaterialTheme.dimens.medium),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = localeContent?.buttonText.toString(),
                        color = Color.White,
                        fontWeight = FontWeight.Bold,
                        fontSize = 14.sp,
                        textAlign = TextAlign.Center
                    )
                }
            }
        }
    }
}


//@Preview(showBackground = true)
//@Composable
//fun CarInspectionScreenPreview() {
//    MaterialTheme {
//        CarInspectionScreen(
//            onInspectClicked = {},
//            null
//        )
//    }
//}
//
//@Preview(showBackground = true)
//@Composable
//fun CarInspectedScreenPreview() {
//    MaterialTheme {
//        CarInspectedScreen(
//            onOpenPdf = {}
//        )
//    }
//}
package se.scmv.morocco.ad.vas.steps.bankapps

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.SheetState
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import coil.compose.AsyncImage
import coil.decode.SvgDecoder
import coil.request.ImageRequest
import se.scmv.morocco.ad.R
import se.scmv.morocco.common.lang.LocaleManager
import se.scmv.morocco.designsystem.components.AvPrimaryButton
import se.scmv.morocco.designsystem.theme.Black
import se.scmv.morocco.designsystem.theme.Blue50
import se.scmv.morocco.designsystem.theme.dimens

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SupportedBankAppsBtmSheet(
    modifier: Modifier = Modifier,
    viewModel: SupportedBankAppsViewModel = hiltViewModel(),
    sheetState: SheetState,
    onDismiss: () -> Unit
) {
    val state = viewModel.bankApps.collectAsStateWithLifecycle().value
    ModalBottomSheet(
        sheetState = sheetState,
        onDismissRequest = onDismiss
    ) {
        Column(
            modifier = modifier
                .fillMaxWidth()
                .padding(MaterialTheme.dimens.screenPaddingHorizontal),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.big),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Image(
                modifier = Modifier.size(120.dp),
                painter = painterResource(R.drawable.img_cash_payment_banking_app_provider_step1_icon),
                contentDescription = null,
                contentScale = ContentScale.Crop
            )

            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small),
            ) {
                Text(
                    text = stringResource(R.string.payment_method_bank_app_title),
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = stringResource(R.string.payment_method_bank_app_description),
                    style = MaterialTheme.typography.bodyMedium,
                )
            }

            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Blue50, MaterialTheme.shapes.large)
                    .padding(horizontal = MaterialTheme.dimens.default)
                    .padding(vertical = MaterialTheme.dimens.large),
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium),
            ) {
                state.forEach {
                    BankApp(
                        imageUrl = it.iconUrl,
                        name = if (LocaleManager.isAr()) it.arLabel else it.frLabel
                    )
                }
            }

            AvPrimaryButton(
                modifier = Modifier.fillMaxWidth(),
                text = stringResource(R.string.common_understood),
                onClick = onDismiss
            )
        }
    }
}

@Composable
fun BankApp(imageUrl: String, name: String) {
    Row(
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium),
        verticalAlignment = Alignment.CenterVertically
    ) {
        AsyncImage(
            modifier = Modifier.size(40.dp),
            model = ImageRequest.Builder(LocalContext.current)
                .data(imageUrl)
                .decoderFactory(SvgDecoder.Factory())
                .build(),
            contentDescription = null,
            contentScale = ContentScale.FillBounds,
            placeholder = painterResource(R.drawable.img_no_image_placeholder),
            error = painterResource(R.drawable.img_no_image_placeholder),
        )
        Text(text = name, style = MaterialTheme.typography.bodyLarge, color = Black)
    }
}
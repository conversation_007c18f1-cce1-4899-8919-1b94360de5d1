package se.scmv.morocco.info.presentation

import android.content.Context
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.core.content.pm.PackageInfoCompat
import se.scmv.morocco.designsystem.R
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.dimens

@Composable
fun AboutRoute(
    modifier: Modifier = Modifier,
    navigateBack: () -> Unit
) {
    val context = LocalContext.current
    val appVersionInfo = remember {
        getAppVersionInfo(context)
    }
    Box(modifier = modifier.fillMaxSize()) {
        IconButton(
            modifier = Modifier.align(Alignment.TopEnd),
            onClick = navigateBack
        ) {
            Icon(
                imageVector = Icons.Filled.Close,
                contentDescription = "Close button"
            )
        }
        Image(
            modifier = Modifier.fillMaxSize(),
            painter = painterResource(id = R.drawable.splash_screen_background),
            contentDescription = null
        )
        Column(
            modifier = Modifier
                .align(Alignment.Center),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Image(
                modifier = Modifier.size(140.dp),
                painter = painterResource(id = R.drawable.splash_screen_main_logo),
                contentDescription = null
            )
            Text(
                modifier = Modifier,
                text = stringResource(
                    R.string.appversion,
                    appVersionInfo.first,
                    appVersionInfo.second.toString()
                ),
                color = MaterialTheme.colorScheme.onSurface,
            )
        }
        Column(
            modifier = Modifier
                .padding(MaterialTheme.dimens.medium)
                .align(Alignment.BottomCenter),
        ) {
            Text(
                text = stringResource(R.string.copyright),
                color = MaterialTheme.colorScheme.onSurface
            )
            Text(
                text = stringResource(R.string.rights_reserved),
                color = MaterialTheme.colorScheme.onSurface
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun AboutScreenContentPreview() {
    AvitoTheme {
        AboutRoute(navigateBack = {})
    }
}

fun getAppVersionInfo(context: Context): Pair<String, Int> {
    return try {
        val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
        val versionName = packageInfo.versionName ?: "Unknown"
        val versionCode = PackageInfoCompat.getLongVersionCode(packageInfo).toInt()
        versionName to versionCode
    } catch (e: Exception) {
        "Unknown" to -1 // Fallback if any error occurs
    }
}
package se.scmv.morocco.info.navigation

import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavHostController
import androidx.navigation.navigation
import kotlinx.serialization.Serializable
import se.scmv.morocco.info.presentation.AboutRoute
import se.scmv.morocco.info.presentation.InfoScreen
import se.scmv.morocco.ui.composableWithAnimation

@Serializable
data object InfoNavGraph

@Serializable
data object InfoRoute

@Serializable
data object AboutRoute

fun NavGraphBuilder.infoNavGraph(
    navController: NavHostController,
    navigateToWebView: (title: String, url: String) -> Unit,
) {
    navigation<InfoNavGraph>(startDestination = InfoRoute) {
        composableWithAnimation<InfoRoute> {
            InfoScreen(
                onBackPress = { navController.navigateUp() },
                navigateToAbout = { navController.navigate(AboutRoute) },
                navigateToWebView = navigateToWebView
            )
        }
        composableWithAnimation<AboutRoute> {
            AboutRoute(
                navigateBack = { navController.navigateUp() }
            )
        }
    }
}
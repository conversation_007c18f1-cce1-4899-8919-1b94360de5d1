package se.scmv.morocco.info_center

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.ViewGroup
import android.webkit.WebView
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.systemBars
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.viewinterop.AndroidView
import se.scmv.morocco.designsystem.theme.AvitoTheme

/**
 * Activity that hosts a [WebView] to load and display web content within the app.
 *
 * This activity provides a customizable top app bar with an optional title and a back button,
 * along with edge-to-edge layout support.
 */
class AvWebViewActivity : ComponentActivity() {

    companion object {
        const val TITLE_KEY = "key_title"
        const val URL_KEY = "key_url"

        /**
         * Opens the [AvWebViewActivity] with the given title and URL.
         *
         * @param context The context from which the activity is started.
         * @param title The title to display in the app bar. Can be null if no title is needed.
         * @param url The URL to be loaded in the [WebView].
         */
        fun open(
            context: Context,
            url: String,
            title: String? = null
        ) {
            context.startActivity(
                Intent(context, AvWebViewActivity::class.java).apply {
                    putExtra(TITLE_KEY, title)
                    putExtra(URL_KEY, url)
                }
            )
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val title: String? = intent.getStringExtra(TITLE_KEY)
        val url: String? = intent.getStringExtra(URL_KEY)

        enableEdgeToEdge()
        setContent {
            AvitoTheme {
                Surface(
                    modifier = Modifier
                        .windowInsetsPadding(WindowInsets.statusBars)
                        .windowInsetsPadding(WindowInsets.systemBars)
                ) {
                    Scaffold(
                        topBar = {
                            TopAppBar(
                                title = title,
                                onBackClicked = { onBackPressedDispatcher.onBackPressed() }
                            )
                        }
                    ) { padding ->
                        AndroidView(
                            factory = { context ->
                                WebView(context).apply { init() }
                            },
                            modifier = Modifier.padding(padding),
                            update = { webView ->
                                url?.let { webView.loadUrl(it) }
                            }
                        )
                    }
                }
            }
        }
    }

    @OptIn(ExperimentalMaterial3Api::class)
    @Composable
    private fun TopAppBar(
        title: String?,
        onBackClicked: () -> Unit
    ) {
        TopAppBar(
            title = {
                title?.let { Text(text = it, style = MaterialTheme.typography.titleMedium) }
            },
            navigationIcon = {
                IconButton(onClick = onBackClicked) {
                    Icon(
                        imageVector = Icons.AutoMirrored.Default.ArrowBack,
                        contentDescription = null
                    )
                }
            }
        )
    }

    /**
     * Initializes the [WebView] with the necessary settings for loading web content.
     *
     * This includes enabling JavaScript, DOM storage, and allowing automatic opening of new windows.
     * You can add other settings if needed!
     */
    private fun WebView.init() {
        layoutParams = ViewGroup.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        )
        settings.javaScriptEnabled = true
        settings.domStorageEnabled = true
        settings.javaScriptCanOpenWindowsAutomatically = true
    }
}
package se.scmv.morocco.authentication.domain.validators

import org.junit.Test
import kotlin.test.assertFalse
import kotlin.test.assertTrue

class CredentialsValidatorTest {

    private val validator = CredentialsValidator()

    @Test
    fun validateEmail() {
        // Valid email tests
        assertTrue(validator.validateEmail("<EMAIL>"))
        assertTrue(validator.validateEmail("<EMAIL>"))
        assertTrue(validator.validateEmail("<EMAIL>"))
        assertTrue(validator.validateEmail("<EMAIL>"))
        assertTrue(validator.validateEmail("<EMAIL>"))
        assertTrue(validator.validateEmail("<EMAIL>"))

        // Invalid email tests
        assertFalse(validator.validateEmail("example.com"))                       // Missing @ symbol
        assertFalse(validator.validateEmail("user@@example.com"))                // Multiple @ symbols
        assertFalse(validator.validateEmail("user@example@com"))                 // Invalid domain format
        assertFalse(validator.validateEmail("user@exam!ple.com"))                // Invalid characters
        assertFalse(validator.validateEmail(""))                                 // Empty email
        assertFalse(validator.validateEmail("user@ example.com"))                // Space in email
        assertFalse(validator.validateEmail("  <EMAIL>"))               // Leading space
        assertFalse(validator.validateEmail("<EMAIL>  "))               // Trailing space
        assertFalse(validator.validateEmail("üñîçødë@example.com"))              // Unicode characters
        assertFalse(validator.validateEmail("user@.com"))                        // Missing domain
        assertFalse(validator.validateEmail("<EMAIL>."))
    }

    @Test
    fun validatePhone() {
        // Valid phone number tests
        assertTrue(validator.validatePhone("0512345678")) // Example valid 10-digit phone number with prefix "05"
        assertTrue(validator.validatePhone("0612345678")) // Valid 10-digit phone number with prefix "06"
        assertTrue(validator.validatePhone("0712345678")) // Valid 10-digit phone number with prefix "07"
        assertTrue(validator.validatePhone("0812345678")) // Valid 10-digit phone number with prefix "08"
        assertTrue(validator.validatePhone("01234567891")) // Valid more than 10 digits phone number starting with "0"
        assertTrue(validator.validatePhone("0123456789012")) // Valid more than 10 digits phone number starting with "0"

        // Invalid phone number tests
        assertFalse(validator.validatePhone("123456789"))   // Less than 10 digits
        assertFalse(validator.validatePhone("051234567"))   // Less than 10 digits
        assertFalse(validator.validatePhone("15123456789")) // More than 10 digits but incorrect prefix
        assertFalse(validator.validatePhone("1512345678"))  // Correct length but invalid prefix
        assertFalse(validator.validatePhone("abc1234567"))  // Contains non-digit characters
        assertFalse(validator.validatePhone(""))             // Empty phone number
        assertFalse(validator.validatePhone("0512 345 678"))// Contains spaces
        assertFalse(validator.validatePhone("0512345678a")) // Contains non-digit characters
    }

    @Test
    fun validatePassword() {
        // This is not over-engineering, read the validatePassword docs to understand.

        // Valid passwords
        assertTrue(validator.validatePassword("0512345678"))

        // Invalid passwords
        assertFalse(validator.validatePassword(""))
        assertFalse(validator.validatePassword("  "))
    }
}
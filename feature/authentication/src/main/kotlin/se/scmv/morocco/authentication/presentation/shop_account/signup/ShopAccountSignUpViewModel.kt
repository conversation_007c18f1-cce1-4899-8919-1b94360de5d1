package se.scmv.morocco.authentication.presentation.shop_account.signup

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.collections.immutable.toImmutableList
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import se.scmv.morocco.authentication.R
import se.scmv.morocco.authentication.domain.validators.CredentialsValidator
import se.scmv.morocco.designsystem.components.ChipData
import se.scmv.morocco.designsystem.components.ChipStateHandler
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.designsystem.utils.localizedName
import se.scmv.morocco.domain.models.NetworkAndBackendErrors
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.models.ShopCategory
import se.scmv.morocco.domain.models.ShopSubscription
import se.scmv.morocco.domain.repositories.AuthenticationRepository
import se.scmv.morocco.ui.renderFailure
import java.util.Locale
import javax.inject.Inject

@HiltViewModel
class ShopAccountSignUpViewModel @Inject constructor(
    private val credentialsValidator: CredentialsValidator,
    private val authenticationRepository: AuthenticationRepository,
    @ApplicationContext private val context: Context
) : ViewModel() {

    private val _viewState = MutableStateFlow(
        ShopAccountSignUpViewState(
            shopCategories = ShopCategory.entries.map {
                ChipData(
                    id = it.name,
                    name = context.getString(it.localizedName()),
                    selected = false
                )
            }.toPersistentList(),
            shopSubscriptions = ShopSubscription.entries.map { subscription ->
                ChipData(
                    id = subscription.name,
                    name = subscription.name.lowercase().replaceFirstChar {
                        if (it.isLowerCase()) it.titlecase(Locale.ROOT) else it.toString()
                    },
                    selected = false
                )
            }.toPersistentList()
        )
    )
    val viewState = _viewState.asStateFlow()

    private val _oneTimeEvents = MutableSharedFlow<Unit>()
    val oneTimeEvents = _oneTimeEvents.asSharedFlow()

    fun onShopCategoryClicked(category: ChipData) {
        val newCategories = ChipStateHandler.handleMonoSelect(
            currentState = _viewState.value.shopCategories, clickedChip = category
        ).toImmutableList()
        _viewState.update { it.copy(shopCategories = newCategories, shopCategoryError = null) }
    }

    fun onShopSubscriptionClicked(subscription: ChipData) {
        val newSubscriptions = ChipStateHandler.handleMonoSelect(
            currentState = _viewState.value.shopSubscriptions, clickedChip = subscription
        ).toImmutableList()
        _viewState.update {
            it.copy(
                shopSubscriptions = newSubscriptions, shopSubscriptionError = null
            )
        }
    }

    fun onFullNameChanged(fullName: String) {
        _viewState.update { it.copy(fullName = fullName, fullNameError = null) }
    }

    fun onPhoneNumberChanged(phoneNumber: String) {
        _viewState.update { it.copy(phoneNumber = phoneNumber, phoneNumberError = null) }
    }

    fun onEmailChanged(email: String) {
        _viewState.update { it.copy(email = email, emailError = null) }
    }

    fun onTosAndPpConfirmChanged(checked: Boolean) {
        _viewState.update { it.copy(confirmTosAndPp = checked, confirmTosAndPpError = null) }
    }

    fun onSubmit() {
        if (validateForm().not()) return

        val shopCategory = _viewState.value.shopCategories.firstOrNull { it.selected }?.let {
            ShopCategory.valueOf(it.id)
        } ?: run {
            // Shouldn't happen but as a security !
            _viewState.update {
                it.copy(shopCategoryError = UiText.FromRes(R.string.shop_account_sign_up_screen_shop_category_required))
            }
            return
        }
        val shopSubscription = _viewState.value.shopSubscriptions.firstOrNull { it.selected }?.let {
            ShopSubscription.valueOf(it.id)
        } ?: run {
            // Shouldn't happen but as a security !
            _viewState.update {
                it.copy(shopSubscriptionError = UiText.FromRes(R.string.shop_account_sign_up_screen_shop_subscription_required))
            }
            return
        }
        val (fullName, phoneNumber, email) = _viewState.value
        showLoading()
        viewModelScope.launch {
            val result = authenticationRepository.registerAccount(
                phoneNumber = phoneNumber,
                fullName = fullName,
                email = email,
                shopCategory = shopCategory,
                shopSubscription = shopSubscription
            )
            hideLoading()
            updateFor(result)
        }
    }

    private suspend fun updateFor(result: Resource<Unit, NetworkAndBackendErrors>) {
        when (result) {
            is Resource.Success -> _oneTimeEvents.emit(Unit)

            is Resource.Failure -> renderFailure(error = result.error)
        }
    }

    private fun validateForm(): Boolean {
        val shopCategories = _viewState.value.shopCategories
        if (shopCategories.none { it.selected }) {
            _viewState.update {
                it.copy(shopCategoryError = UiText.FromRes(R.string.shop_account_sign_up_screen_shop_category_required))
            }
            return false
        }

        val shopSubscriptions = _viewState.value.shopSubscriptions
        if (shopSubscriptions.none { it.selected }) {
            _viewState.update {
                it.copy(shopSubscriptionError = UiText.FromRes(R.string.shop_account_sign_up_screen_shop_subscription_required))
            }
            return false
        }

        val fullName = _viewState.value.fullName
        if (credentialsValidator.validateFullName(fullName).not()) {
            _viewState.update {
                it.copy(fullNameError = UiText.FromRes(R.string.common_full_name_field_required))
            }
            return false
        }

        val phoneNumber = _viewState.value.phoneNumber
        when {
            phoneNumber.isBlank() -> R.string.common_phone_number_field_required
            credentialsValidator.validatePhone(phoneNumber)
                .not() -> R.string.common_phone_number_field_format_invalid

            else -> null
        }?.let { errorMsg ->
            _viewState.update { it.copy(phoneNumberError = UiText.FromRes(errorMsg)) }
            return false
        }

        val email = _viewState.value.email
        when {
            email.isBlank() -> R.string.common_email_field_required
            credentialsValidator.validateEmail(email)
                .not() -> R.string.common_email_field_format_invalid

            else -> null
        }?.let { errorMsg ->
            _viewState.update { it.copy(emailError = UiText.FromRes(errorMsg)) }
            return false
        }

        val tosAndPpConfirm = _viewState.value.confirmTosAndPp
        if (tosAndPpConfirm.not()) {
            _viewState.update { it.copy(confirmTosAndPpError = R.string.common_tos_and_pp_field_required) }
            return false
        }

        return true
    }

    private fun hideLoading() {
        _viewState.update { it.copy(loading = false) }
    }

    private fun showLoading() {
        _viewState.update { it.copy(loading = true) }
    }
}
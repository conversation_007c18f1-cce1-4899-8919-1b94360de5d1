package se.scmv.morocco.authentication.presentation.private_account.merged

import android.widget.Toast
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.IntentSenderRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.LocalOverscrollConfiguration
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.google.android.gms.auth.api.identity.Identity
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import se.scmv.morocco.analytics.TrackScreenViewEvent
import se.scmv.morocco.analytics.models.AnalyticsEvent
import se.scmv.morocco.analytics.models.Param
import se.scmv.morocco.authentication.R
import se.scmv.morocco.authentication.presentation.common.GoogleAuthUi
import se.scmv.morocco.authentication.presentation.common.TestTags
import se.scmv.morocco.designsystem.components.AvPasswordField
import se.scmv.morocco.designsystem.components.AvPrimaryButton
import se.scmv.morocco.designsystem.components.AvScreenSubTitle
import se.scmv.morocco.designsystem.components.AvScreenTitle
import se.scmv.morocco.designsystem.components.AvTextField
import se.scmv.morocco.designsystem.components.AvTopAppBar
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.Gray500
import se.scmv.morocco.designsystem.theme.dimens

@Composable
fun PrivateAccountMergedRoute(
    navigateBack: () -> Unit,
    navigateToResetPassword: () -> Unit,
    navigateToOtpValidation: (String) -> Unit,
    navigateToStoreSignIn: () -> Unit,
    modifier: Modifier = Modifier,
    viewModel: PrivateAccountMergedViewModel = hiltViewModel(),
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()

    val googleAuthUi = remember { GoogleAuthUi(context, Identity.getSignInClient(context)) }
    val googleSignInLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartIntentSenderForResult()
    ) { result ->
        result.data?.let {
            val token = googleAuthUi.getGoogleIdToken(it) ?: return@let
            viewModel.onSignInWithGoogle(token)
        }
    }

    val state = viewModel.viewState.collectAsState().value
    var googleLoading by remember { mutableStateOf(false) }
    Column(modifier = modifier.fillMaxSize()) {
        AvTopAppBar(onNavigationIconClicked = navigateBack)
        PrivateAccountMergedScreen(
            modifier = modifier.fillMaxSize()
                .padding(horizontal = MaterialTheme.dimens.screenPaddingHorizontal)
                .padding(top = MaterialTheme.dimens.medium)
                .padding(bottom = MaterialTheme.dimens.screenPaddingHorizontal),
            state = state,
            onPhoneNumberChanged = viewModel::onPhoneNumberChanged,
            onPasswordChanged = viewModel::onPasswordChanged,
            onPasswordForgottenClicked = navigateToResetPassword,
            onCheckAccountClicked = viewModel::onCheckAccount,
            onSubmitBtnClicked = viewModel::onSubmit,
            onSignUpClicked = viewModel::onSignUp,
            onGoogleBtnClicked = {
                scope.launch {
                    googleLoading = true
                    val signInIntent = googleAuthUi.signIn()
                    googleLoading = false
                    if (signInIntent == null) {
                        Toast.makeText(
                            context,
                            R.string.common_unexpected_error_verify_and_try_later,
                            Toast.LENGTH_SHORT
                        ).show()
                        return@launch
                    }
                    googleSignInLauncher.launch(IntentSenderRequest.Builder(signInIntent).build())
                }
            },
            onGoToStoreSignInBtnClicked = navigateToStoreSignIn
        )
    }
    
    if (googleLoading) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            CircularProgressIndicator()
        }
    }
    
    LaunchedEffect(key1 = Unit) {
        viewModel.oneTimeEvents.collectLatest { event ->
            when (event) {
                is PrivateAccountMergedOneTimeEvents.Success -> navigateBack()
                is PrivateAccountMergedOneTimeEvents.Otp -> {
                    navigateToOtpValidation(event.phone)
                }
            }
        }
    }
    
    TrackScreenViewEvent(
        screenName = AnalyticsEvent.ScreensNames.LOGIN,
        properties = setOf(
            Param(
                key = AnalyticsEvent.ParamKeys.ACCOUNT_TYPE,
                value = AnalyticsEvent.ParamValues.ACCOUNT_TYPE_PRIVATE
            )
        )
    )
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
private fun PrivateAccountMergedScreen(
    modifier: Modifier = Modifier,
    state: PrivateAccountMergedViewState,
    onPhoneNumberChanged: (String) -> Unit,
    onPasswordChanged: (String) -> Unit,
    onPasswordForgottenClicked: () -> Unit,
    onCheckAccountClicked: () -> Unit,
    onSubmitBtnClicked: () -> Unit,
    onSignUpClicked: () -> Unit,
    onGoogleBtnClicked: () -> Unit,
    onGoToStoreSignInBtnClicked: () -> Unit
) {
    CompositionLocalProvider(
        LocalOverscrollConfiguration provides null
    ) {
        Column(
            modifier = modifier.verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.big)
        ) {
            PrivateAccountMergedForm(
                viewState = state,
                onPhoneNumberChanged = onPhoneNumberChanged,
                onPasswordChanged = onPasswordChanged,
                onPasswordForgottenClicked = onPasswordForgottenClicked,
                onCheckAccountClicked = onCheckAccountClicked,
                onSubmit = onSubmitBtnClicked,
                onSignUp = onSignUpClicked
            )
            
            // Always show Google sign-in, not just when account exists
            PrivateAccountMergedSocialMediaProviders(
                onGoogleBtnClicked = onGoogleBtnClicked
            )
            
            // Shop sign-in button
            Button(
                modifier = Modifier.fillMaxWidth(),
                onClick = onGoToStoreSignInBtnClicked,
                shape = MaterialTheme.shapes.small,
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.surfaceContainer,
                    contentColor = MaterialTheme.colorScheme.onSurface
                )
            ) {
                Text(
                    text = stringResource(id = R.string.private_account_merged_screen_shop_signin),
                    style = MaterialTheme.typography.titleSmall
                )
            }
        }
    }
}

@Composable
private fun PrivateAccountMergedForm(
    viewState: PrivateAccountMergedViewState,
    onPhoneNumberChanged: (String) -> Unit,
    onPasswordChanged: (String) -> Unit,
    onPasswordForgottenClicked: () -> Unit,
    onCheckAccountClicked: () -> Unit,
    onSubmit: () -> Unit,
    onSignUp: () -> Unit
) {
    val context = LocalContext.current
    val focusManager = LocalFocusManager.current
    
    Column(
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.extraBig)
    ) {
        Column(
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.large)
        ) {
            AvScreenTitle(title = R.string.private_account_merged_screen_title)
            AvScreenSubTitle(title = R.string.private_account_merged_screen_subtitle)
        }
        
        Column(
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.large)
        ) {
            AvTextField(
                modifier = Modifier
                    .fillMaxWidth()
                    .testTag(TestTags.TEST_TAG_PHONE_NUMBER),
                value = viewState.phoneNumber,
                onValueChanged = onPhoneNumberChanged,
                title = R.string.common_phone_number_field_label,
                placeholder = R.string.common_phone_number_field_placeholder,
                error = viewState.phoneNumberError?.getValue(context),
                keyboardOptions = KeyboardOptions(
                    keyboardType = KeyboardType.Phone,
                    imeAction = ImeAction.Next
                ),
                keyboardActions = KeyboardActions(
                    onNext = {
                        if (viewState.accountExists == null) {
                            onCheckAccountClicked()
                        }
                    }
                )
            )
            
            if (viewState.showPasswordField) {
                AvPasswordField(
                    modifier = Modifier
                        .fillMaxWidth()
                        .testTag(TestTags.TEST_TAG_PASSWORD),
                    value = viewState.password,
                    onValueChanged = onPasswordChanged,
                    title = R.string.common_password_field_label,
                    placeholder = R.string.common_password_field_placeholder,
                    error = viewState.passwordError?.getValue(context),
                    keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
                    keyboardActions = KeyboardActions(
                        onDone = {
                            focusManager.clearFocus()
                            onSubmit()
                        }
                    )
                )
                
                if (viewState.showForgotPassword) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(MaterialTheme.dimens.extraBig),
                        verticalArrangement = Arrangement.Top,
                        horizontalAlignment = Alignment.End
                    ) {
                        Text(
                            modifier = Modifier.clickable(onClick = onPasswordForgottenClicked),
                            text = stringResource(id = R.string.private_account_sign_in_screen_password_forgotten),
                            color = MaterialTheme.colorScheme.primary,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }
            
            // Action button
            when {
                viewState.checkingAccount -> {
                    Box(
                        modifier = Modifier.fillMaxWidth(),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator()
                    }
                }
                viewState.accountExists == true -> {
                    AvPrimaryButton(
                        modifier = Modifier.fillMaxWidth(),
                        text = stringResource(id = R.string.private_account_sign_in_screen_submit_button),
                        loading = viewState.loading,
                        onClick = {
                            focusManager.clearFocus()
                            onSubmit()
                        }
                    )
                }
                else -> {
                    AvPrimaryButton(
                        modifier = Modifier.fillMaxWidth(),
                        text = stringResource(id = R.string.common_continue),
                        loading = viewState.loading,
                        onClick = {
                            focusManager.clearFocus()
                            onCheckAccountClicked()
                        }
                    )
                }
            }
        }
    }
}

@Composable
private fun PrivateAccountMergedSocialMediaProviders(
    onGoogleBtnClicked: () -> Unit,
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.large),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Row(
            modifier = Modifier.padding(vertical = MaterialTheme.dimens.large),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium)
        ) {
            HorizontalDivider(modifier = Modifier.weight(1f))
            Text(
                text = stringResource(id = R.string.private_account_sign_in_screen_other_providers),
                color = Gray500,
                style = MaterialTheme.typography.bodySmall
            )
            HorizontalDivider(modifier = Modifier.weight(1f))
        }

        OutlinedButton(
            border = BorderStroke(1.dp, MaterialTheme.colorScheme.outline),
            onClick = {
                onGoogleBtnClicked()
            },
            shape = MaterialTheme.shapes.small
        ) {
            Image(
                painter = painterResource(id = R.drawable.ic_google),
                contentDescription = stringResource(id = R.string.private_account_sign_in_screen_other_providers),
                modifier = Modifier.size(MaterialTheme.dimens.big)
            )
            Spacer(modifier = Modifier.width(MaterialTheme.dimens.medium))
            Text(text = stringResource(R.string.private_account_sign_in_screen_login_with_google))
        }
    }
}

@Preview
@Composable
private fun PrivateAccountMergedScreenPreview() {
    AvitoTheme {
        Surface {
            var state by remember {
                mutableStateOf(PrivateAccountMergedViewState())
            }
            PrivateAccountMergedScreen(
                state = state,
                onPhoneNumberChanged = {
                    state = state.copy(phoneNumber = it)
                },
                onPasswordChanged = {
                    state = state.copy(password = it)
                },
                onPasswordForgottenClicked = {},
                onCheckAccountClicked = {},
                onSubmitBtnClicked = {},
                onSignUpClicked = {},
                onGoogleBtnClicked = {},
                onGoToStoreSignInBtnClicked = {}
            )
        }
    }
} 
package se.scmv.morocco.authentication.domain.validators

import androidx.core.util.PatternsCompat
import javax.inject.Inject

class CredentialsValidator @Inject constructor() {

    /**
     * Validates an email address string using a regular expression pattern.
     *
     * The function checks if the provided email string matches the standard email address pattern.
     * It utilizes the `PatternsCompat.EMAIL_ADDRESS` pattern from the Android framework to perform the validation.
     *
     * @param email The email address to be validated as a [String].
     * @return `true` if the email address matches the pattern and is considered valid, `false` otherwise.
     *
     * Example usage:
     * ```
     * val isValid = validateEmail("<EMAIL>") // Returns true
     * val isValid = validateEmail("invalid-email") // Returns false
     * ```
     */
    fun validateEmail(email: String): Boolean {
        return PatternsCompat.EMAIL_ADDRESS.matcher(email).matches()
    }

    /**
     * Validates a phone number string based on predefined criteria.
     *
     * The function performs the following validations:
     * - The phone number must not be blank, less than 10 characters, or contain any non-digit characters.
     * - Moroccan number: phone number is exactly 10 digits long, it must start with one
     * of the prefixes "05", "06", "07", and "08".
     * - International number: phone number is more than 10 digits long, it must start with "0".
     *
     * @param phone The phone number to be validated as a [String].
     * @return `true` if the phone number is valid according to the specified criteria, `false` otherwise.
     *
     * Example usage:
     * ```
     * val isValid = validatePhone("051234") // Returns false
     * val isValid = validatePhone("0512345678") // Returns true
     * val isValid = validatePhone("0912345678") // Returns false
     * val isValid = validatePhone("00512345678") // Returns true
     * ```
     */
    fun validatePhone(
        phone: String
    ): Boolean {
        if (phone.isBlank() || phone.length < 10 || phone.any { it.isDigit().not() }) return false

        return when (phone.length) {
            10 -> {
                val prefixes = listOf("05", "06", "07", "08")
                prefixes.any { phone.startsWith(it) }
            }
            else -> phone.startsWith("0")
        }
    }

    /**
     * Validates a password string based on predefined criteria.
     * Currently we check only if the password is not blank. But we create this function
     * to have the password validation centralized her and if in the future we want to add
     * other validation criteria we only add them here
     */
    fun validatePassword(password: String): Boolean {
        return password.isNotBlank()
    }

    /**
     * Validates a fullName string based on predefined criteria.
     * Currently we check only if the fullName is not blank. But we create this function
     * to have the fullName validation centralized her and if in the future we want to add
     * other validation criteria we only add them here
     */
    fun validateFullName(fullName: String): Boolean {
        return fullName.isNotBlank()
    }
}
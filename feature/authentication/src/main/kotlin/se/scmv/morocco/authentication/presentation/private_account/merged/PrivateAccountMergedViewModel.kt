package se.scmv.morocco.authentication.presentation.private_account.merged

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import se.scmv.morocco.authentication.R
import se.scmv.morocco.authentication.domain.validators.CredentialsValidator
import se.scmv.morocco.authentication.presentation.private_account.merged.Country
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.domain.models.NetworkAndBackendErrors
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.models.SignInResult
import se.scmv.morocco.domain.models.SignInType
import se.scmv.morocco.domain.repositories.AuthenticationRepository
import se.scmv.morocco.ui.renderFailure
import javax.inject.Inject

@HiltViewModel
class PrivateAccountMergedViewModel @Inject constructor(
    private val credentialsValidator: CredentialsValidator,
    private val authenticationRepository: AuthenticationRepository,
) : ViewModel() {

    private val _viewState = MutableStateFlow(PrivateAccountMergedViewState())
    val viewState = _viewState.asStateFlow()

    private val _oneTimeEvents = MutableSharedFlow<PrivateAccountMergedOneTimeEvents>()
    val oneTimeEvents = _oneTimeEvents.asSharedFlow()

    fun onPhoneNumberChanged(phoneNumber: String) {
        val currentPhoneNumber = _viewState.value.phoneNumber
        if (currentPhoneNumber != phoneNumber) {
            _viewState.update {
                it.copy(
                    phoneNumber = phoneNumber,
                    phoneNumberError = null,
                    accountExists = null,
                    showPasswordField = false,
                    showForgotPassword = false,
                    showGoogleSignIn = false
                )
            }
        } else {
            _viewState.update {
                it.copy(
                    phoneNumber = phoneNumber,
                    phoneNumberError = null
                )
            }
        }
    }

    fun onCountrySelected(country: Country) {
        _viewState.update {
            it.copy(
                selectedCountry = country,
                phoneNumberError = null,
                accountExists = null,
                showPasswordField = false,
                showForgotPassword = false,
                showGoogleSignIn = false
            )
        }
    }

    fun onPasswordChanged(password: String) {
        _viewState.update { it.copy(password = password, passwordError = null) }
    }

    fun onCheckAccount() {
        clearErrors()
        if (validatePhone().not()) return

        showCheckingAccount()
        val fullPhoneNumber = getFullPhoneNumber()
        viewModelScope.launch {
            val result = authenticationRepository.checkAccountExistance(fullPhoneNumber)
            hideCheckingAccount()
            updateForAccountCheckResult(result)
        }
    }

    fun onSubmit() {
        clearErrors()
        if (validateForm().not()) return

        showLoading()
        val fullPhoneNumber = getFullPhoneNumber()
        val password = _viewState.value.password
        viewModelScope.launch {
            val result = authenticationRepository.signIn(
                emailOrPhone = fullPhoneNumber,
                password = password,
                type = SignInType.PHONE
            )
            hideLoading()
            updateForResult(result)
        }
    }

    fun onSignInWithGoogle(googleIdToken: String) {
        viewModelScope.launch {
            showLoading()
            val result = authenticationRepository.signInWithGoogle(googleIdToken)
            hideLoading()
            updateForResult(result)
        }
    }

    fun onSignUp() {
        clearErrors()
        if (validatePhone().not()) {
            // If validation fails, don't proceed
            return
        }

        showLoading()
        val fullPhoneNumber = getFullPhoneNumber()
        viewModelScope.launch {
            val result = authenticationRepository.sendSmsVerificationForSignUp(fullPhoneNumber)
            hideLoading()
            updateForSignUpResult(result)
        }
    }

    private fun validatePhone(): Boolean {
        val phoneNumber = _viewState.value.phoneNumber
        val selectedCountry = _viewState.value.selectedCountry

        when {
            phoneNumber.isBlank() -> R.string.common_phone_number_field_required
            else -> {
                // For Morocco, validate that phone starts with 0 and has 10 digits
                if (selectedCountry?.phonePrefix == "+212") {
                    if (!isValidMoroccanPhoneNumber(phoneNumber)) {
                        R.string.common_phone_number_field_format_invalid
                    } else null
                } else {
                    // For other countries, validate with flexible 0 handling
                    if (!isValidInternationalPhoneNumber(phoneNumber)) {
                        R.string.common_phone_number_field_format_invalid
                    } else null
                }
            }
        }?.let { error ->
            _viewState.update { it.copy(phoneNumberError = UiText.FromRes(error)) }
            return false
        }
        return true
    }

    private fun validateForm(): Boolean {
        val phoneNumber = _viewState.value.phoneNumber
        val selectedCountry = _viewState.value.selectedCountry
        val password = _viewState.value.password

        when {
            phoneNumber.isBlank() -> R.string.common_phone_number_field_required
            else -> {
                // For Morocco, validate that phone starts with 0 and has 10 digits
                if (selectedCountry?.phonePrefix == "+212") {
                    if (!isValidMoroccanPhoneNumber(phoneNumber)) {
                        R.string.common_phone_number_field_format_invalid
                    } else null
                } else {
                    // For other countries, validate with flexible 0 handling
                    if (!isValidInternationalPhoneNumber(phoneNumber)) {
                        R.string.common_phone_number_field_format_invalid
                    } else null
                }
            }
        }?.let { error ->
            _viewState.update { it.copy(phoneNumberError = UiText.FromRes(error)) }
            return false
        }

        if (credentialsValidator.validatePassword(password).not()) {
            _viewState.update {
                it.copy(passwordError = UiText.FromRes(R.string.common_password_field_required))
            }
            return false
        }

        return true
    }

    private suspend fun updateForAccountCheckResult(result: Resource<Boolean, NetworkAndBackendErrors>) {
        when (result) {
            is Resource.Success -> {
                val accountExists = result.data
                if (accountExists) {
                    _viewState.update {
                        it.copy(
                            accountExists = accountExists,
                            showPasswordField = accountExists,
                            showForgotPassword = accountExists,
                            showGoogleSignIn = accountExists
                        )
                    }
                } else {
                    // If account doesn't exist, automatically trigger sign-up
                    val fullPhoneNumber = getFullPhoneNumber()
                    val signUpResult =
                        authenticationRepository.sendSmsVerificationForSignUp(fullPhoneNumber)
                    updateForSignUpResult(signUpResult)
                }
            }

            is Resource.Failure -> renderFailure(result.error)
        }
    }

    private suspend fun updateForResult(result: Resource<SignInResult, NetworkAndBackendErrors>) {
        when (result) {
            is Resource.Success -> {
                when (result.data) {
                    SignInResult.SUCCESS -> _oneTimeEvents.emit(PrivateAccountMergedOneTimeEvents.Success)

                    SignInResult.ECOMMERCE_RESTRICTION -> renderFailure(
                        UiText.FromRes(R.string.ecommerce_store_owner_restriction_message)
                    )
                }
            }

            is Resource.Failure -> renderFailure(result.error)
        }
    }

    private suspend fun updateForSignUpResult(result: Resource<String, NetworkAndBackendErrors>) {
        when (result) {
            is Resource.Success -> _oneTimeEvents.emit(
                PrivateAccountMergedOneTimeEvents.Otp(phone = result.data)
            )

            is Resource.Failure -> renderFailure(result.error)
        }
    }

    private fun clearErrors() {
        _viewState.update { it.copy(phoneNumberError = null, passwordError = null) }
    }

    private fun showLoading() {
        _viewState.update { it.copy(loading = true) }
    }

    private fun hideLoading() {
        _viewState.update { it.copy(loading = false) }
    }

    private fun showCheckingAccount() {
        _viewState.update { it.copy(checkingAccount = true) }
    }

    private fun hideCheckingAccount() {
        _viewState.update { it.copy(checkingAccount = false) }
    }

    private fun getFullPhoneNumber(): String {
        val phoneNumber = _viewState.value.phoneNumber
        val selectedCountry = _viewState.value.selectedCountry ?: return phoneNumber

        // For all countries, remove leading 0 if present and add country code
        val cleanedPhoneNumber = if (phoneNumber.startsWith("0")) {
            phoneNumber.substring(1) // Remove leading 0
        } else {
            phoneNumber
        }

        return "${selectedCountry.phonePrefix}$cleanedPhoneNumber"
    }

    private fun isValidMoroccanPhoneNumber(phoneNumber: String): Boolean {
        // Handle both formats: with or without leading 0
        val normalizedPhone = if (phoneNumber.startsWith("0")) {
            phoneNumber.substring(1) // Remove leading 0
        } else {
            phoneNumber
        }

        // Check if it's 9 digits after removing the 0
        return normalizedPhone.length == 9
    }

    private fun isValidInternationalPhoneNumber(phoneNumber: String): Boolean {
        // Handle both formats: with or without leading 0
        val normalizedPhone = if (phoneNumber.startsWith("0")) {
            phoneNumber.substring(1) // Remove leading 0
        } else {
            phoneNumber
        }

        if (normalizedPhone.length < 4 || normalizedPhone.length > 12) return false

        // Should only contain digits
        return normalizedPhone.all { it.isDigit() }
    }
}
package se.scmv.morocco.authentication.presentation.resset_password.private_account.update

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import se.scmv.morocco.authentication.R
import se.scmv.morocco.authentication.domain.validators.CredentialsValidator
import se.scmv.morocco.authentication.presentation.navigation.ARG_OTP_CODE
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.domain.models.NetworkAndBackendErrors
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.repositories.AuthenticationRepository
import se.scmv.morocco.ui.renderFailure
import javax.inject.Inject

@HiltViewModel
class PasswordResetViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle,
    private val credentialsValidator: CredentialsValidator,
    private val authenticationRepository: AuthenticationRepository,
) : ViewModel() {

    private val otpCode: String = requireNotNull(savedStateHandle[ARG_OTP_CODE]) {
        "Otp code cannot be null! make sure you passed an otp code in navigation."
    }

    private val _viewState = MutableStateFlow(PasswordResetViewState())
    val viewState = _viewState.asStateFlow()

    private val _oneTimeEvents = MutableSharedFlow<Unit>()
    val oneTimeEvents = _oneTimeEvents.asSharedFlow()

    fun onPasswordChanged(password: String) {
        _viewState.update { it.copy(password = password, passwordError = null) }
    }

    fun onPasswordConfirmChanged(password: String) {
        _viewState.update { it.copy(passwordConfirm = password, passwordConfirmError = null) }
    }

    fun onSubmit() {
        if (validateForm().not()) return

        val password = _viewState.value.password
        showLoading()
        viewModelScope.launch {
            val result = authenticationRepository.resetPassword(otpCode, password)
            hideLoading()
            updateFor(result)
        }
    }

    private suspend fun updateFor(result: Resource<String, NetworkAndBackendErrors>) {
        when (result) {
            is Resource.Success -> _oneTimeEvents.emit(Unit)

            is Resource.Failure -> renderFailure(error = result.error)
        }
    }

    private fun validateForm(): Boolean {
        val (password, _, passwordConfirm) = _viewState.value
        if (credentialsValidator.validatePassword(password).not()) {
            _viewState.update { it.copy(passwordError = UiText.FromRes(R.string.common_password_field_required)) }
            return false
        }
        if (credentialsValidator.validatePassword(passwordConfirm).not()) {
            _viewState.update { it.copy(passwordConfirmError = UiText.FromRes(R.string.common_password_confirm_field_required)) }
            return false
        }
        if (password != passwordConfirm) {
            _viewState.update { it.copy(passwordConfirmError = UiText.FromRes(R.string.common_password_confirm_field_not_match)) }
            return false
        }
        return true
    }

    private fun showLoading() {
        _viewState.update { it.copy(loading = true) }
    }

    private fun hideLoading() {
        _viewState.update { it.copy(loading = false) }
    }
}
package se.scmv.morocco.authentication.presentation.private_account.signup.phone

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.LocalOverscrollConfiguration
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import se.scmv.morocco.analytics.TrackScreenViewEvent
import se.scmv.morocco.analytics.models.AnalyticsEvent
import se.scmv.morocco.analytics.models.Param
import se.scmv.morocco.authentication.R
import se.scmv.morocco.authentication.presentation.common.TestTags
import se.scmv.morocco.designsystem.components.AvPrimaryButton
import se.scmv.morocco.designsystem.components.AvScreenSubTitle
import se.scmv.morocco.designsystem.components.AvScreenTitle
import se.scmv.morocco.designsystem.components.AvTermOfServicesAndPrivacyPolicy
import se.scmv.morocco.designsystem.components.AvTextField
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.dimens

@Composable
fun PrivateAccountSignUpRoute(
    modifier: Modifier = Modifier,
    viewModel: PrivateAccountSignUpViewModel = hiltViewModel(),
    navigateToOtpValidation: (String) -> Unit,
    navigateToWebViewScreen: (String, String) -> Unit,
) {
    val state = viewModel.viewState.collectAsState().value
    PrivateAccountSignUpScreen(
        modifier = modifier
            .fillMaxSize()
            .padding(horizontal = MaterialTheme.dimens.screenPaddingHorizontal)
            .padding(top = MaterialTheme.dimens.medium)
            .padding(bottom = MaterialTheme.dimens.screenPaddingHorizontal),
        state = state,
        onPhoneNumberChanged = viewModel::onPhoneNumberChanged,
        onTosAndPpConfirmChanged = viewModel::onTosAndPpConfirmChanged,
        navigateToWebViewScreen = navigateToWebViewScreen,
        onSubmit = viewModel::onSubmit,
    )
    LaunchedEffect(Unit) {
        viewModel.oneTimeEvents.collect {
            navigateToOtpValidation(it.phone)
        }
    }
    TrackScreenViewEvent(
        screenName = AnalyticsEvent.ScreensNames.SIGNUP,
        properties = setOf(
            Param(
                key = AnalyticsEvent.ParamKeys.ACCOUNT_TYPE,
                value = AnalyticsEvent.ParamValues.ACCOUNT_TYPE_PRIVATE
            )
        )
    )
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
private fun PrivateAccountSignUpScreen(
    modifier: Modifier = Modifier,
    state: PrivateAccountSignUpViewState,
    onPhoneNumberChanged: (String) -> Unit,
    onTosAndPpConfirmChanged: (Boolean) -> Unit,
    navigateToWebViewScreen: (String, String) -> Unit,
    onSubmit: () -> Unit,
) {
    CompositionLocalProvider(
        LocalOverscrollConfiguration provides null
    ) {
        Column(
            modifier = modifier.verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.extraExtraBig)
        ) {
            Column(
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.large)
            ) {
                AvScreenTitle(title = R.string.private_account_sign_up_screen_title)
                AvScreenSubTitle(title = R.string.private_account_sign_up_screen_subtitle)
            }
            Column(
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.extraBig)
            ) {
                val context = LocalContext.current
                val focusManager = LocalFocusManager.current
                AvTextField(
                    modifier = Modifier
                        .fillMaxWidth()
                        .testTag(TestTags.TEST_TAG_PHONE_NUMBER),
                    value = state.phoneNumber,
                    onValueChanged = onPhoneNumberChanged,
                    required = true,
                    title = R.string.common_phone_number_field_label,
                    placeholder = R.string.common_phone_number_field_placeholder,
                    error = state.phoneError?.getValue(context),
                    keyboardOptions = KeyboardOptions(
                        keyboardType = KeyboardType.Phone,
                        imeAction = ImeAction.Done
                    ),
                    keyboardActions = KeyboardActions(
                        onDone = {
                            focusManager.clearFocus()
                            onSubmit()
                        }
                    )
                )
                AvTermOfServicesAndPrivacyPolicy(
                    checked = state.confirmTosAndPp,
                    error = state.confirmTosAndPpError,
                    onCheckedChange = onTosAndPpConfirmChanged,
                    navigateToWebViewScreen = navigateToWebViewScreen
                )
                AvPrimaryButton(
                    modifier = Modifier.fillMaxWidth(),
                    text = stringResource(R.string.common_continue),
                    loading = state.loading,
                    onClick = {
                        focusManager.clearFocus()
                        onSubmit()
                    }
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun PrivateAccountSignUpScreenPreview() {
    AvitoTheme {
        PrivateAccountSignUpScreen(
            state = PrivateAccountSignUpViewState(),
            onPhoneNumberChanged = { },
            onTosAndPpConfirmChanged = {},
            navigateToWebViewScreen = {_,_ -> },
            onSubmit = { },
        )
    }
}
package se.scmv.morocco.authentication.presentation.otp

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import se.scmv.morocco.authentication.R
import se.scmv.morocco.authentication.presentation.navigation.ARG_OTP_VALIDATION_REASON
import se.scmv.morocco.authentication.presentation.navigation.ARG_PHONE_NUMBER
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.domain.models.NetworkAndBackendErrors
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.repositories.AuthenticationRepository
import se.scmv.morocco.ui.SnackBarController
import se.scmv.morocco.ui.SnackBarEvent
import se.scmv.morocco.ui.SnackBarType
import se.scmv.morocco.ui.renderFailure
import javax.inject.Inject
import kotlin.time.Duration.Companion.seconds

@HiltViewModel
class OtpValidationViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle,
    private val authenticationRepository: AuthenticationRepository,
    private val codeResendTimeout: CodeResendTimeout
) : ViewModel() {

    private val phoneNumber: String = requireNotNull(savedStateHandle[ARG_PHONE_NUMBER]) {
        "Phone number cannot be null! make sure you passed a phone number in navigation."
    }
    private val otpValidationReason: OtpValidationReason = requireNotNull(
        savedStateHandle.get<String?>(ARG_OTP_VALIDATION_REASON)
            ?.let { OtpValidationReason.valueOf(it) }
    ) {
        "otp validation reason cannot be null! make sure you passed a OtpValidationReason in navigation."
    }

    private val _viewState = MutableStateFlow(OtpValidationViewState(phoneNumber))
    val viewState = _viewState.asStateFlow()

    private val _oneTimeEvents = MutableSharedFlow<OtpValidationSuccessEvent>()
    val oneTimeEvents = _oneTimeEvents.asSharedFlow()

    init {
        startResendTimer()
    }

    fun onCodeChanged(code: String) {
        _viewState.update { it.copy(code = code, codeError = null) }
    }

    fun onResendCodeClicked() {
        if (_viewState.value.isResendButtonEnabled.not()) return // Prevent spamming

        showLoading()
        viewModelScope.launch {
            val result = when (otpValidationReason) {
                OtpValidationReason.SIGN_UP -> authenticationRepository.sendSmsVerificationForSignUp(
                    phoneNumber
                )

                OtpValidationReason.PASSWORD_RESET -> authenticationRepository.sendSmsVerificationForPasswordReset(
                    phoneNumber
                )
            }
            hideLoading()
            when (result) {
                is Resource.Success -> {
                    startResendTimer()  // Start countdown timer.
                    showSuccessSnackBar(UiText.FromRes(R.string.otp_validation_screen_code_resent_success))
                }

                is Resource.Failure -> renderFailure(result.error)
            }
        }
    }

    fun onSkipValidationClicked() {
        viewModelScope.launch {
            _oneTimeEvents.emit(
                OtpValidationSuccessEvent(phoneNumber = phoneNumber, otpCode = null)
            )
        }
    }

    fun onSubmit() {
        if (validateForm().not()) return

        val code = _viewState.value.code
        showLoading()
        viewModelScope.launch {
            val result = when (otpValidationReason) {
                OtpValidationReason.SIGN_UP -> authenticationRepository.validatePhone(
                    phoneNumber = phoneNumber,
                    code = code
                )

                OtpValidationReason.PASSWORD_RESET -> authenticationRepository.validatePhone(
                    code = code
                )
            }
            hideLoading()
            updateFor(code, result)
        }
    }

    private fun validateForm(): Boolean {
        val code = _viewState.value.code
        if (code.isBlank()) {
            _viewState.update { it.copy(codeError = UiText.FromRes(R.string.otp_validation_screen_code_required)) }
            return false
        }
        if (code.length < 6) {
            _viewState.update { it.copy(codeError = UiText.FromRes(R.string.otp_validation_screen_code_not_completed)) }
            return false
        }

        return true
    }

    private suspend fun updateFor(code: String, result: Resource<Boolean, NetworkAndBackendErrors>) {
        when (result) {
            is Resource.Success -> _oneTimeEvents.emit(
                OtpValidationSuccessEvent(phoneNumber = phoneNumber, otpCode = code)
            )

            is Resource.Failure -> renderFailure(error = result.error)
        }
    }

    // New: Start the resend timer
    private fun startResendTimer() {
        _viewState.update {
            it.copy(
                isResendButtonEnabled = false,
                resendTimer = codeResendTimeout.timeout,
                isSkipValidationButtonEnabled = false
            )
        }
        viewModelScope.launch {
            for (i in codeResendTimeout.timeout downTo 1) {
                delay(1.seconds) // 1 second delay
                _viewState.update { state -> state.copy(resendTimer = i - 1) }
            }
            // Enable resend button after countdown
            _viewState.update { state ->
                state.copy(
                    isResendButtonEnabled = true,
                    isSkipValidationButtonEnabled = otpValidationReason == OtpValidationReason.SIGN_UP
                )
            }
        }
    }

    private fun showLoading() {
        _viewState.update { it.copy(loading = true) }
    }

    private fun hideLoading() {
        _viewState.update { it.copy(loading = false) }
    }

    private suspend fun showSuccessSnackBar(message: UiText) {
        SnackBarController.showSnackBar(
            event = SnackBarEvent(
                message = message,
                type = SnackBarType.successOrError(true)
            )
        )
    }
}
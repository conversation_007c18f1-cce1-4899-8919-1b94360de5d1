package se.scmv.morocco.authentication.presentation.shop_account.signup

import androidx.compose.runtime.Stable
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import se.scmv.morocco.designsystem.components.ChipData
import se.scmv.morocco.designsystem.utils.UiText

@Stable
data class ShopAccountSignUpViewState(
    val fullName: String = "",
    val phoneNumber: String = "",
    val email: String = "",
    val shopCategories: ImmutableList<ChipData> = persistentListOf(),
    val shopSubscriptions: ImmutableList<ChipData> = persistentListOf(),
    val confirmTosAndPp: Boolean = true,
    val fullNameError: UiText? = null,
    val phoneNumberError: UiText?= null,
    val emailError: UiText?= null,
    val shopCategoryError: UiText? = null,
    val shopSubscriptionError: UiText? = null,
    val confirmTosAndPpError: Int? = null,
    val loading: Boolean = false
)

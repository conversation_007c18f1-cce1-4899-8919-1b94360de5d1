package se.scmv.morocco.authentication.presentation.otp

import androidx.compose.runtime.Stable
import se.scmv.morocco.designsystem.utils.UiText

@Stable
data class OtpValidationViewState(
    val phoneNumber: String,
    val code: String = "",
    val codeError: UiText? = null,
    val loading: Boolean = false,
    val isResendButtonEnabled: Boolean = true,
    val resendTimer: Int = 0,
    val isSkipValidationButtonEnabled: Boolean = false,
)

enum class OtpValidationReason {
    SIGN_UP, PASSWORD_RESET
}

data class OtpValidationSuccessEvent(val phoneNumber: String, val otpCode: String?)

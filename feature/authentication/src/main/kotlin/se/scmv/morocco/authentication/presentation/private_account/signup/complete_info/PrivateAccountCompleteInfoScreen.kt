package se.scmv.morocco.authentication.presentation.private_account.signup.complete_info

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import se.scmv.morocco.authentication.R
import se.scmv.morocco.authentication.presentation.common.TestTags
import se.scmv.morocco.designsystem.components.AvPasswordField
import se.scmv.morocco.designsystem.components.AvPrimaryButton
import se.scmv.morocco.designsystem.components.AvScreenSubTitle
import se.scmv.morocco.designsystem.components.AvScreenTitle
import se.scmv.morocco.designsystem.components.AvTermOfServicesAndPrivacyPolicy
import se.scmv.morocco.designsystem.components.AvTextField
import se.scmv.morocco.designsystem.components.AvTopAppBar
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.Green700
import se.scmv.morocco.designsystem.theme.dimens

@Composable
fun PrivateAccountCompleteInfoRoute(
    modifier: Modifier = Modifier,
    viewModel: PrivateAccountCompleteInfoViewModel = hiltViewModel(),
    navigateBack: () -> Unit,
    navigateToHome: () -> Unit,
    navigateToWebViewScreen: (String, String) -> Unit,
) {
    Column (
        modifier = modifier,
    ) {
        val state = viewModel.viewState.collectAsState().value
        AvTopAppBar(onNavigationIconClicked = navigateBack)
        PrivateAccountCompleteInfoScreen(
            modifier = Modifier,
            state = state,
            onFullNameChanged = viewModel::onFullNameChanged,
            onPasswordChanged = viewModel::onPasswordChanged,
            onPasswordConfirmChanged = viewModel::onPasswordConfirmChanged,
            onTosAndPpConfirmChanged = viewModel::onTosAndPpConfirmChanged,
            navigateToWebViewScreen = navigateToWebViewScreen,
            onSubmit = viewModel::onSubmit
        )
    }
    LaunchedEffect(Unit) {
        viewModel.oneTimeEvents.collect { navigateToHome() }
    }
}

@Composable
private fun PrivateAccountCompleteInfoScreen(
    modifier: Modifier = Modifier,
    state: PrivateAccountCompleteInfoViewState,
    onFullNameChanged: (String) -> Unit,
    onPasswordChanged: (String) -> Unit,
    onPasswordConfirmChanged: (String) -> Unit,
    onTosAndPpConfirmChanged: (Boolean) -> Unit,
    navigateToWebViewScreen: (String, String) -> Unit,
    onSubmit: () -> Unit
) {
    val context = LocalContext.current
    val focusManager = LocalFocusManager.current
    Column(
        modifier = modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(
                horizontal = MaterialTheme.dimens.screenPaddingHorizontal,
                vertical = MaterialTheme.dimens.screenPaddingVertical
            ),
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.large)
    ) {
        Column(
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.large)
        ) {
            AvScreenTitle(title = R.string.private_account_complete_info_screen_title)
            AvScreenSubTitle(title = R.string.private_account_complete_info_screen_subtitle)
        }
        Spacer(modifier = Modifier.height(MaterialTheme.dimens.big))
        AvTextField(
            modifier = Modifier
                .fillMaxWidth()
                .testTag(TestTags.TEST_TAG_PHONE_NUMBER),
            value = state.phoneNumber,
            onValueChanged = {},
            enabled = false,
            title = R.string.common_phone_number_field_label,
            trailingIcon = {
                Icon(
                    modifier = Modifier.size(MaterialTheme.dimens.bigger),
                    painter = painterResource(id = se.scmv.morocco.designsystem.R.drawable.ic_check_circle),
                    contentDescription = null,
                    tint = Green700
                )
            },
        )
        AvTextField(
            modifier = Modifier
                .fillMaxWidth()
                .testTag(TestTags.TEST_TAG_FULL_NAME),
            value = state.fullName,
            onValueChanged = onFullNameChanged,
            title = R.string.common_full_name_field_label,
            placeholder = R.string.common_full_name_field_placeholder,
            error = state.fullNameError?.getValue(context),
            keyboardOptions = KeyboardOptions(
                keyboardType = KeyboardType.Text,
                imeAction = ImeAction.Next
            )
        )
        AvPasswordField(
            modifier = Modifier
                .fillMaxWidth()
                .testTag(TestTags.TEST_TAG_PASSWORD),
            value = state.password,
            onValueChanged = onPasswordChanged,
            title = R.string.common_password_field_label,
            placeholder = R.string.common_password_field_placeholder,
            error = state.passwordError?.getValue(context),
            keyboardOptions = KeyboardOptions(imeAction = ImeAction.Next)
        )
        AvPasswordField(
            modifier = Modifier
                .fillMaxWidth()
                .testTag(TestTags.TEST_TAG_PASSWORD_CONFIRM),
            value = state.passwordConfirm,
            onValueChanged = onPasswordConfirmChanged,
            title = R.string.common_password_confirm_field_label,
            placeholder = R.string.common_password_confirm_field_placeholder,
            error = state.passwordConfirmError?.getValue(context),
            keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
            keyboardActions = KeyboardActions(
                onDone = {
                    focusManager.clearFocus()
                    onSubmit()
                }
            )
        )
        AvTermOfServicesAndPrivacyPolicy(
            checked = state.confirmTosAndPp,
            error = state.confirmTosAndPpError,
            onCheckedChange = onTosAndPpConfirmChanged,
            navigateToWebViewScreen = navigateToWebViewScreen
        )
        Spacer(modifier = Modifier.height(MaterialTheme.dimens.big))
        AvPrimaryButton(
            modifier = Modifier.fillMaxWidth(),
            text = stringResource(R.string.private_account_complete_info_screen_submit_button),
            loading = state.loading,
            onClick = onSubmit
        )
    }
}

@Preview
@Composable
private fun PrivateAccountCompleteInfoScreenPreview() {
    AvitoTheme {
        Surface {
            var state by remember { mutableStateOf(PrivateAccountCompleteInfoViewState("**********")) }
            PrivateAccountCompleteInfoScreen(
                state = state,
                onFullNameChanged = {
                    state = state.copy(fullName = it)
                },
                onPasswordChanged = {
                    state = state.copy(password = it)
                },
                onPasswordConfirmChanged = {
                    state = state.copy(passwordConfirm = it)
                },
                onTosAndPpConfirmChanged = {
                    state = state.copy(confirmTosAndPp = it)
                },
                navigateToWebViewScreen = {_,_ -> },
                onSubmit = {}
            )
        }
    }
}

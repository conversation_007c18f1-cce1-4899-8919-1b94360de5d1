package se.scmv.morocco.authentication.presentation.private_account.signin

import androidx.compose.runtime.Stable
import se.scmv.morocco.authentication.presentation.common.LoginType
import se.scmv.morocco.designsystem.utils.UiText

@Stable
data class PrivateAccountSignInViewState(
    val emailOrPhone: String = "",
    val emailOrPhoneError: UiText? = null,
    val password: String = "",
    val passwordError: UiText? = null,
    val loading: Boolean = false,
)

sealed interface PrivateAccountSignInEvent {
    data class Success(
        val loginType: LoginType,
        val token: String
    ) : PrivateAccountSignInEvent
    
    data object EcommerceRestriction : PrivateAccountSignInEvent
}
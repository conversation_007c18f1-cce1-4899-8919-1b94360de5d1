package se.scmv.morocco.authentication.presentation.resset_password.shop_account

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import se.scmv.morocco.analytics.TrackScreenViewEvent
import se.scmv.morocco.analytics.models.AnalyticsEvent
import se.scmv.morocco.analytics.models.Param
import se.scmv.morocco.authentication.R
import se.scmv.morocco.authentication.presentation.common.TestTags
import se.scmv.morocco.designsystem.components.AvPrimaryButton
import se.scmv.morocco.designsystem.components.AvScreenSubTitle
import se.scmv.morocco.designsystem.components.AvTextField
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.dimens

@Composable
fun ResetPasswordEmailRoute(
    modifier: Modifier = Modifier,
    viewModel: ResetPasswordEmailViewModel = hiltViewModel(),
    navigateToSuccess: (String) -> Unit
) {
    val state = viewModel.viewState.collectAsState().value
    ResetPasswordEmailScreen(
        modifier = modifier,
        state = state,
        onEmailChanged = viewModel::onEmailChanged,
        onSubmit = viewModel::onSubmit,
    )
    LaunchedEffect(Unit) {
        viewModel.oneTimeEvents.collect { navigateToSuccess(it.email) }
    }
    TrackScreenViewEvent(
        screenName = AnalyticsEvent.ScreensNames.PASSWORD_RECOVERY,
        properties = setOf(
            Param(
                key = AnalyticsEvent.ParamKeys.ACCOUNT_TYPE,
                value = AnalyticsEvent.ParamValues.ACCOUNT_TYPE_SHOP
            )
        )
    )
}

@Composable
private fun ResetPasswordEmailScreen(
    modifier: Modifier = Modifier,
    state: ResetPasswordEmailViewState,
    onEmailChanged: (String) -> Unit,
    onSubmit: () -> Unit,
) {
    val context = LocalContext.current
    val focusManager = LocalFocusManager.current
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(horizontal = MaterialTheme.dimens.screenPaddingHorizontal)
            .padding(bottom = MaterialTheme.dimens.screenPaddingHorizontal),
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.extraExtraBig)
    ) {
        Column(
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.extraBig)
        ) {
            AvScreenSubTitle(title = R.string.reset_password_email_screen_subtitle)
            AvTextField(
                modifier = Modifier
                    .testTag(TestTags.TEST_TAG_EMAIL)
                    .fillMaxWidth(),
                value = state.email,
                onValueChanged = onEmailChanged,
                required = true,
                title = R.string.common_email_field_label,
                placeholder = R.string.common_email_field_placeholder,
                error = state.emailError?.getValue(context),
                keyboardOptions = KeyboardOptions(
                    imeAction = ImeAction.Next,
                    keyboardType = KeyboardType.Email
                ),
            )
        }
        AvPrimaryButton(
            modifier = Modifier.fillMaxWidth(),
            text = stringResource(R.string.common_send),
            loading = state.loading,
            onClick = {
                focusManager.clearFocus()
                onSubmit()
            }
        )
    }
}

@Preview
@Composable
private fun ResetPasswordEmailScreenPreview() {
    AvitoTheme {
        Surface {
            ResetPasswordEmailScreen(
                state = ResetPasswordEmailViewState(),
                onEmailChanged = {},
                onSubmit = {},
            )
        }
    }
}
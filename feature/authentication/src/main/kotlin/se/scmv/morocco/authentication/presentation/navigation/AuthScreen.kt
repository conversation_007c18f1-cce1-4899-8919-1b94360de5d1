package se.scmv.morocco.authentication.presentation.navigation

const val ARG_PHONE_NUMBER = "arg_phone_number"
const val ARG_OTP_VALIDATION_REASON = "arg_otp_validation_reason"
const val ARG_OTP_CODE = "arg_otp_code"

enum class AuthScreen(val route: String) {
    PRIVATE_ACCOUNT_AUTH("private_account_auth"),
    PRIVATE_ACCOUNT_SIGN_UP_OTP_VALIDATION("private_account_sign_up_otp_validation/{$ARG_PHONE_NUMBER}/{$ARG_OTP_VALIDATION_REASON}"),
    PRIVATE_ACCOUNT_SIGN_UP_COMPLETE_INFO("private_account_sign_up_complete_info/{$ARG_PHONE_NUMBER}?$ARG_OTP_CODE={$ARG_OTP_CODE}"),
    SHOP_ACCOUNT_SIGN_IN("shop_account_sign_in"),
    SHOP_ACCOUNT_SIGN_UP("shop_account_sign_up"),
    SHOP_ACCOUNT_SIGN_UP_SUCCESS("shop_account_sign_up_success"),
    RESET_PASSWORD("reset_password"),
    RESET_PASSWORD_PHONE_OTP_VALIDATION("reset_password_phone_otp_validation/{$ARG_PHONE_NUMBER}/{$ARG_OTP_VALIDATION_REASON}"),
    RESET_PASSWORD_PHONE_UPDATE("reset_password_phone_update/{$ARG_OTP_CODE}"),
    RESET_PASSWORD_PHONE_SUCCESS("reset_password_phone_success"),
    RESET_PASSWORD_EMAIL_SUCCESS("reset_password_email_success");

    companion object {
        val NAV_GRAPH_ROUTE = "auth_graph"
    }
}


package se.scmv.morocco.authentication.presentation.private_account.merged

import androidx.compose.runtime.Stable
import se.scmv.morocco.designsystem.utils.UiText

@Stable
data class PrivateAccountMergedViewState(
    val phoneNumber: String = "",
    val phoneNumberError: UiText? = null,
    val selectedCountry: Country? = Country(
        flag = "🇲🇦",
        displayName = "Maroc",
        phonePrefix = "+212"
    ),
    val password: String = "",
    val passwordError: UiText? = null,
    val loading: Boolean = false,
    val checkingAccount: Boolean = false,
    val accountExists: <PERSON>olean? = null,
    val showPasswordField: Boolean = false,
    val showForgotPassword: <PERSON>olean = false,
    val showGoogleSignIn: Boolean = false,
)

data class Country(
    val flag: String,
    val displayName: String,
    val phonePrefix: String
)

sealed interface PrivateAccountMergedOneTimeEvents {
    data object Success: PrivateAccountMergedOneTimeEvents

    data class Otp(
        val phone: String
    ) : PrivateAccountMergedOneTimeEvents
}
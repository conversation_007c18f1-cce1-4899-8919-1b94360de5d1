package se.scmv.morocco.authentication.presentation.shop_account.signin

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.Button
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedCard
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.hilt.navigation.compose.hiltViewModel
import se.scmv.morocco.analytics.TrackScreenViewEvent
import se.scmv.morocco.analytics.models.AnalyticsEvent
import se.scmv.morocco.analytics.models.Param
import se.scmv.morocco.authentication.R
import se.scmv.morocco.authentication.presentation.common.TestTags
import se.scmv.morocco.designsystem.components.AvPasswordField
import se.scmv.morocco.designsystem.components.AvPrimaryButton
import se.scmv.morocco.designsystem.components.AvScreenSubTitle
import se.scmv.morocco.designsystem.components.AvScreenTitle
import se.scmv.morocco.designsystem.components.AvTextField
import se.scmv.morocco.designsystem.components.AvTopAppBar
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.dimens

@Composable
fun ShopAccountSignInRoute(
    modifier: Modifier = Modifier,
    viewModel: ShopAccountSignInViewModel = hiltViewModel(),
    navigateToStoreSignUp: () -> Unit,
    navigateToResetPassword: () -> Unit,
    navigateBack: () -> Unit,
    navigateToHome: () -> Unit,
    navigateToWebViewScreen: (String, String) -> Unit,
) {
    val state = viewModel.viewState.collectAsState().value
    var showEcommerceDialog by remember { mutableStateOf(false) }
    Column(modifier = modifier.fillMaxSize()) {
        AvTopAppBar(onNavigationIconClicked = navigateBack)
        ShopAccountSignInScreen(
            modifier = Modifier.fillMaxSize(),
            state = state,
            onEmailChanged = viewModel::onEmailChanged,
            onPasswordChanged = viewModel::onPasswordChanged,
            onPasswordForgottenClicked = navigateToResetPassword,
            onSubmit = viewModel::onSubmit,
            onGoToStoreSignInBtnClicked = navigateToStoreSignUp
        )
    }
    
    // Ecommerce restriction dialog
    if (showEcommerceDialog) {
        Dialog(
            properties = DialogProperties(
                dismissOnBackPress = true,
                dismissOnClickOutside = false
            ),
            onDismissRequest = {
                showEcommerceDialog = false
            }
        ) {
            OutlinedCard {
                Column(
                    modifier = Modifier
                        .padding(MaterialTheme.dimens.big)
                        .fillMaxWidth()
                        .background(color = MaterialTheme.colorScheme.background),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium)
                ) {
                    // Warning icon
                    Icon(
                        modifier = Modifier.size(48.dp),
                        imageVector = Icons.Default.Warning,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.error
                    )
                    
                    // Title
                    Text(
                        text = stringResource(R.string.ecommerce_store_owner_restriction_title),
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        textAlign = TextAlign.Center
                    )
                    
                    // Message
                    Text(
                        text = stringResource(R.string.ecommerce_store_owner_restriction_message),
                        style = MaterialTheme.typography.bodyMedium,
                        textAlign = TextAlign.Center
                    )
                    
                    // Buttons
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium)
                    ) {
                        // Cancel button
                        TextButton(
                            onClick = {
                                showEcommerceDialog = false
                            },
                            modifier = Modifier.weight(1f)
                        ) {
                            Text(stringResource(R.string.ok))
                        }
                        val url = stringResource(R.string.ecommerce_store_signin_url)
                        val title = stringResource(R.string.ecommerce_store_owner_restriction_title)
                        // Open web button
                        Button(
                            onClick = {
                                showEcommerceDialog = false
                                navigateToWebViewScreen(title, url)
                            },
                            modifier = Modifier.weight(1f)
                        ) {
                            Text(stringResource(R.string.ecommerce_store_owner_restriction_open_web))
                        }
                    }
                }
            }
        }
    }
    
    LaunchedEffect(key1 = Unit) {
        viewModel.oneTimeEvents.collect { event ->
            when (event) {
                is ShopAccountSignInEvent.Success -> {
                    navigateToHome()
                }
                is ShopAccountSignInEvent.EcommerceRestriction -> {
                    showEcommerceDialog = true
                }
            }
        }
    }
    TrackScreenViewEvent(
        screenName = AnalyticsEvent.ScreensNames.LOGIN,
        properties = setOf(
            Param(
                key = AnalyticsEvent.ParamKeys.ACCOUNT_TYPE,
                value = AnalyticsEvent.ParamValues.ACCOUNT_TYPE_SHOP
            )
        )
    )
}

// Removed external browser opener; navigation to in-app WebView is handled via navigateToWebViewScreen

@Composable
fun ShopAccountSignInScreen(
    modifier: Modifier = Modifier,
    state: ShopAccountSignInViewState,
    onEmailChanged: (String) -> Unit,
    onPasswordChanged: (String) -> Unit,
    onPasswordForgottenClicked: () -> Unit,
    onSubmit: () -> Unit,
    onGoToStoreSignInBtnClicked: () -> Unit
) {
    Box(
        modifier = modifier
            .fillMaxSize()
            .padding(horizontal = MaterialTheme.dimens.screenPaddingHorizontal)
            .padding(vertical = MaterialTheme.dimens.screenPaddingVertical)
    ) {
        ShopAccountSignInForm(
            modifier = modifier,
            state = state,
            onEmailChanged = onEmailChanged,
            onPasswordChanged = onPasswordChanged,
            onPasswordForgottenClicked = onPasswordForgottenClicked,
            onSubmit = onSubmit,
        )
    }
}

@Preview
@Composable
private fun ShopAccountSignInScreenPreview() {
    var state by remember { mutableStateOf(ShopAccountSignInViewState()) }
    AvitoTheme {
        Surface {
            ShopAccountSignInScreen(
                state = state,
                onEmailChanged = {
                    state = state.copy(email = it)
                },
                onPasswordChanged = {
                    state = state.copy(password = it)
                },
                onPasswordForgottenClicked = {},
                onSubmit = {
                    state = state.copy(loading = true)
                },
                onGoToStoreSignInBtnClicked = {}
            )
        }
    }
}

@Composable
fun ShopAccountSignInForm(
    modifier: Modifier = Modifier,
    state: ShopAccountSignInViewState,
    onEmailChanged: (String) -> Unit,
    onPasswordChanged: (String) -> Unit,
    onPasswordForgottenClicked: () -> Unit,
    onSubmit: () -> Unit
) {
    val context = LocalContext.current
    val focusManager = LocalFocusManager.current

    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.large)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = MaterialTheme.dimens.big),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.large)
        ) {
            AvScreenTitle(title = R.string.shop_account_sign_in_screen_title)
            AvScreenSubTitle(title = R.string.shop_account_sign_in_screen_subtitle)
        }
        AvTextField(
            modifier = Modifier
                .fillMaxWidth()
                .testTag(TestTags.TEST_TAG_EMAIL),
            value = state.email,
            onValueChanged = onEmailChanged,
            title = R.string.common_email_field_label,
            placeholder = R.string.common_email_field_placeholder,
            error = state.emailError?.getValue(context),
            keyboardOptions = KeyboardOptions(
                keyboardType = KeyboardType.Email,
                imeAction = ImeAction.Next
            )
        )
        AvPasswordField(
            modifier = Modifier
                .fillMaxWidth()
                .testTag(TestTags.TEST_TAG_PASSWORD),
            value = state.password,
            onValueChanged = onPasswordChanged,
            title = R.string.common_password_field_label,
            placeholder = R.string.common_password_field_placeholder,
            error = state.passwordError?.getValue(context),
            keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
            keyboardActions = KeyboardActions(
                onDone = {
                    focusManager.clearFocus()
                    onSubmit()
                }
            )
        )
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = MaterialTheme.dimens.medium),
            horizontalAlignment = Alignment.End
        ) {
            Text(
                modifier = Modifier.clickable(onClick = onPasswordForgottenClicked),
                text = stringResource(id = R.string.shop_account_sign_in_screen_password_forgotten),
                color = MaterialTheme.colorScheme.primary,
                fontWeight = FontWeight.Medium
            )
        }
        AvPrimaryButton(
            modifier = Modifier.fillMaxWidth(),
            text = stringResource(R.string.shop_account_sign_in_screen_submit_button),
            loading = state.loading,
            onClick = {
                focusManager.clearFocus()
                onSubmit()
            }
        )
    }
}
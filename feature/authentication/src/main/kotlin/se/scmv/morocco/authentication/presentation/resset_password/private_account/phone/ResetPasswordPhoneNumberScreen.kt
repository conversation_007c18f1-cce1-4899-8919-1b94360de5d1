package se.scmv.morocco.authentication.presentation.resset_password.private_account.phone

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import se.scmv.morocco.analytics.TrackScreenViewEvent
import se.scmv.morocco.analytics.models.AnalyticsEvent
import se.scmv.morocco.analytics.models.Param
import se.scmv.morocco.authentication.R
import se.scmv.morocco.authentication.presentation.common.TestTags
import se.scmv.morocco.designsystem.components.AvPrimaryButton
import se.scmv.morocco.designsystem.components.AvScreenSubTitle
import se.scmv.morocco.designsystem.components.AvTextField
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.dimens

@Composable
fun ResetPasswordPhoneNumberRoute(
    modifier: Modifier = Modifier,
    viewModel: ResetPasswordPhoneNumberViewModel = hiltViewModel(),
    navigateToOtpValidation: (String) -> Unit
) {
    val state = viewModel.viewState.collectAsState().value
    ResetPasswordPhoneNumberScreen(
        modifier = modifier,
        state = state,
        onPhoneNumberChanged = viewModel::onPhoneNumberChanged,
        onSubmit = viewModel::onSubmit,
    )
    LaunchedEffect(Unit) {
        viewModel.oneTimeEvents.collect { navigateToOtpValidation(it.phone) }
    }
    TrackScreenViewEvent(
        screenName = AnalyticsEvent.ScreensNames.PASSWORD_RECOVERY,
        properties = setOf(
            Param(
                key = AnalyticsEvent.ParamKeys.ACCOUNT_TYPE,
                value = AnalyticsEvent.ParamValues.ACCOUNT_TYPE_PRIVATE
            )
        )
    )
}

@Composable
private fun ResetPasswordPhoneNumberScreen(
    modifier: Modifier = Modifier,
    state: ResetPasswordPhoneNumberViewState,
    onPhoneNumberChanged: (String) -> Unit,
    onSubmit: () -> Unit,
) {
    val context = LocalContext.current
    val focusManager = LocalFocusManager.current
    Column (
        modifier = modifier
            .fillMaxSize()
            .padding(horizontal = MaterialTheme.dimens.screenPaddingHorizontal)
            .padding(bottom = MaterialTheme.dimens.screenPaddingHorizontal),
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.extraExtraBig)
    ) {
        Column(
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.extraBig)
        ) {
            AvScreenSubTitle(title = R.string.reset_password_phone_number_screen_subtitle)
            AvTextField(
                modifier = Modifier
                    .testTag(TestTags.TEST_TAG_PHONE_NUMBER)
                    .fillMaxWidth(),
                value = state.phoneNumber,
                onValueChanged = onPhoneNumberChanged,
                required = true,
                title = R.string.common_phone_number_field_label,
                placeholder = R.string.common_phone_number_field_placeholder,
                error = state.phoneNumberError?.getValue(context),
                keyboardOptions = KeyboardOptions(
                    imeAction = ImeAction.Done,
                    keyboardType = KeyboardType.Phone
                ),
                keyboardActions = KeyboardActions(
                    onDone = {
                        focusManager.clearFocus()
                        onSubmit()
                    }
                )
            )
        }
        AvPrimaryButton(
            modifier = Modifier.fillMaxWidth(),
            text = stringResource(R.string.common_continue),
            loading = state.loading,
            onClick = {
                focusManager.clearFocus()
                onSubmit()
            }
        )
    }
}

@Preview
@Composable
private fun ResetPasswordPhoneNumberScreenPreview() {
    AvitoTheme {
        Surface {
            ResetPasswordPhoneNumberScreen(
                state = ResetPasswordPhoneNumberViewState(),
                onPhoneNumberChanged = {},
                onSubmit = {},
            )
        }
    }
}
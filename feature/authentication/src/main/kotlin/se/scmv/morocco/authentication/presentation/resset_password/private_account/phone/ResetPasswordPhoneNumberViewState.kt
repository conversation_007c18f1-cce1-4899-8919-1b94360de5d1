package se.scmv.morocco.authentication.presentation.resset_password.private_account.phone

import androidx.compose.runtime.Stable
import se.scmv.morocco.designsystem.utils.UiText

@Stable
data class ResetPasswordPhoneNumberViewState(
    val phoneNumber: String = "",
    val phoneNumberError: UiText? = null,
    val loading: Boolean = false
)

@JvmInline
value class ResetPasswordPhoneNumberSuccessEvent(val phone: String)

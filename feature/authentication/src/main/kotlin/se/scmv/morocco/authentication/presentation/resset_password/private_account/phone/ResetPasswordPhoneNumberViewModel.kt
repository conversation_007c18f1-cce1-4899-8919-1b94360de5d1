package se.scmv.morocco.authentication.presentation.resset_password.private_account.phone

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import se.scmv.morocco.authentication.R
import se.scmv.morocco.authentication.domain.validators.CredentialsValidator
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.domain.models.NetworkAndBackendErrors
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.repositories.AuthenticationRepository
import se.scmv.morocco.ui.renderFailure
import javax.inject.Inject

@HiltViewModel
class ResetPasswordPhoneNumberViewModel @Inject constructor(
    private val credentialsValidator: CredentialsValidator,
    private val authenticationRepository: AuthenticationRepository
) : ViewModel() {

    private val _viewState = MutableStateFlow(ResetPasswordPhoneNumberViewState())
    val viewState = _viewState.asStateFlow()

    private val _oneTimeEvents = MutableSharedFlow<ResetPasswordPhoneNumberSuccessEvent>()
    val oneTimeEvents = _oneTimeEvents.asSharedFlow()

    fun onPhoneNumberChanged(phoneNumber: String) {
        _viewState.update { it.copy(phoneNumber = phoneNumber, phoneNumberError = null) }
    }

    fun onSubmit() {
        if (validateForm().not()) return

        val phoneNumber = _viewState.value.phoneNumber
        showLoading()
        viewModelScope.launch {
            val result = authenticationRepository.sendSmsVerificationForPasswordReset(phoneNumber)
            hideLoading()
            updateFor(phoneNumber, result)
        }
    }

    private suspend fun updateFor(
        phoneNumber: String,
        result: Resource<String, NetworkAndBackendErrors>
    ) {
        when (result) {
            is Resource.Success -> _oneTimeEvents.emit(
                ResetPasswordPhoneNumberSuccessEvent(phone = phoneNumber)
            )

            is Resource.Failure -> renderFailure(error = result.error)
        }
    }

    private fun validateForm(): Boolean {
        val phoneNumber = _viewState.value.phoneNumber
        when {
            phoneNumber.isBlank() -> R.string.common_phone_number_field_required
            credentialsValidator.validatePhone(phoneNumber).not() ->
                R.string.common_phone_number_field_format_invalid

            else -> null
        }?.let { errorMsg ->
            _viewState.update { it.copy(phoneNumberError = UiText.FromRes(errorMsg)) }
            return false
        }

        return true
    }

    private fun showLoading() {
        _viewState.update { it.copy(loading = true) }
    }

    private fun hideLoading() {
        _viewState.update { it.copy(loading = false) }
    }
}
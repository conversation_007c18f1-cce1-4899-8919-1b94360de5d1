package se.scmv.morocco.authentication.presentation.resset_password.private_account.update

import androidx.compose.runtime.Stable
import se.scmv.morocco.designsystem.utils.UiText

@Stable
data class PasswordResetViewState(
    val password: String = "",
    val passwordError: UiText?  = null,
    val passwordConfirm: String = "",
    val passwordConfirmError: UiText?  = null,
    val loading: Boolean = false
)
package se.scmv.morocco.authentication.presentation.common

import androidx.annotation.StringRes
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import se.scmv.morocco.authentication.R
import se.scmv.morocco.designsystem.components.AvPrimaryButton
import se.scmv.morocco.designsystem.components.AvScreenSubTitle
import se.scmv.morocco.designsystem.components.AvScreenTitle
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.Black
import se.scmv.morocco.designsystem.theme.White
import se.scmv.morocco.designsystem.theme.dimens

@Composable
fun SuccessScreen(
    modifier: Modifier = Modifier,
    @StringRes title: Int? = null,
    @StringRes subtitle: Int? = null,
    onClose: () -> Unit,
    onContinue: () -> Unit,
) {
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.primary)
            .padding(horizontal = MaterialTheme.dimens.screenPaddingHorizontal)
            .padding(bottom = MaterialTheme.dimens.screenPaddingHorizontal)
    ) {
        // NB: we're hardcoding the White color here because we're sure that it will be always white
        IconButton(
            modifier = Modifier.align(Alignment.TopEnd),
            onClick = onClose,
            colors = IconButtonDefaults.filledIconButtonColors(
                containerColor = White.copy(alpha = 0.2f)
            )
        ) {
            Icon(
                imageVector = Icons.Default.Close,
                contentDescription = "Close button",
                tint = White
            )
        }
        Column(
            modifier = Modifier.align(Alignment.Center),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            title?.let {
                AvScreenTitle(
                    title = it,
                    color = White,
                    textAlign = TextAlign.Center
                )
            }
            Spacer(modifier = Modifier.height(MaterialTheme.dimens.medium))
            subtitle?.let {
                AvScreenSubTitle(
                    title = it,
                    color = White,
                    textAlign = TextAlign.Center
                )
            }
            Spacer(modifier = Modifier.height(MaterialTheme.dimens.big))
            Icon(
                modifier = Modifier.size(123.dp),
                painter = painterResource(R.drawable.ic_check_circle),
                contentDescription = null,
                tint = White
            )
        }
        AvPrimaryButton(
            modifier = Modifier
                .fillMaxWidth()
                .align(Alignment.BottomCenter),
            text = stringResource(R.string.common_continue),
            colors = ButtonDefaults.buttonColors(
                containerColor = White,
                contentColor = Black
            ),
            onClick = onContinue
        )
    }
}

@Preview
@Composable
private fun SuccessScreenPreview() {
    AvitoTheme {
        Surface {
            SuccessScreen(
                title = R.string.shop_account_sign_up_success_screen_title,
                subtitle = R.string.shop_account_sign_up_success_screen_subtitle,
                onClose = {},
                onContinue = {}
            )
        }
    }
}
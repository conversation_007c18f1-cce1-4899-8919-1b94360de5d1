package se.scmv.morocco.authentication.presentation.resset_password.shop_account

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import se.scmv.morocco.authentication.R
import se.scmv.morocco.authentication.domain.validators.CredentialsValidator
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.domain.models.NetworkAndBackendErrors
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.repositories.AuthenticationRepository
import se.scmv.morocco.ui.renderFailure
import javax.inject.Inject

@HiltViewModel
class ResetPasswordEmailViewModel @Inject constructor(
    private val authenticationRepository: AuthenticationRepository,
    private val credentialsValidator: CredentialsValidator
) : ViewModel() {
    private val _viewState = MutableStateFlow(ResetPasswordEmailViewState())
    val viewState = _viewState.asStateFlow()

    private val _oneTimeEvents = MutableSharedFlow<ResetPasswordEmailSuccessEvent>()
    val oneTimeEvents = _oneTimeEvents.asSharedFlow()

    fun onEmailChanged(email: String) {
        _viewState.update { it.copy(email = email, emailError = null) }
    }

    fun onSubmit() {
        if (validateForm().not()) return

        val email = _viewState.value.email
        showLoading()
        viewModelScope.launch {
            val result = authenticationRepository.sendPasswordResetLink(email)
            hideLoading()
            updateFor(email, result)
        }
    }

    private suspend fun updateFor(
        email: String,
        result: Resource<String, NetworkAndBackendErrors>
    ) {
        when (result) {
            is Resource.Success -> _oneTimeEvents.emit(ResetPasswordEmailSuccessEvent(email))

            is Resource.Failure -> renderFailure(error = result.error)
        }
    }

    private fun validateForm(): Boolean {
        val email = _viewState.value.email
        when {
            email.isBlank() -> R.string.common_email_field_required
            credentialsValidator.validateEmail(email).not() ->
                R.string.common_email_field_format_invalid

            else -> null
        }?.let { errorMsg ->
            _viewState.update { it.copy(emailError = UiText.FromRes(errorMsg)) }
            return false
        }

        return true
    }

    private fun showLoading() {
        _viewState.update { it.copy(loading = true) }
    }

    private fun hideLoading() {
        _viewState.update { it.copy(loading = false) }
    }
}
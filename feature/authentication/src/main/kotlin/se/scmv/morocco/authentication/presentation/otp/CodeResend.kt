package se.scmv.morocco.authentication.presentation.otp

import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.ViewModelComponent

class CodeResendTimeout(val timeout: Int)

@Module
@InstallIn(ViewModelComponent::class)
object CodeResendModule {

    @Provides
    fun provideCodeResendTimeOut(): CodeResendTimeout {
        return CodeResendTimeout(60)
    }
}
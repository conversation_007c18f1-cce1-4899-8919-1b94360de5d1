package se.scmv.morocco.authentication.presentation.private_account.signup.phone

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import se.scmv.morocco.authentication.R
import se.scmv.morocco.authentication.domain.validators.CredentialsValidator
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.domain.models.NetworkAndBackendErrors
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.repositories.AuthenticationRepository
import se.scmv.morocco.ui.renderFailure
import javax.inject.Inject

@HiltViewModel
class PrivateAccountSignUpViewModel @Inject constructor(
    private val credentialsValidator: CredentialsValidator,
    private val authenticationRepository: AuthenticationRepository
) : ViewModel() {

    private val _viewState = MutableStateFlow(PrivateAccountSignUpViewState())
    val viewState = _viewState.asStateFlow()

    private val _oneTimeEvents = MutableSharedFlow<PrivateAccountSignUpSuccessEvent>()
    val oneTimeEvents = _oneTimeEvents.asSharedFlow()

    fun onPhoneNumberChanged(phoneNumber: String) {
        _viewState.update { it.copy(phoneNumber = phoneNumber, phoneError = null) }
    }

    fun onTosAndPpConfirmChanged(checked: Boolean) {
        _viewState.update { it.copy(confirmTosAndPp = checked, confirmTosAndPpError = null) }
    }

    fun onSubmit() {
        clearErrors()
        if (validateForm().not()) return

        showLoading()
        val phoneNumber = _viewState.value.phoneNumber
        viewModelScope.launch {
            val result = authenticationRepository.sendSmsVerificationForSignUp(phoneNumber)
            hideLoading()
            updateForResult(result)
        }
    }

    private fun validateForm(): Boolean {
        val phoneNumber = _viewState.value.phoneNumber
        when {
            phoneNumber.isBlank() -> R.string.common_phone_number_field_required
            credentialsValidator.validatePhone(phoneNumber)
                .not() -> R.string.common_phone_number_field_format_invalid

            else -> null
        }?.let { errorMsg ->
            _viewState.update { it.copy(phoneError = UiText.FromRes(errorMsg)) }
            return false
        }

        val confirmTosAndPp = _viewState.value.confirmTosAndPp
        if (confirmTosAndPp.not()) {
            _viewState.update {
                it.copy(confirmTosAndPpError = R.string.common_tos_and_pp_field_required)
            }
            return false
        }

        return true
    }

    private suspend fun updateForResult(
        result: Resource<String, NetworkAndBackendErrors>
    ) {
        when (result) {
            is Resource.Success -> _oneTimeEvents.emit(PrivateAccountSignUpSuccessEvent(result.data))

            is Resource.Failure -> renderFailure(error = result.error)
        }
    }

    private fun clearErrors() {
        _viewState.update { it.copy(phoneError = null) }
    }

    private fun showLoading() {
        _viewState.update { it.copy(loading = true) }
    }

    private fun hideLoading() {
        _viewState.update { it.copy(loading = false) }
    }
}
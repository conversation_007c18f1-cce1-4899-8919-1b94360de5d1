package se.scmv.morocco.authentication.presentation

import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.consumeWindowInsets
import androidx.compose.foundation.layout.safeDrawingPadding
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.ui.Modifier
import androidx.navigation.compose.rememberNavController
import dagger.hilt.android.AndroidEntryPoint
import se.scmv.morocco.analytics.AnalyticsHelper
import se.scmv.morocco.analytics.LocalAnalyticsHelper
import se.scmv.morocco.authentication.presentation.common.LoginType
import se.scmv.morocco.authentication.presentation.navigation.AuthNavHost
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.ui.SnackBarHostForSnackBarController
import javax.inject.Inject

@AndroidEntryPoint
class AuthenticationActivity : ComponentActivity() {

    @Inject
    lateinit var analyticsHelper: AnalyticsHelper

    companion object {
        const val TOKEN_KEY = "key_access_token"
        const val LOGIN_TYPE_KEY = "key_login_type"

        const val REQUEST_SIGN_IN = 0x376
        const val REQUEST_SIGN_IN_FILTERS = 0x382
        const val REQUEST_SIGN_IN_ECOMMERCE = 0x376
        const val REQUEST_SIGN_IN_FAVORITES_STATS = 0x379
        const val REQUEST_SIGN_AD_INSERTION = 0x381
        const val REQUEST_SIGN_IN_MESSAGING = 0x377
        const val REQUEST_SIGN_IN_MY_ACCOUNT = 0x378
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            CompositionLocalProvider(LocalAnalyticsHelper provides analyticsHelper) {
                AvitoTheme {
                    Scaffold(
                        snackbarHost = { SnackBarHostForSnackBarController() }
                    ) { innerPadding ->
                        val navController = rememberNavController()
                        AuthNavHost(
                            modifier = Modifier
                                .safeDrawingPadding()
                                .consumeWindowInsets(innerPadding),
                            navController = navController,
                            navigateBack = {
                                onBackPressedDispatcher.onBackPressed()
                            },
                            navigateToHome = ::resumeWithToken,
                            navigateToWebViewScreen = {_,_ ->}
                        )
                    }
                }
            }
        }
    }

    private fun resumeWithToken(loginType: LoginType) {
        setResult(
            RESULT_OK,
            Intent().apply {
                putExtra(TOKEN_KEY, "token")
                putExtra(LOGIN_TYPE_KEY, loginType.name)
            }
        )
        finish()
    }
}
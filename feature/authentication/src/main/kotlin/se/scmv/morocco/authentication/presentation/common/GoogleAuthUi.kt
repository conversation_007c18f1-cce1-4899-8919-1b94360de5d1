package se.scmv.morocco.authentication.presentation.common

import android.content.Context
import android.content.Intent
import android.content.IntentSender
import android.widget.Toast
import com.google.android.gms.auth.api.identity.BeginSignInRequest
import com.google.android.gms.auth.api.identity.SignInClient
import com.google.android.gms.common.api.ApiException
import kotlinx.coroutines.tasks.await
import se.scmv.morocco.authentication.R

class GoogleAuthUi(
    private val context: Context,
    private val oneTapClient: SignInClient
) {
    suspend fun signIn(): IntentSender? {
        return auth()
    }

    private suspend fun auth(): IntentSender? {
        return try {
            val result = oneTapClient.beginSignIn(createSignInRequest()).await()
            result?.pendingIntent?.intentSender
        } catch (e: Exception) {
            e.printStackTrace()
            Toast.makeText(
                context,
                R.string.common_unexpected_error_verify_and_try_later,
                Toast.LENGTH_SHORT
            ).show()
            null
        }
    }

    fun getGoogleIdToken(intent: Intent): String? {
        return try {
            oneTapClient.getSignInCredentialFromIntent(intent).googleIdToken
        } catch (e: ApiException) {
            e.printStackTrace()
            if (e.status.isCanceled.not()) {
                Toast.makeText(
                    context,
                    R.string.common_unexpected_error_verify_and_try_later,
                    Toast.LENGTH_SHORT
                ).show()
            }
            null
        }
    }

    private fun createSignInRequest(): BeginSignInRequest = BeginSignInRequest.builder()
        .setPasswordRequestOptions(
            BeginSignInRequest.PasswordRequestOptions.builder()
                .setSupported(true)
                .build()
        )
        .setGoogleIdTokenRequestOptions(
            BeginSignInRequest.GoogleIdTokenRequestOptions.builder()
                .setSupported(true)
                // Your server's client ID, not your Android client ID.
                .setServerClientId("************-vts29faf120vna9m3o2qv5vpqh9jtl4p.apps.googleusercontent.com")
                // Only show accounts previously used to sign in.
                .setFilterByAuthorizedAccounts(false)
                .build()
        )
        .build()

    // TODO We should use this credentials manager implementation instead of the above implementation.
    /*private fun launchGoogleSignIn(
        context: Context,
        scope: CoroutineScope,
        onResult: (Result<GetCredentialResponse>) -> Unit
    ) {
        val googleIdOption: GetGoogleIdOption = GetGoogleIdOption.Builder()
            .setFilterByAuthorizedAccounts(false)
            .setServerClientId("************-sf0sf8kj6ber8nfq16ha2ff3agj8quqe.apps.googleusercontent.com")
            .setAutoSelectEnabled(true)
            .build()

        val request: GetCredentialRequest = GetCredentialRequest.Builder()
            .addCredentialOption(googleIdOption)
            .build()
        val credentialManager = CredentialManager.create(context)
        scope.launch {
            try {
                val result = credentialManager.getCredential(context, request)
                onResult(Result.success(result))
            } catch (e: GetCredentialException) {
                e.printStackTrace()
                onResult(Result.failure(e))
            }
        }
    }*/
}

package se.scmv.morocco.authentication.presentation.private_account.signup.phone

import androidx.compose.runtime.Stable
import se.scmv.morocco.designsystem.utils.UiText

@Stable
data class PrivateAccountSignUpViewState(
    val phoneNumber: String = "",
    val confirmTosAndPp: Boolean = true,
    val phoneError: UiText? = null,
    val confirmTosAndPpError: Int? = null,
    val loading: Boolean = false,
)

@JvmInline
value class PrivateAccountSignUpSuccessEvent(val phone: String)
package se.scmv.morocco.authentication.presentation.private_account.master

import kotlinx.collections.immutable.toImmutableList
import se.scmv.morocco.authentication.R
import se.scmv.morocco.designsystem.components.TabData

val privateAccountAuthTabs = listOf(
    TabData(text = R.string.private_account_auth_screen_sign_in_tab),
    TabData(text = R.string.private_account_auth_screen_sign_up_tab),
).toImmutableList()

enum class PrivateAccountAuthPages {
    SIGN_IN, SIGN_UP
}
package se.scmv.morocco.authentication.presentation.private_account.master

import androidx.compose.foundation.layout.consumeWindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import se.scmv.morocco.authentication.presentation.common.LoginType
import se.scmv.morocco.authentication.presentation.private_account.merged.PrivateAccountMergedRoute
import se.scmv.morocco.designsystem.components.AvTopAppBar

@Composable
fun PrivateAccountAuthRoute(
    modifier: Modifier = Modifier,
    navigateBack: () -> Unit,
    navigateToStoreSignIn: () -> Unit,
    navigateToResetPassword: () -> Unit,
    navigateToHome: (LoginType) -> Unit,
    navigateToOtpValidation: (String) -> Unit,
    navigateToWebViewScreen: (String, String) -> Unit,
) {
    Scaffold(
        modifier = modifier,
        topBar = {
            AvTopAppBar(onNavigationIconClicked = navigateBack)
        }
    ) { padding ->
        PrivateAccountMergedRoute(
            modifier = Modifier
                .fillMaxSize()
                .consumeWindowInsets(padding),
            navigateBack = {},
            navigateToResetPassword = navigateToResetPassword,
            navigateToOtpValidation = navigateToOtpValidation,
            navigateToStoreSignIn = navigateToStoreSignIn,
        )
    }
}



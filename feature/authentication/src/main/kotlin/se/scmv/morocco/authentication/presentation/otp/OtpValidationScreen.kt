package se.scmv.morocco.authentication.presentation.otp

import androidx.compose.foundation.Image
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.consumeWindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredSize
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import kotlinx.coroutines.flow.collectLatest
import se.scmv.morocco.authentication.R
import se.scmv.morocco.authentication.presentation.common.TestTags
import se.scmv.morocco.designsystem.components.AvPrimaryButton
import se.scmv.morocco.designsystem.components.AvScreenTitle
import se.scmv.morocco.designsystem.components.AvTopAppBar
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.dimens

@Composable
fun OtpValidationRoute(
    modifier: Modifier = Modifier,
    viewModel: OtpValidationViewModel = hiltViewModel(),
    navigateBack: () -> Unit,
    onOtpValidationSuccess: (OtpValidationSuccessEvent) -> Unit
) {
    Scaffold(
        modifier = modifier,
        topBar = { AvTopAppBar(onNavigationIconClicked = navigateBack) }
    ) {
        val state = viewModel.viewState.collectAsState().value
        OtpValidationScreen(
            modifier = modifier.consumeWindowInsets(it),
            state = state,
            onCodeChanged = viewModel::onCodeChanged,
            onResendCodeClicked = viewModel::onResendCodeClicked,
            onSkipValidationClicked = viewModel::onSkipValidationClicked,
            onSubmit = viewModel::onSubmit,
        )
    }
    LaunchedEffect(key1 = Unit) {
        viewModel.oneTimeEvents.collectLatest(onOtpValidationSuccess)
    }
}

@Composable
private fun OtpValidationScreen(
    modifier: Modifier = Modifier,
    state: OtpValidationViewState,
    onCodeChanged: (String) -> Unit,
    onResendCodeClicked: () -> Unit,
    onSkipValidationClicked: () -> Unit,
    onSubmit: () -> Unit,
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(
                horizontal = MaterialTheme.dimens.screenPaddingHorizontal,
                vertical = MaterialTheme.dimens.screenPaddingVertical
            ),
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.extraBig),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Column(
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.large)
        ) {
            AvScreenTitle(title = R.string.otp_validation_screen_title)
            OtpValidationScreenSubtitle(state)
        }
        Image(
            painter = painterResource(R.drawable.img_otp_illustration),
            contentDescription = null
        )
        OtpTextField(
            modifier = Modifier.fillMaxWidth(),
            otpText = state.code,
            otpError = state.codeError?.getValue(LocalContext.current),
            onOtpTextChange = { otp, completed ->
                onCodeChanged(otp)
                if (completed) {
                    onSubmit()
                }
            }
        )
        Column(
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small)
        ) {
            val focusManager = LocalFocusManager.current
            OtpValidationScreenResendCode(
                resendTimer = state.resendTimer,
                isResendButtonEnabled = state.isResendButtonEnabled,
                onClick = {
                    focusManager.clearFocus()
                    onResendCodeClicked()
                }
            )
            if (state.isSkipValidationButtonEnabled) {
                OtpValidationScreenSkipValidation(onClick = onSkipValidationClicked)
            }
        }
        AvPrimaryButton(
            modifier = Modifier.fillMaxWidth(),
            text = stringResource(R.string.common_verify),
            loading = state.loading,
            onClick = onSubmit
        )
    }
}

@Composable
private fun OtpValidationScreenSubtitle(state: OtpValidationViewState) {
    val spannedString = buildAnnotatedString {
        withStyle(
            style = SpanStyle(
                fontFamily = FontFamily(Font(R.font.roboto_regular)),
                fontSize = MaterialTheme.typography.bodyMedium.fontSize
            )
        ) { append(stringResource(R.string.otp_validation_screen_subtitle_part1)) }
        append(" ")
        withStyle(
            style = SpanStyle(
                fontSize = MaterialTheme.typography.titleSmall.fontSize,
                fontWeight = FontWeight.SemiBold
            )
        ) { append(state.phoneNumber) }
        append(" ")
        withStyle(
            style = SpanStyle(
                fontFamily = FontFamily(Font(R.font.roboto_regular)),
                fontSize = MaterialTheme.typography.bodyMedium.fontSize
            )
        ) { append(stringResource(R.string.otp_validation_screen_subtitle_part2)) }
    }
    Text(text = spannedString)
}

@Composable
private fun OtpValidationScreenResendCode(
    resendTimer: Int,
    isResendButtonEnabled: Boolean,
    onClick: () -> Unit
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = stringResource(R.string.otp_validation_screen_code_unreceived),
            style = MaterialTheme.typography.titleSmall,
            textAlign = TextAlign.Center
        )
        Text(
            modifier = Modifier.clickable(onClick = onClick),
            text = if (isResendButtonEnabled) {
                stringResource(R.string.otp_validation_screen_resend_code)
            } else stringResource(
                R.string.otp_validation_screen_resend_code_timer,
                resendTimer
            ),
            color = MaterialTheme.colorScheme.primary,
            style = MaterialTheme.typography.titleSmall,
            textAlign = TextAlign.Center
        )
    }
}

@Composable
private fun OtpValidationScreenSkipValidation(onClick: () -> Unit) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = stringResource(R.string.common_or),
            style = MaterialTheme.typography.titleSmall,
            textAlign = TextAlign.Center
        )
        Text(
            modifier = Modifier.clickable(onClick = onClick),
            text = stringResource(R.string.otp_validation_screen_skip_validation),
            color = MaterialTheme.colorScheme.primary,
            style = MaterialTheme.typography.titleSmall,
            textAlign = TextAlign.Center
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun OtpValidationScreenPreview() {
    AvitoTheme {
        Surface {
            var state = remember {
                OtpValidationViewState(
                    phoneNumber = "06666666",
                    isResendButtonEnabled = false,
                    resendTimer = 30,
                    isSkipValidationButtonEnabled = true
                )
            }
            OtpValidationScreen(
                state = state,
                onCodeChanged = {
                    state = state.copy(code = it)
                },
                onResendCodeClicked = {},
                onSkipValidationClicked = {},
                onSubmit = {
                    state = state.copy(loading = true)
                },
            )
        }
    }
}

@Composable
private fun OtpTextField(
    modifier: Modifier = Modifier,
    otpText: String,
    otpError: String? = null,
    otpCount: Int = 6,
    onOtpTextChange: (String, Boolean) -> Unit
) {
    LaunchedEffect(Unit) {
        if (otpText.length > otpCount) {
            throw IllegalArgumentException("Otp text value must not have more than otpCount: $otpCount characters")
        }
    }
    val focusManager = LocalFocusManager.current
    val focusRequester = remember { FocusRequester() }
    var textFieldLoaded by remember { mutableStateOf(false) }
    CompositionLocalProvider(LocalLayoutDirection provides LayoutDirection.Ltr) {
        BasicTextField(
            modifier = modifier
                .testTag(TestTags.TEST_TAG_OTP)
                .focusRequester(focusRequester)
                .onGloballyPositioned {
                    if (!textFieldLoaded) {
                        focusRequester.requestFocus() // IMPORTANT
                        textFieldLoaded = true // stop cyclic recompositions
                    }
                },
            value = TextFieldValue(otpText, selection = TextRange(otpText.length)),
            onValueChange = {
                if (it.text.length <= otpCount) {
                    onOtpTextChange.invoke(it.text, it.text.length == otpCount)
                }
            },
            keyboardOptions = KeyboardOptions(
                keyboardType = KeyboardType.NumberPassword,
                imeAction = ImeAction.Done
            ),
            keyboardActions = KeyboardActions(onDone = { focusManager.clearFocus() }),
            decorationBox = {
                Column(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Row(horizontalArrangement = Arrangement.Center) {
                        repeat(otpCount) { index ->
                            CharView(
                                modifier = Modifier,
                                index = index,
                                text = otpText
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                        }
                    }
                    otpError?.let {
                        Text(it, color = MaterialTheme.colorScheme.error)
                    }
                }
            }
        )
    }
}

@Composable
private fun CharView(
    modifier: Modifier = Modifier,
    index: Int,
    text: String
) {
    val isFocused = text.length == index
    val char = when {
        index < text.length -> text[index].toString()
        else -> "-"
    }
    Text(
        modifier = modifier
            .border(
                1.dp, when {
                    isFocused -> MaterialTheme.colorScheme.primary
                    else -> MaterialTheme.colorScheme.outline
                }, CircleShape
            )
            .requiredSize(45.dp)
            .padding(top = 10.dp),
        text = char,
        style = MaterialTheme.typography.titleLarge,
        color = if (isFocused) {
            MaterialTheme.colorScheme.primary
        } else {
            MaterialTheme.colorScheme.onSurface
        },
        textAlign = TextAlign.Center
    )
}


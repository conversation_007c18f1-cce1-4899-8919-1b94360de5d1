package se.scmv.morocco.authentication.presentation.shop_account.signup

import androidx.activity.ComponentActivity
import androidx.compose.ui.test.junit4.createAndroidComposeRule
import org.junit.Rule
import org.junit.Test
import se.scmv.morocco.domain.models.ShopCategory
import se.scmv.morocco.domain.models.ShopSubscription

/**
 * NB: Regarding the fields format validation, currently we've only one constraint:
 * Name shouldn't be empty. If the PM give us other constraints in the future we'll
 * add tests for them.
 */
class ShopAccountSignUpScreenCredentialsTest {

    @get:Rule val composeTestRule = createAndroidComposeRule<ComponentActivity>()

    @Test
    fun signup_categoryRequired() {
        launchShopAccountSignUpScreen(
            composeTestRule
        ) {
            chooseCategory(null)
            chooseSubscription(ShopSubscription.BASE)
            typeFullName("Jhon DOE")
            typePhoneNumber("**********")
            typeEmail("<EMAIL>")
            submit()
        } verify {
            categoryRequiredErrorIsDisplayed()
        }
    }

    @Test
    fun signup_subscriptionRequired() {
        launchShopAccountSignUpScreen(
            composeTestRule
        ) {
            chooseCategory(ShopCategory.IMMO)
            chooseSubscription(null)
            typeFullName("Jhon DOE")
            typePhoneNumber("**********")
            typeEmail("<EMAIL>")
            submit()
        } verify {
            subscriptionRequiredErrorIsDisplayed()
        }
    }

    @Test
    fun signup_fullNameRequired() {
        launchShopAccountSignUpScreen(
            composeTestRule
        ) {
            chooseCategory(ShopCategory.IMMO)
            chooseSubscription(ShopSubscription.BASE)
            typeFullName("")
            typePhoneNumber("**********")
            typeEmail("<EMAIL>")
            submit()
        } verify {
            fullNameRequiredErrorIsDisplayed()
        }
    }

    @Test
    fun signup_phoneNumberRequired() {
        launchShopAccountSignUpScreen(
            composeTestRule
        ) {
            chooseCategory(ShopCategory.IMMO)
            chooseSubscription(ShopSubscription.BASE)
            typeFullName("Jhon DOE")
            typePhoneNumber("")
            typeEmail("<EMAIL>")
            submit()
        } verify {
            phoneNumberRequiredErrorIsDisplayed()
        }
    }

    @Test
    fun signup_emailRequired() {
        launchShopAccountSignUpScreen(
            composeTestRule
        ) {
            chooseCategory(ShopCategory.IMMO)
            chooseSubscription(ShopSubscription.BASE)
            typeFullName("Jhon DOE")
            typePhoneNumber("**********")
            typeEmail("")
            submit()
        } verify {
            emailRequiredErrorIsDisplayed()
        }
    }

    @Test
    fun signup_tosAndPpAutoFiled() {
        launchShopAccountSignUpScreen(
            rule = composeTestRule,
        ) {
            // do nothing
        } verify {
            tosAndPpIsAlreadyChecked()
        }
    }

    @Test
    fun signup_tosAndPpConfirmationRequired() {
        launchShopAccountSignUpScreen(
            rule = composeTestRule,
        ) {
            chooseCategory(ShopCategory.IMMO)
            chooseSubscription(ShopSubscription.BASE)
            typeFullName("Jhon DOE")
            typePhoneNumber("**********")
            typeEmail("<EMAIL>")
            uncheckTosAndPp()
            submit()
        } verify {
            tosAndPpConfirmationRequiredErrorIsDisplayed()
        }
    }

    @Test
    fun signup_emailFormatInvalid() {
        launchShopAccountSignUpScreen(
            rule = composeTestRule,
        ) {
            chooseCategory(ShopCategory.IMMO)
            chooseSubscription(ShopSubscription.BASE)
            typeFullName("Jhon DOE")
            typePhoneNumber("**********")
            listOf(
                "example.com",
                "user@@example.com",
                "user@example@com",
                "user@exam!ple.com",
                "user@ example.com",
                "  <EMAIL>",
                "<EMAIL>  ",
                "üñîçødë@example.com",
                "user@.com",
                "<EMAIL>.",
            ).forEach {email ->
                typeEmail(email)
                submit()
                verify {
                    emailFormatInvalidErrorIsDisplayed()
                }
            }
        }
    }

    @Test
    fun signup_emailFormatValid() {
        launchShopAccountSignUpScreen(
            rule = composeTestRule,
        ) {
            chooseCategory(ShopCategory.IMMO)
            chooseSubscription(ShopSubscription.BASE)
            typeFullName("Jhon DOE")
            typePhoneNumber("**********")
            listOf(
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
            ).forEach {email ->
                typeEmail(email)
                submit()
                verify {
                    emailRequiredErrorIsNotDisplayed()
                    emailFormatInvalidErrorIsNotDisplayed()
                }
            }
        }
    }

    @Test
    fun signUp_phoneNumberFormatInvalid() {
        launchShopAccountSignUpScreen(
            rule = composeTestRule,
        ) {
            chooseCategory(ShopCategory.IMMO)
            chooseSubscription(ShopSubscription.BASE)
            typeFullName("Jhon DOE")
            typeEmail("<EMAIL>")
            listOf(
                "*********",
                "*********",
                "15*********",
                "**********",
            ).forEach { phone ->
                typePhoneNumber(phone)
                submit()
                verify {
                    phoneNumberFormatInvalidErrorIsDisplayed()
                }
            }
        }
    }

    @Test
    fun signUp_phoneNumberFormatValid() {
        launchShopAccountSignUpScreen(
            rule = composeTestRule,
        ) {
            chooseCategory(ShopCategory.IMMO)
            chooseSubscription(ShopSubscription.BASE)
            typeFullName("Jhon DOE")
            typeEmail("<EMAIL>")
            listOf(
                "*********8",
                "**********",
                "**********",
                "**********",
                "0*********1",
                "*************"
            ).forEach { phone ->
                typePhoneNumber(phone)
                submit()
                verify {
                    phoneNumberRequiredErrorIsNotDisplayed()
                    phoneNumberFormatInvalidErrorIsNotDisplayed()
                }
            }
        }
    }
}
package se.scmv.morocco.authentication.presentation.private_account.reset_password.phone

import androidx.activity.ComponentActivity
import androidx.compose.ui.test.junit4.createAndroidComposeRule
import org.junit.Rule
import org.junit.Test
import se.scmv.morocco.authentication.data.repository.AuthenticationRepositoryConfigurableImpl
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.models.NetworkAndBackendErrors
import se.scmv.morocco.domain.models.NetworkErrors

class ResetPasswordPhoneNumberEndTest {

    @get:Rule
    val composeTestRule = createAndroidComposeRule<ComponentActivity>()

    @Test
    fun resetPassword_BackendError() {
        launchResetPasswordPhoneNumberScreen(
            rule = composeTestRule,
            authenticationRepository = AuthenticationRepositoryConfigurableImpl(
                sendSmsVerificationForPasswordResetResponse = Resource.Failure(
                    NetworkAndBackendErrors.Backend(
                        AuthenticationRepositoryConfigurableImpl.PHONE_NUMBER_SIGN_UP_BACKEND_ERROR
                    )
                )
            )
        ) {
            typePhoneNumber("0610292828")
            submit()
        } verify {
            phoneHasNoError()
            backendErrorIsDisplayed()
        }
    }

    @Test
    fun resetPassword_NetworkError() {
        launchResetPasswordPhoneNumberScreen(
            rule = composeTestRule,
            authenticationRepository = AuthenticationRepositoryConfigurableImpl(
                sendSmsVerificationForPasswordResetResponse = Resource.Failure(
                    NetworkAndBackendErrors.Network(NetworkErrors.NO_INTERNET)
                )
            )
        ) {
            typePhoneNumber("0610292828")
            submit()
        } verify {
            phoneHasNoError()
            networkErrorIsDisplayed()
        }
    }

    @Test
    fun resetPassword_success() {
        launchResetPasswordPhoneNumberScreen(
            rule = composeTestRule,
            authenticationRepository = AuthenticationRepositoryConfigurableImpl()
        ) {
            typePhoneNumber("0610292828")
            submit()
        } verify {
            phoneHasNoError()
            networkErrorIsNotDisplayed()
            backendErrorIsNotDisplayed()
        }
    }
}
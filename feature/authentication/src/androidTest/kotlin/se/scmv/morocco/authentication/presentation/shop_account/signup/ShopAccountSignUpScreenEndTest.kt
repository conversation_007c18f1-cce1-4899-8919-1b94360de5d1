package se.scmv.morocco.authentication.presentation.shop_account.signup

import androidx.activity.ComponentActivity
import androidx.compose.ui.test.junit4.createAndroidComposeRule
import org.junit.Rule
import org.junit.Test
import se.scmv.morocco.authentication.data.repository.AuthenticationRepositoryConfigurableImpl
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.models.ShopCategory
import se.scmv.morocco.domain.models.ShopSubscription
import se.scmv.morocco.domain.models.NetworkAndBackendErrors
import se.scmv.morocco.domain.models.NetworkErrors

class ShopAccountSignUpScreenEndTest {

    @get:Rule
    val composeTestRule = createAndroidComposeRule<ComponentActivity>()

    @Test
    fun signup_backendError() {
        launchShopAccountSignUpScreen(
            rule = composeTestRule,
            authenticationRepository = AuthenticationRepositoryConfigurableImpl(
                shopSignUpResponse = Resource.Failure(
                    NetworkAndBackendErrors.Backend(
                        message = AuthenticationRepositoryConfigurableImpl.USERS_NAME_INVALID,
                    )
                )
            )
        ) {
            chooseCategory(ShopCategory.IMMO)
            chooseSubscription(ShopSubscription.BASE)
            typeFullName("Jhon DOE")
            typePhoneNumber("**********")
            typeEmail("<EMAIL>")
            submit()
        } verify {
            fullNameInvalidErrorIsDisplayed()
        }
    }

    @Test
    fun signup_NetworkError() {
        launchShopAccountSignUpScreen(
            rule = composeTestRule,
            authenticationRepository = AuthenticationRepositoryConfigurableImpl(
                shopSignUpResponse = Resource.Failure(
                    NetworkAndBackendErrors.Network(NetworkErrors.NO_INTERNET)
                )
            )
        ) {
            chooseCategory(ShopCategory.IMMO)
            chooseSubscription(ShopSubscription.BASE)
            typeFullName("Jhon DOE")
            typePhoneNumber("**********")
            typeEmail("<EMAIL>")
            submit()
        } verify {
            networkErrorIsDisplayed()
        }
    }

    @Test
    fun signup_success() {
        launchShopAccountSignUpScreen(
            rule = composeTestRule,
            authenticationRepository = AuthenticationRepositoryConfigurableImpl()
        ) {
            chooseCategory(ShopCategory.IMMO)
            chooseSubscription(ShopSubscription.BASE)
            typeFullName("Jhon DOE")
            typePhoneNumber("**********")
            typeEmail("<EMAIL>")
            submit()
        } verify {
            fullNameRequiredErrorIsNotDisplayed()
            phoneNumberRequiredErrorIsNotDisplayed()
            emailRequiredErrorIsNotDisplayed()
        }
    }
}
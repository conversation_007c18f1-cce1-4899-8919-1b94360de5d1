package se.scmv.morocco.authentication.presentation.private_account.reset_password.update

import androidx.activity.ComponentActivity
import androidx.compose.ui.test.junit4.createAndroidComposeRule
import org.junit.Rule
import org.junit.Test
import se.scmv.morocco.authentication.data.repository.AuthenticationRepositoryConfigurableImpl
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.models.NetworkAndBackendErrors
import se.scmv.morocco.domain.models.NetworkErrors

class PasswordResetEndTest {

    // 10.2 64GO 8gen

    @get:Rule
    val composeTestRule = createAndroidComposeRule<ComponentActivity>()

    @Test
    fun resetPassword_backendError() {
        launchPasswordResetScreen(
            rule = composeTestRule,
            authenticationRepository = AuthenticationRepositoryConfigurableImpl(
                resetPasswordResponse = Resource.Failure(
                    NetworkAndBackendErrors.Backend(
                        message = AuthenticationRepositoryConfigurableImpl.USER_ACTIVATION_CODE_INVALID
                    )
                )
            )
        ) {
            typePassword("Azerty@123")
            typePasswordConfirm("Azerty@123")
            submit()
        } verify {
            passwordHasNoError()
            passwordConfirmHasNoError()
            backendErrorIsDisplayed()
        }
    }

    @Test
    fun resetPassword_networkError() {
        launchPasswordResetScreen(
            rule = composeTestRule,
            authenticationRepository = AuthenticationRepositoryConfigurableImpl(
                resetPasswordResponse = Resource.Failure(
                    error = NetworkAndBackendErrors.Network(NetworkErrors.NO_INTERNET)
                )
            )
        ) {
            typePassword("Azerty@123")
            typePasswordConfirm("Azerty@123")
            submit()
        } verify {
            passwordHasNoError()
            passwordConfirmHasNoError()
            networkErrorIsDisplayed()
        }
    }

    @Test
    fun resetPassword_success() {
        launchPasswordResetScreen(
            rule = composeTestRule,
            authenticationRepository = AuthenticationRepositoryConfigurableImpl(
                resetPasswordResponse = Resource.Success("fake Code")
            )
        ) {
            typePassword("Azerty@123")
            typePasswordConfirm("Azerty@123")
            submit()
        } verify {
            passwordHasNoError()
            passwordConfirmHasNoError()
        }
    }
}
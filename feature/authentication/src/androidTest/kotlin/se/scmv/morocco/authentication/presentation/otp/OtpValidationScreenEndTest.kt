package se.scmv.morocco.authentication.presentation.otp

import androidx.activity.ComponentActivity
import androidx.compose.ui.test.junit4.createAndroidComposeRule
import org.junit.Rule
import org.junit.Test
import se.scmv.morocco.authentication.data.repository.AuthenticationRepositoryConfigurableImpl
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.models.NetworkAndBackendErrors
import se.scmv.morocco.domain.models.NetworkErrors

class OtpValidationScreenEndTest {

    @get:Rule
    val rule = createAndroidComposeRule<ComponentActivity>()

    @Test
    fun backendError() {
        launchOtpValidationScreen(
            rule = rule,
            authenticationRepository = AuthenticationRepositoryConfigurableImpl(
                validatePhoneForPasswordResetResponse = Resource.Failure(
                    NetworkAndBackendErrors.Backend(
                        AuthenticationRepositoryConfigurableImpl.OTP_BACKEND_ERROR
                    )
                )
            )
        ) {
            typeCode("123456")
            submit()
        } verify {
            codeRequiredErrorIsNotDisplayed()
            codeIncompleteErrorIsNotDisplayed()
            backendErrorIsDisplayed()
        }
    }

    @Test
    fun networkError() {
        launchOtpValidationScreen(
            rule = rule,
            authenticationRepository = AuthenticationRepositoryConfigurableImpl(
                validatePhoneForPasswordResetResponse = Resource.Failure(
                    NetworkAndBackendErrors.Network(NetworkErrors.NO_INTERNET)
                )
            )
        ) {
            typeCode("123456")
            submit()
        } verify {
            codeRequiredErrorIsNotDisplayed()
            codeIncompleteErrorIsNotDisplayed()
            networkErrorIsDisplayed()
        }
    }

    @Test
    fun success() {
        launchOtpValidationScreen(
            rule = rule,
            authenticationRepository = AuthenticationRepositoryConfigurableImpl(
                validatePhoneForPasswordResetResponse = Resource.Success(true)
            )
        ) {
            typeCode("123456")
            submit()
        } verify {
            codeRequiredErrorIsNotDisplayed()
            codeIncompleteErrorIsNotDisplayed()
            networkErrorIsNotDisplayed()
        }
    }

    @Test
    fun resendCodeBackendError() {
        launchOtpValidationScreen(
            rule = rule,
            authenticationRepository = AuthenticationRepositoryConfigurableImpl(
                sendSmsVerificationForPasswordResetResponse = Resource.Failure(
                    NetworkAndBackendErrors.Backend(
                        AuthenticationRepositoryConfigurableImpl.OTP_BACKEND_ERROR
                    )
                )
            )
        ) {
            clickResendCode()
        } verify {
            backendErrorIsDisplayed()
        }
    }

    @Test
    fun resendCodeNetworkError() {
        launchOtpValidationScreen(
            rule = rule,
            authenticationRepository = AuthenticationRepositoryConfigurableImpl(
                sendSmsVerificationForPasswordResetResponse = Resource.Failure(
                    NetworkAndBackendErrors.Network(NetworkErrors.NO_INTERNET)
                )
            )
        ) {
            clickResendCode()
        } verify {
            networkErrorIsDisplayed()
        }
    }

    @Test
    fun resendCodeSuccess() {
        launchOtpValidationScreen(
            rule = rule,
            authenticationRepository = AuthenticationRepositoryConfigurableImpl(
                sendSmsVerificationResponse = Resource.Success("06666666")
            )
        ) {
            clickResendCode()
        } verify {
            resendCodeSuccessIsDisplayed()
        }
    }
}
package se.scmv.morocco.authentication.presentation.shop_account.signup

import androidx.activity.ComponentActivity
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.ui.Modifier
import androidx.compose.ui.test.assertIsDisplayed
import androidx.compose.ui.test.assertIsNotDisplayed
import androidx.compose.ui.test.assertIsOn
import androidx.compose.ui.test.junit4.AndroidComposeTestRule
import androidx.compose.ui.test.onNodeWithTag
import androidx.compose.ui.test.onNodeWithText
import androidx.compose.ui.test.performClick
import androidx.compose.ui.test.performScrollTo
import androidx.compose.ui.test.performTextClearance
import androidx.compose.ui.test.performTextInput
import androidx.test.ext.junit.rules.ActivityScenarioRule
import se.scmv.morocco.authentication.R
import se.scmv.morocco.authentication.data.repository.AuthenticationRepositoryConfigurableImpl
import se.scmv.morocco.authentication.domain.validators.CredentialsValidator
import se.scmv.morocco.authentication.presentation.common.TestTags
import se.scmv.morocco.designsystem.components.TEST_TAG_TOS_AND_PP
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.utils.localizedName
import se.scmv.morocco.domain.models.ShopCategory
import se.scmv.morocco.domain.models.ShopSubscription
import se.scmv.morocco.domain.repositories.AuthenticationRepository
import se.scmv.morocco.ui.SnackBarHostForSnackBarController
import java.util.Locale

fun launchShopAccountSignUpScreen(
    rule: AndroidComposeTestRule<ActivityScenarioRule<ComponentActivity>, ComponentActivity>,
    authenticationRepository: AuthenticationRepository = AuthenticationRepositoryConfigurableImpl(),
    block: ShopAccountSignUpScreenRobot.() -> Unit
): ShopAccountSignUpScreenRobot {
    val viewModel = ShopAccountSignUpViewModel(
        credentialsValidator = CredentialsValidator(),
        authenticationRepository = authenticationRepository,
        context = rule.activity
    )
    rule.setContent {
        AvitoTheme {
            Scaffold(
                snackbarHost = { SnackBarHostForSnackBarController() }
            ) {
                ShopAccountSignUpRoute(
                    modifier = Modifier.padding(it),
                    viewModel = viewModel,
                    navigateToSuccess = {},
                    navigateBack = {},
                    navigateToWebViewScreen = {_,_ ->}
                )
            }
        }
    }
    return ShopAccountSignUpScreenRobot(rule).apply(block)
}

class ShopAccountSignUpScreenRobot(
    private val rule: AndroidComposeTestRule<ActivityScenarioRule<ComponentActivity>, ComponentActivity>
) {
    fun chooseCategory(category: ShopCategory?) {
        category?.let {
            rule.onNodeWithTag(rule.activity.getString(category.localizedName()))
                .performClick()
        }
    }

    fun chooseSubscription(subscription: ShopSubscription?) {
        subscription?.let {
            rule.onNodeWithTag(
                subscription.name.lowercase().replaceFirstChar {
                    if (it.isLowerCase()) it.titlecase(Locale.ROOT) else it.toString()
                }
            ).performClick()
        }
    }

    fun typeFullName(fullName: String) {
        rule.onNodeWithTag(TestTags.TEST_TAG_FULL_NAME)
            .apply {
                performTextClearance()
                performTextInput(fullName)
            }
    }

    fun typePhoneNumber(phoneNumber: String) {
        rule.onNodeWithTag(TestTags.TEST_TAG_PHONE_NUMBER)
            .apply {
                performTextClearance()
                performTextInput(phoneNumber)
            }
    }

    fun typeEmail(email: String) {
        rule.onNodeWithTag(TestTags.TEST_TAG_EMAIL)
            .apply {
                performTextClearance()
                performTextInput(email)
            }
    }

    /**
     * Assume that the cgu is checked by default.
     */
    fun uncheckTosAndPp() {
        rule.onNodeWithTag(TEST_TAG_TOS_AND_PP)
            .performClick()
    }

    fun submit() {
        val buttonText = rule.activity.getString(R.string.shop_account_sign_up_screen_submit_button)
        rule.onNodeWithText(buttonText)
            // NB: because our form is too large and we're using verticalScroll(rememberScrollState())
            // So we must scrollTo this button to perform click, else the click will not performed
            // and tests will fail.
            .performScrollTo()
            .performClick()
    }

    infix fun verify(block: ShopAccountSignUpScreenVerification.() -> Unit): ShopAccountSignUpScreenVerification {
        return ShopAccountSignUpScreenVerification(rule).apply(block)
    }
}

class ShopAccountSignUpScreenVerification(
    private val rule: AndroidComposeTestRule<ActivityScenarioRule<ComponentActivity>, ComponentActivity>
) {
    fun categoryRequiredErrorIsDisplayed() {
        val categoryRequired =
            rule.activity.getString(R.string.shop_account_sign_up_screen_shop_category_required)
        rule.onNodeWithText(categoryRequired)
            .assertIsDisplayed()
    }

    fun subscriptionRequiredErrorIsDisplayed() {
        val subscriptionRequired =
            rule.activity.getString(R.string.shop_account_sign_up_screen_shop_subscription_required)
        rule.onNodeWithText(subscriptionRequired)
            .assertIsDisplayed()
    }

    fun fullNameRequiredErrorIsDisplayed() {
        val fullNameRequired = rule.activity.getString(R.string.common_full_name_field_required)
        rule.onNodeWithText(fullNameRequired)
            .assertIsDisplayed()
    }

    fun fullNameRequiredErrorIsNotDisplayed() {
        val fullNameRequired = rule.activity.getString(R.string.common_full_name_field_required)
        rule.onNodeWithText(fullNameRequired)
            .assertIsNotDisplayed()
    }

    fun phoneNumberRequiredErrorIsDisplayed() {
        val phoneNumberRequired =
            rule.activity.getString(R.string.common_phone_number_field_required)
        rule.onNodeWithText(phoneNumberRequired)
            .assertIsDisplayed()
    }

    fun phoneNumberRequiredErrorIsNotDisplayed() {
        val phoneNumberRequired =
            rule.activity.getString(R.string.common_phone_number_field_required)
        rule.onNodeWithText(phoneNumberRequired)
            .assertIsNotDisplayed()
    }

    fun emailRequiredErrorIsDisplayed() {
        val emailRequired = rule.activity.getString(R.string.common_email_field_required)
        rule.onNodeWithText(emailRequired)
            .assertIsDisplayed()
    }

    fun emailRequiredErrorIsNotDisplayed() {
        val emailRequired = rule.activity.getString(R.string.common_email_field_required)
        rule.onNodeWithText(emailRequired)
            .assertIsNotDisplayed()
    }

    fun tosAndPpIsAlreadyChecked() {
        rule.onNodeWithTag(TEST_TAG_TOS_AND_PP)
            .assertIsOn()
    }

    fun tosAndPpConfirmationRequiredErrorIsDisplayed() {
        val tosAndPpConfirmation =
            rule.activity.getString(R.string.common_tos_and_pp_field_required)
        rule.onNodeWithText(tosAndPpConfirmation)
            .assertIsDisplayed()
    }

    fun emailFormatInvalidErrorIsDisplayed() {
        val emailFormatInvalid = rule.activity.getString(R.string.common_email_field_format_invalid)
        rule.onNodeWithText(emailFormatInvalid)
            .assertIsDisplayed()
    }

    fun emailFormatInvalidErrorIsNotDisplayed() {
        val emailFormatInvalid = rule.activity.getString(R.string.common_email_field_format_invalid)
        rule.onNodeWithText(emailFormatInvalid)
            .assertIsNotDisplayed()
    }

    fun phoneNumberFormatInvalidErrorIsDisplayed() {
        val phoneInvalid =
            rule.activity.getString(R.string.common_phone_number_field_format_invalid)
        rule.onNodeWithText(phoneInvalid)
            .assertIsDisplayed()
    }

    fun phoneNumberFormatInvalidErrorIsNotDisplayed() {
        val phoneInvalid =
            rule.activity.getString(R.string.common_phone_number_field_format_invalid)
        rule.onNodeWithText(phoneInvalid)
            .assertIsNotDisplayed()
    }

    fun fullNameInvalidErrorIsDisplayed() {
        rule.onNodeWithText(AuthenticationRepositoryConfigurableImpl.USERS_NAME_INVALID)
            .assertIsDisplayed()
    }

    fun networkErrorIsDisplayed() {
        val networkError =
            rule.activity.getString(R.string.common_network_error_verify_and_try_later)
        rule.onNodeWithText(networkError)
            .assertIsDisplayed()
    }
}
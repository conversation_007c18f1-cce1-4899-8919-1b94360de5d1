package se.scmv.morocco.authentication.presentation.private_account.reset_password.update

import androidx.activity.ComponentActivity
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.ui.Modifier
import androidx.compose.ui.test.assertIsDisplayed
import androidx.compose.ui.test.assertIsNotDisplayed
import androidx.compose.ui.test.junit4.AndroidComposeTestRule
import androidx.compose.ui.test.onNodeWithTag
import androidx.compose.ui.test.onNodeWithText
import androidx.compose.ui.test.performClick
import androidx.compose.ui.test.performTextClearance
import androidx.compose.ui.test.performTextInput
import androidx.lifecycle.SavedStateHandle
import androidx.test.ext.junit.rules.ActivityScenarioRule
import se.scmv.morocco.authentication.R
import se.scmv.morocco.authentication.data.repository.AuthenticationRepositoryConfigurableImpl
import se.scmv.morocco.authentication.domain.validators.CredentialsValidator
import se.scmv.morocco.authentication.presentation.common.TestTags
import se.scmv.morocco.authentication.presentation.navigation.ARG_OTP_CODE
import se.scmv.morocco.authentication.presentation.resset_password.private_account.update.PasswordResetRoute
import se.scmv.morocco.authentication.presentation.resset_password.private_account.update.PasswordResetViewModel
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.domain.repositories.AuthenticationRepository
import se.scmv.morocco.ui.SnackBarHostForSnackBarController

fun launchPasswordResetScreen(
    rule: AndroidComposeTestRule<ActivityScenarioRule<ComponentActivity>, ComponentActivity>,
    authenticationRepository: AuthenticationRepository = AuthenticationRepositoryConfigurableImpl(),
    block: PasswordResetRobot.() -> Unit
): PasswordResetRobot {
    val viewModel = PasswordResetViewModel(
        savedStateHandle = SavedStateHandle().apply { set(ARG_OTP_CODE, "123456") },
        credentialsValidator = CredentialsValidator(),
        authenticationRepository = authenticationRepository
    )
    rule.setContent {
        AvitoTheme {
            Scaffold(
                snackbarHost = { SnackBarHostForSnackBarController() }
            ) {
                PasswordResetRoute(
                    modifier = Modifier.padding(it),
                    viewModel = viewModel,
                    navigateBack = {},
                    navigateToPasswordResetSuccess = {}
                )
            }
        }
    }
    return PasswordResetRobot(rule).apply(block)
}

class PasswordResetRobot(
    private val rule: AndroidComposeTestRule<ActivityScenarioRule<ComponentActivity>, ComponentActivity>,
) {
    fun typePassword(password: String) {
        rule.onNodeWithTag(TestTags.TEST_TAG_PASSWORD)
            .apply {
                performTextClearance()
                performTextInput(password)
            }
    }

    fun typePasswordConfirm(passwordConfirm: String) {
        rule.onNodeWithTag(TestTags.TEST_TAG_PASSWORD_CONFIRM)
            .apply {
                performTextClearance()
                performTextInput(passwordConfirm)
            }
    }

    fun submit() {
        val buttonText = rule.activity.getString(R.string.common_continue)
        rule.onNodeWithText(buttonText)
            .performClick()
    }

    infix fun verify(
        block: PasswordResetVerification.() -> Unit
    ): PasswordResetVerification {
        return PasswordResetVerification(rule).apply(block)
    }
}

class PasswordResetVerification(
    private val rule: AndroidComposeTestRule<ActivityScenarioRule<ComponentActivity>, ComponentActivity>
) {
    fun passwordRequiredErrorIsDisplayed() {
        val passwordRequired = rule.activity.getString(R.string.common_password_field_required)
        rule.onNodeWithText(passwordRequired)
            .assertIsDisplayed()
    }

    fun passwordHasNoError() {
        val passwordRequired = rule.activity.getString(R.string.common_password_field_required)
        rule.onNodeWithText(passwordRequired)
            .assertIsNotDisplayed()
    }

    fun passwordConfirmRequiredErrorIsDisplayed() {
        val passwordConfirmRequired = rule.activity.getString(R.string.common_password_confirm_field_required)
        rule.onNodeWithText(passwordConfirmRequired)
            .assertIsDisplayed()
    }

    fun passwordConfirmHasNoError() {
        val passwordConfirmRequired = rule.activity.getString(R.string.common_password_confirm_field_required)
        rule.onNodeWithText(passwordConfirmRequired)
            .assertIsNotDisplayed()
    }

    fun passwordDoNotMatchErrorIsDisplayed() {
        val passwordsDoNotMatchError = rule.activity.getString(R.string.common_password_confirm_field_not_match)
        rule.onNodeWithText(passwordsDoNotMatchError)
            .assertIsDisplayed()
    }

    fun backendErrorIsDisplayed() {
        rule.onNodeWithText(AuthenticationRepositoryConfigurableImpl.USER_ACTIVATION_CODE_INVALID)
            .assertIsDisplayed()
    }

    fun networkErrorIsDisplayed() {
        val networkError =
            rule.activity.getString(R.string.common_network_error_verify_and_try_later)
        rule.onNodeWithText(networkError)
            .assertIsDisplayed()
    }
}
package se.scmv.morocco.authentication.presentation.private_account.signup.complete_info

import androidx.activity.ComponentActivity
import androidx.compose.ui.test.junit4.createAndroidComposeRule
import org.junit.Rule
import org.junit.Test
import se.scmv.morocco.authentication.data.repository.AuthenticationRepositoryConfigurableImpl

/**
 * NB: Regarding the fields format validation, currently we've only one constraint:
 * Name, password shouldn't be empty. If the PM give us other constraints in the future we'll
 * add tests for them.
 */
class PrivateAccountCompleteInfoCredentialsTest {

    @get:Rule
    val composeTestRule = createAndroidComposeRule<ComponentActivity>()

    @Test
    fun completeInfo_PhoneAutoFiled() {
        launchPrivateAccountCompleteInfoScreen(
            rule = composeTestRule,
            authenticationRepository = AuthenticationRepositoryConfigurableImpl()
        ) {
            // do nothing
        } verify {
            phoneNumberIsAlreadyFiledAndDisabled()
        }
    }

    @Test
    fun completeInfo_FullNameRequired() {
        launchPrivateAccountCompleteInfoScreen(
            rule = composeTestRule,
            authenticationRepository = AuthenticationRepositoryConfigurableImpl()
        ) {
            typeFullName("")
            typePassword("Azerty@123")
            typePasswordConfirm("Azerty@123")
            submit()
        } verify {
            fullNameRequiredErrorIsDisplayed()
        }
    }

    @Test
    fun completeInfo_PasswordRequired() {
        launchPrivateAccountCompleteInfoScreen(
            rule = composeTestRule,
            authenticationRepository = AuthenticationRepositoryConfigurableImpl()
        ) {
            typeFullName("Jhon DOE")
            typePassword("")
            typePasswordConfirm("Azerty@123")
            submit()
        } verify {
            passwordRequiredErrorIsDisplayed()
        }
    }

    @Test
    fun completeInfo_PasswordConfirmRequired() {
        launchPrivateAccountCompleteInfoScreen(
            rule = composeTestRule,
            authenticationRepository = AuthenticationRepositoryConfigurableImpl()
        ) {
            typeFullName("Jhon DOE")
            typePassword("Azerty@123")
            typePasswordConfirm("")
            submit()
        } verify {
            passwordConfirmRequiredErrorIsDisplayed()
        }
    }

    @Test
    fun completeInfo_tosAndPpAutoFiled() {
        launchPrivateAccountCompleteInfoScreen(
            rule = composeTestRule,
            authenticationRepository = AuthenticationRepositoryConfigurableImpl()
        ) {
            // do nothing
        } verify {
            tosAndPpIsAlreadyChecked()
        }
    }

    @Test
    fun completeInfo_tosAndPpConfirmationRequired() {
        launchPrivateAccountCompleteInfoScreen(
            rule = composeTestRule,
            authenticationRepository = AuthenticationRepositoryConfigurableImpl()
        ) {
            typeFullName("Jhon DOE")
            typePassword("Azerty@123")
            typePasswordConfirm("Azerty@123")
            uncheckTosAndPp()
            submit()
        } verify {
            tosAndPpConfirmationRequiredErrorIsDisplayed()
        }
    }

    @Test
    fun completeInfo_passwordsDoNotMatch() {
        launchPrivateAccountCompleteInfoScreen(
            rule = composeTestRule,
            authenticationRepository = AuthenticationRepositoryConfigurableImpl()
        ) {
            typeFullName("Jhon DOE")
            listOf(
                "Azerty@123" to "Azerty@12",
                "Azerty@123" to "Azerty@",
                "Azerty@" to "Azerty@123",
                "Azerty@12" to "Azerty@13",
                "azerty@123" to "Azerty@123",
            ).forEach {
                typePassword(it.first)
                typePasswordConfirm(it.second)
                submit()
                verify {
                    passwordDoNotMatchErrorIsDisplayed()
                }
            }
        }
    }
}
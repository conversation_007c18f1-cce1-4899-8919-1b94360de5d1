package se.scmv.morocco.authentication.presentation.private_account.signin

import androidx.activity.ComponentActivity
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.ui.Modifier
import androidx.compose.ui.test.assertIsDisplayed
import androidx.compose.ui.test.assertIsNotDisplayed
import androidx.compose.ui.test.junit4.AndroidComposeTestRule
import androidx.compose.ui.test.onAllNodesWithText
import androidx.compose.ui.test.onFirst
import androidx.compose.ui.test.onLast
import androidx.compose.ui.test.onNodeWithTag
import androidx.compose.ui.test.onNodeWithText
import androidx.compose.ui.test.performClick
import androidx.compose.ui.test.performScrollTo
import androidx.compose.ui.test.performTextClearance
import androidx.compose.ui.test.performTextInput
import androidx.test.ext.junit.rules.ActivityScenarioRule
import com.facebook.FacebookSdk
import se.scmv.morocco.authentication.R
import se.scmv.morocco.authentication.data.repository.AuthenticationRepositoryConfigurableImpl
import se.scmv.morocco.authentication.domain.validators.CredentialsValidator
import se.scmv.morocco.authentication.presentation.common.TestTags
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.domain.repositories.AuthenticationRepository
import se.scmv.morocco.ui.SnackBarHostForSnackBarController

fun launchPrivateAccountSignInScreen(
    rule: AndroidComposeTestRule<ActivityScenarioRule<ComponentActivity>, ComponentActivity>,
    authenticationRepository: AuthenticationRepository,
    block: PrivateAccountSignInScreenRobot.() -> Unit
): PrivateAccountSignInScreenRobot {
    FacebookSdk.setApplicationId("se.scmv.morocco")
    FacebookSdk.setClientToken("FakeToken")
    FacebookSdk.sdkInitialize(rule.activity.application)
    val viewModel = PrivateAccountSignInViewModel(
        credentialsValidator = CredentialsValidator(),
        authenticationRepository = authenticationRepository
    )
    rule.setContent {
        AvitoTheme {
            Scaffold(
                snackbarHost = { SnackBarHostForSnackBarController() }
            ) {
                PrivateAccountSignInRoute(
                    modifier = Modifier.padding(it),
                    viewModel = viewModel,
                    navigateToResetPassword = {},
                    navigateToStoreSignIn = {},
                    navigateToHome = {_, _ ->},
                    navigateToWebViewScreen = { _, _ -> }
                )
            }
        }
    }
    return PrivateAccountSignInScreenRobot(rule).apply { block() }
}

class PrivateAccountSignInScreenRobot(
    private val rule: AndroidComposeTestRule<ActivityScenarioRule<ComponentActivity>, ComponentActivity>,
) {

    fun typeEmailOrPhone(emailOrPhone: String) {
        rule.onNodeWithTag(TestTags.TEST_TAG_EMAIL_OR_PHONE)
            .apply {
                performTextClearance()
                performTextInput(emailOrPhone)
            }
    }

    fun typePassword(password: String) {
        rule.onNodeWithTag(TestTags.TEST_TAG_PASSWORD)
            .apply {
                performTextClearance()
                performTextInput(password)
            }
    }

    fun submit() {
        val buttonText =
            rule.activity.getString(R.string.private_account_sign_in_screen_submit_button)
        rule.onNodeWithText(buttonText)
            // NB: because our form is too large and we're using verticalScroll(rememberScrollState())
            // So we must scrollTo this button to perform click, else the click will not performed
            // and tests will fail.
            .performScrollTo()
            .performClick()
    }

    infix fun verify(
        block: PrivateAccountSignInScreenVerification.() -> Unit
    ): PrivateAccountSignInScreenVerification {
        return PrivateAccountSignInScreenVerification(rule).apply { block() }
    }
}

class PrivateAccountSignInScreenVerification(
    private val rule: AndroidComposeTestRule<ActivityScenarioRule<ComponentActivity>, ComponentActivity>,
) {
    fun emailHasNoError() {
        val emailFormatInvalidError =
            rule.activity.getString(R.string.common_email_field_format_invalid)
        rule.onNodeWithText(emailFormatInvalidError)
            .assertIsNotDisplayed()
    }

    fun phoneHasNoError() {
        val phoneFormatInvalidError =
            rule.activity.getString(R.string.common_phone_number_field_format_invalid)
        rule.onNodeWithText(phoneFormatInvalidError)
            .assertIsNotDisplayed()
    }

    fun passwordHasNoError() {
        val passwordInvalidError =
            rule.activity.getString(R.string.common_password_field_required)
        rule.onNodeWithText(passwordInvalidError)
            .assertIsNotDisplayed()
    }

    fun emailOrPhoneRequiredErrorIsDisplayed() {
        val emailFormatInvalidError =
            rule.activity.getString(R.string.private_account_sign_in_screen_email_or_phone_field_required)
        rule.onNodeWithText(emailFormatInvalidError)
            .assertIsDisplayed()
    }

    fun emailFormatInvalidErrorIsDisplayed() {
        val emailFormatInvalidError =
            rule.activity.getString(R.string.common_email_field_format_invalid)
        rule.onNodeWithText(emailFormatInvalidError)
            .assertIsDisplayed()
    }

    fun phoneFormatInvalidErrorIsDisplayed() {
        val emailFormatInvalidError =
            rule.activity.getString(R.string.common_phone_number_field_format_invalid)
        rule.onNodeWithText(emailFormatInvalidError)
            .assertIsDisplayed()
    }

    fun passwordRequiredErrorIsDisplayed() {
        val passwordFormatInvalidError = rule.activity.getString(R.string.common_password_field_required)
        rule.onNodeWithText(passwordFormatInvalidError)
            .assertIsDisplayed()
    }

    fun backendErrorIsDisplayed() {
        rule.onAllNodesWithText(AuthenticationRepositoryConfigurableImpl.SIGN_IN_ERROR)
            .apply {
                onFirst().assertIsDisplayed()
                onLast().assertIsDisplayed()
            }
    }

    fun networkErrorIsDisplayed() {
        val networkError =
            rule.activity.getString(R.string.common_network_error_verify_and_try_later)
        rule.onNodeWithText(networkError)
            .assertIsDisplayed()
    }
}
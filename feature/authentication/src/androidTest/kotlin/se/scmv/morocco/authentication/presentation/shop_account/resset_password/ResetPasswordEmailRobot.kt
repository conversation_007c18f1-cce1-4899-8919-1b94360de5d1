package se.scmv.morocco.authentication.presentation.shop_account.resset_password

import androidx.activity.ComponentActivity
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.ui.Modifier
import androidx.compose.ui.test.assertIsDisplayed
import androidx.compose.ui.test.assertIsNotDisplayed
import androidx.compose.ui.test.junit4.AndroidComposeTestRule
import androidx.compose.ui.test.onNodeWithTag
import androidx.compose.ui.test.onNodeWithText
import androidx.compose.ui.test.performClick
import androidx.compose.ui.test.performTextClearance
import androidx.compose.ui.test.performTextInput
import androidx.test.ext.junit.rules.ActivityScenarioRule
import se.scmv.morocco.authentication.R
import se.scmv.morocco.authentication.data.repository.AuthenticationRepositoryConfigurableImpl
import se.scmv.morocco.authentication.domain.validators.CredentialsValidator
import se.scmv.morocco.authentication.presentation.common.TestTags
import se.scmv.morocco.authentication.presentation.resset_password.shop_account.ResetPasswordEmailRoute
import se.scmv.morocco.authentication.presentation.resset_password.shop_account.ResetPasswordEmailViewModel
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.domain.repositories.AuthenticationRepository
import se.scmv.morocco.ui.SnackBarHostForSnackBarController

fun launchResetPasswordEmailScreen(
    rule: AndroidComposeTestRule<ActivityScenarioRule<ComponentActivity>, ComponentActivity>,
    authenticationRepository: AuthenticationRepository = AuthenticationRepositoryConfigurableImpl(),
    block: ResetPasswordEmailRobot.() -> Unit
): ResetPasswordEmailRobot {
    val viewModel = ResetPasswordEmailViewModel(
        credentialsValidator = CredentialsValidator(),
        authenticationRepository = authenticationRepository
    )
    rule.setContent {
        AvitoTheme {
            Scaffold(
                snackbarHost = { SnackBarHostForSnackBarController() }
            ) {
                ResetPasswordEmailRoute (
                    modifier = Modifier.padding(it),
                    viewModel = viewModel,
                    navigateToSuccess = {}
                )
            }
        }
    }
    return ResetPasswordEmailRobot(rule).apply(block)
}

class ResetPasswordEmailRobot(
    private val rule: AndroidComposeTestRule<ActivityScenarioRule<ComponentActivity>, ComponentActivity>
) {
    fun typeEmail(phone: String) {
        rule.onNodeWithTag(TestTags.TEST_TAG_EMAIL)
            .apply {
                performTextClearance()
                performTextInput(phone)
            }
    }

    fun submit() {
        val buttonText = rule.activity.getString(R.string.common_send)
        rule.onNodeWithText(buttonText)
            .performClick()
    }

    infix fun verify(
        block: ResetPasswordEmailVerification.() -> Unit
    ): ResetPasswordEmailVerification {
        return ResetPasswordEmailVerification(rule).apply { block() }
    }
}

class ResetPasswordEmailVerification(
    private val rule: AndroidComposeTestRule<ActivityScenarioRule<ComponentActivity>, ComponentActivity>
) {
    fun emailRequiredErrorIsDisplayed() {
        val phoneRequired = rule.activity.getString(R.string.common_email_field_required)
        rule.onNodeWithText(phoneRequired)
            .assertIsDisplayed()
    }

    fun emailFormatInvalidErrorIsDisplayed() {
        val phoneInvalid =
            rule.activity.getString(R.string.common_email_field_format_invalid)
        rule.onNodeWithText(phoneInvalid)
            .assertIsDisplayed()
    }

    fun emailHasNoError() {
        val phoneInvalid =
            rule.activity.getString(R.string.common_email_field_format_invalid)
        rule.onNodeWithText(phoneInvalid)
            .assertIsNotDisplayed()
    }

    fun backendErrorIsDisplayed() {
        rule.onNodeWithText(AuthenticationRepositoryConfigurableImpl.RESET_PASSWORD_EMAIL_BACKEND_ERROR)
            .assertIsDisplayed()
    }

    fun backendErrorIsNotDisplayed() {
        rule.onNodeWithText(AuthenticationRepositoryConfigurableImpl.RESET_PASSWORD_EMAIL_BACKEND_ERROR)
            .assertIsNotDisplayed()
    }

    fun networkErrorIsDisplayed() {
        val networkError =
            rule.activity.getString(R.string.common_network_error_verify_and_try_later)
        rule.onNodeWithText(networkError)
            .assertIsDisplayed()
    }

    fun networkErrorIsNotDisplayed() {
        val networkError =
            rule.activity.getString(R.string.common_network_error_verify_and_try_later)
        rule.onNodeWithText(networkError)
            .assertIsNotDisplayed()
    }
}
package se.scmv.morocco.authentication.presentation.private_account.reset_password.phone

import androidx.activity.ComponentActivity
import androidx.compose.ui.test.junit4.createAndroidComposeRule
import org.junit.Rule
import org.junit.Test

class ResetPasswordPhoneNumberCredentialsTest {

    @get:Rule
    val composeTestRule = createAndroidComposeRule<ComponentActivity>()

    @Test
    fun resetPassword_required() {
        launchResetPasswordPhoneNumberScreen(
            composeTestRule
        ) {
            typePhoneNumber("")
            submit()
        } verify {
            phoneRequiredErrorIsDisplayed()
        }
    }

    @Test
    fun resetPassword_phoneNumberFormatInvalid() {
        launchResetPasswordPhoneNumberScreen(
            rule = composeTestRule,
        ) {
            listOf(
                "*********",
                "*********",
                "**********9",
                "**********",
            ).forEach { phone ->
                typePhoneNumber(phone)
                submit()
                verify {
                    phoneNumberFormatInvalidErrorIsDisplayed()
                }
            }
        }
    }

    @Test
    fun resetPassword_phoneNumberFormatValid() {
        launchResetPasswordPhoneNumberScreen(
            rule = composeTestRule,
        ) {
            listOf(
                "*********8",
                "0612345678",
                "0712345678",
                "0812345678",
                "0*********1",
                "0*********012"
            ).forEach { phone ->
                typePhoneNumber(phone)
                submit()
                verify {
                    phoneHasNoError()
                }
            }
        }
    }
}
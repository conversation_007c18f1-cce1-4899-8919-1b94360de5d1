package se.scmv.morocco.authentication.presentation.shop_account.signin

import androidx.activity.ComponentActivity
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.ui.Modifier
import androidx.compose.ui.test.assertIsDisplayed
import androidx.compose.ui.test.assertIsNotDisplayed
import androidx.compose.ui.test.junit4.AndroidComposeTestRule
import androidx.compose.ui.test.onAllNodesWithText
import androidx.compose.ui.test.onFirst
import androidx.compose.ui.test.onLast
import androidx.compose.ui.test.onNodeWithTag
import androidx.compose.ui.test.onNodeWithText
import androidx.compose.ui.test.performClick
import androidx.compose.ui.test.performTextClearance
import androidx.compose.ui.test.performTextInput
import androidx.test.ext.junit.rules.ActivityScenarioRule
import se.scmv.morocco.authentication.R
import se.scmv.morocco.authentication.data.repository.AuthenticationRepositoryConfigurableImpl
import se.scmv.morocco.authentication.domain.validators.CredentialsValidator
import se.scmv.morocco.authentication.presentation.common.TestTags
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.domain.repositories.AuthenticationRepository
import se.scmv.morocco.ui.SnackBarHostForSnackBarController

fun launchShopAccountSignInScreen(
    rule: AndroidComposeTestRule<ActivityScenarioRule<ComponentActivity>, ComponentActivity>,
    authenticationRepository: AuthenticationRepository,
    block: ShopAccountSignInScreenRobot.() -> Unit
): ShopAccountSignInScreenRobot {
    val viewModel = ShopAccountSignInViewModel(
        credentialsValidator = CredentialsValidator(),
        authenticationRepository = authenticationRepository
    )
    rule.setContent {
        AvitoTheme {
            Scaffold(
                snackbarHost = { SnackBarHostForSnackBarController() }
            ) {
                ShopAccountSignInRoute(
                    modifier = Modifier.padding(it),
                    viewModel = viewModel,
                    navigateToResetPassword = {},
                    navigateToStoreSignUp = {},
                    navigateBack = {},
                    navigateToHome = {},
                    navigateToWebViewScreen = { _, _ -> }
                )
            }
        }
    }
    return ShopAccountSignInScreenRobot(rule).apply { block() }
}

class ShopAccountSignInScreenRobot(
    private val rule: AndroidComposeTestRule<ActivityScenarioRule<ComponentActivity>, ComponentActivity>
) {
    fun typeEmail(email: String) {
        rule.onNodeWithTag(TestTags.TEST_TAG_EMAIL)
            .apply {
                performTextClearance()
                performTextInput(email)
            }
    }

    fun typePassword(password: String) {
        rule.onNodeWithTag(TestTags.TEST_TAG_PASSWORD)
            .apply {
                performTextClearance()
                performTextInput(password)
            }
    }

    fun submit() {
        val buttonText = rule.activity.getString(R.string.shop_account_sign_in_screen_submit_button)
        rule.onNodeWithText(buttonText)
            .performClick()
    }

    infix fun verify(
        block: ShopAccountSignInScreenVerification.() -> Unit
    ): ShopAccountSignInScreenVerification {
        return ShopAccountSignInScreenVerification(rule).apply { block() }
    }
}

class ShopAccountSignInScreenVerification(
    private val rule: AndroidComposeTestRule<ActivityScenarioRule<ComponentActivity>, ComponentActivity>
) {
    fun emailRequiredErrorIsDisplayed() {
        val emailRequired = rule.activity.getString(R.string.common_email_field_required)
        rule.onNodeWithText(emailRequired)
            .assertIsDisplayed()
    }

    fun passwordRequiredErrorIsDisplayed() {
        val passwordRequired = rule.activity.getString(R.string.common_password_field_required)
        rule.onNodeWithText(passwordRequired)
            .assertIsDisplayed()
    }

    fun emailFormatInvalidErrorIsDisplayed() {
        val emailInvalid =
            rule.activity.getString(R.string.common_email_field_format_invalid)
        rule.onNodeWithText(emailInvalid)
            .assertIsDisplayed()
    }

    fun emailHasNoError() {
        val emailInvalid =
            rule.activity.getString(R.string.common_email_field_format_invalid)
        rule.onNodeWithText(emailInvalid)
            .assertIsNotDisplayed()
    }

    fun passwordHasNoError() {
        val passwordRequired = rule.activity.getString(R.string.common_password_field_required)
        rule.onNodeWithText(passwordRequired)
            .assertIsNotDisplayed()
    }

    fun backendErrorIsDisplayed() {
        rule.onAllNodesWithText(AuthenticationRepositoryConfigurableImpl.SIGN_IN_ERROR)
            .apply {
                onFirst().assertIsDisplayed()
                onLast().assertIsDisplayed()
            }
    }

    fun networkErrorIsDisplayed() {
        val networkError =
            rule.activity.getString(R.string.common_network_error_verify_and_try_later)
        rule.onNodeWithText(networkError)
            .assertIsDisplayed()
    }
}
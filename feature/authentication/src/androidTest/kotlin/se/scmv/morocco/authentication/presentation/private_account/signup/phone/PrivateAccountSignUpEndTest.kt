package se.scmv.morocco.authentication.presentation.private_account.signup.phone

import androidx.activity.ComponentActivity
import androidx.compose.ui.test.junit4.createAndroidComposeRule
import org.junit.Rule
import org.junit.Test
import se.scmv.morocco.authentication.data.repository.AuthenticationRepositoryConfigurableImpl
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.models.NetworkAndBackendErrors
import se.scmv.morocco.domain.models.NetworkErrors

class PrivateAccountSignUpEndTest {

    @get:Rule(order = 0)
    val composeTestRule = createAndroidComposeRule<ComponentActivity>()

    @Test
    fun signUp_BackendError() {
        launchPrivateAccountSignUpScreen(
            rule = composeTestRule,
            authenticationRepository = AuthenticationRepositoryConfigurableImpl(
                sendSmsVerificationResponse = Resource.Failure(
                    NetworkAndBackendErrors.Backend(
                        AuthenticationRepositoryConfigurableImpl.PHONE_NUMBER_SIGN_UP_BACKEND_ERROR
                    )
                )
            )
        ) {
            typePhoneNumber("**********")
            submit()
        } verify {
            phoneHasNoError()
            backendErrorIsDisplayed()
        }
    }

    @Test
    fun signUp_NetworkError() {
        launchPrivateAccountSignUpScreen(
            rule = composeTestRule,
            authenticationRepository = AuthenticationRepositoryConfigurableImpl(
                sendSmsVerificationResponse = Resource.Failure(
                    NetworkAndBackendErrors.Network(NetworkErrors.NO_INTERNET)
                )
            )
        ) {
            typePhoneNumber("**********")
            submit()
        } verify {
            phoneHasNoError()
            networkErrorIsDisplayed()
        }
    }

    @Test
    fun signUp_success() {
        launchPrivateAccountSignUpScreen(
            rule = composeTestRule,
            authenticationRepository = AuthenticationRepositoryConfigurableImpl()
        ) {
            typePhoneNumber("**********")
            submit()
        } verify {
            phoneHasNoError()
        }
    }
}
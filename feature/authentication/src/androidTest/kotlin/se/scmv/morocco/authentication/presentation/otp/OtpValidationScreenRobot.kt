package se.scmv.morocco.authentication.presentation.otp

import androidx.activity.ComponentActivity
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.ui.Modifier
import androidx.compose.ui.test.assertIsDisplayed
import androidx.compose.ui.test.assertIsNotDisplayed
import androidx.compose.ui.test.junit4.AndroidComposeTestRule
import androidx.compose.ui.test.onNodeWithTag
import androidx.compose.ui.test.onNodeWithText
import androidx.compose.ui.test.performClick
import androidx.compose.ui.test.performScrollTo
import androidx.compose.ui.test.performTextClearance
import androidx.compose.ui.test.performTextInput
import androidx.lifecycle.SavedStateHandle
import androidx.test.ext.junit.rules.ActivityScenarioRule
import se.scmv.morocco.authentication.R
import se.scmv.morocco.authentication.data.repository.AuthenticationRepositoryConfigurableImpl
import se.scmv.morocco.authentication.presentation.common.TestTags
import se.scmv.morocco.authentication.presentation.navigation.ARG_OTP_VALIDATION_REASON
import se.scmv.morocco.authentication.presentation.navigation.ARG_PHONE_NUMBER
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.domain.repositories.AuthenticationRepository
import se.scmv.morocco.ui.SnackBarHostForSnackBarController

fun launchOtpValidationScreen(
    rule: AndroidComposeTestRule<ActivityScenarioRule<ComponentActivity>, ComponentActivity>,
    authenticationRepository: AuthenticationRepository = AuthenticationRepositoryConfigurableImpl(),
    block: OtpValidationScreenRobot.() -> Unit
): OtpValidationScreenRobot {
    val viewModel = OtpValidationViewModel(
        savedStateHandle = SavedStateHandle().apply {
            set(ARG_PHONE_NUMBER, "0611223344")
            set(ARG_OTP_VALIDATION_REASON, OtpValidationReason.PASSWORD_RESET.name)
        },
        authenticationRepository = authenticationRepository,
        codeResendTimeout = CodeResendTimeout(0)
    )
    rule.setContent {
        AvitoTheme {
            Scaffold(
                snackbarHost = { SnackBarHostForSnackBarController() }
            ) {
                OtpValidationRoute(
                    modifier = Modifier.padding(it),
                    viewModel = viewModel,
                    navigateBack = {},
                    onOtpValidationSuccess = {}
                )
            }
        }
    }
    return OtpValidationScreenRobot(rule).apply(block)
}

class OtpValidationScreenRobot(
    private val rule: AndroidComposeTestRule<ActivityScenarioRule<ComponentActivity>, ComponentActivity>,
) {
    fun typeCode(code: String) {
        rule.onNodeWithTag(TestTags.TEST_TAG_OTP)
            .apply {
                performTextClearance()
                performTextInput(code)
            }
    }

    fun submit() {
        val buttonText = rule.activity.getString(R.string.common_verify)
        rule.onNodeWithText(buttonText)
            // NB: because our form is too large and we're using verticalScroll(rememberScrollState())
            // So we must scrollTo this button to perform click, else the click will not performed
            // and tests will fail.
            .performScrollTo()
            .performClick()
    }

    fun clickResendCode() {
        val resendCode = rule.activity.getString(R.string.otp_validation_screen_resend_code)
        rule.onNodeWithText(resendCode)
            .performClick()
    }

    infix fun verify(
        block: OtpValidationScreenVerification.() -> Unit
    ): OtpValidationScreenVerification {
        return OtpValidationScreenVerification(rule).apply(block)
    }
}

class OtpValidationScreenVerification(
    private val rule: AndroidComposeTestRule<ActivityScenarioRule<ComponentActivity>, ComponentActivity>,
) {
    fun codeRequiredErrorIsDisplayed() {
        val codeRequired = rule.activity.getString(R.string.otp_validation_screen_code_required)
        rule.onNodeWithText(codeRequired)
            .assertIsDisplayed()
    }

    fun codeIncompleteErrorIsDisplayed() {
        val codeRequired =
            rule.activity.getString(R.string.otp_validation_screen_code_not_completed)
        rule.onNodeWithText(codeRequired)
            .assertIsDisplayed()
    }

    fun codeRequiredErrorIsNotDisplayed() {
        val codeRequired = rule.activity.getString(R.string.otp_validation_screen_code_required)
        rule.onNodeWithText(codeRequired)
            .assertIsNotDisplayed()
    }

    fun codeIncompleteErrorIsNotDisplayed() {
        val codeRequired =
            rule.activity.getString(R.string.otp_validation_screen_code_not_completed)
        rule.onNodeWithText(codeRequired)
            .assertIsNotDisplayed()
    }

    fun backendErrorIsDisplayed() {
        rule.onNodeWithText(AuthenticationRepositoryConfigurableImpl.OTP_BACKEND_ERROR)
            .assertIsDisplayed()
    }

    fun networkErrorIsDisplayed() {
        val networkError =
            rule.activity.getString(R.string.common_network_error_verify_and_try_later)
        rule.onNodeWithText(networkError)
            .assertIsDisplayed()
    }

    fun networkErrorIsNotDisplayed() {
        val networkError =
            rule.activity.getString(R.string.common_network_error_verify_and_try_later)
        rule.onNodeWithText(networkError)
            .assertIsNotDisplayed()
    }

    fun resendCodeSuccessIsDisplayed() {
        val resendCodeSuccess =
            rule.activity.getString(R.string.otp_validation_screen_code_resent_success)
        rule.onNodeWithText(resendCodeSuccess)
            .assertIsDisplayed()
    }
}
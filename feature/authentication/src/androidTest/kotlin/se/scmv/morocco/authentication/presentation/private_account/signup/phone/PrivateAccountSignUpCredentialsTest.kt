package se.scmv.morocco.authentication.presentation.private_account.signup.phone

import androidx.activity.ComponentActivity
import androidx.compose.ui.test.junit4.createAndroidComposeRule
import org.junit.Rule
import org.junit.Test

class PrivateAccountSignUpCredentialsTest {

    @get:Rule(order = 0)
    val composeTestRule = createAndroidComposeRule<ComponentActivity>()

    @Test
    fun signUp_PhoneNumberEmpty() {
        launchPrivateAccountSignUpScreen(composeTestRule) {
            typePhoneNumber("")
            submit()
        } verify {
            phoneRequiredErrorIsDisplayed()
        }
    }

    @Test
    fun signUp_phoneNumberFormatInvalid() {
        launchPrivateAccountSignUpScreen(
            rule = composeTestRule,
        ) {
            listOf(
                "*********",
                "*********",
                "15*********",
                "**********",
            ).forEach { phone ->
                typePhoneNumber(phone)
                submit()
                verify {
                    phoneNumberFormatInvalidErrorIsDisplayed()
                }
            }
        }
    }

    @Test
    fun signUp_phoneNumberFormatValid() {
        launchPrivateAccountSignUpScreen(
            rule = composeTestRule,
        ) {
            listOf(
                "*********8",
                "**********",
                "**********",
                "**********",
                "0*********1",
                "*************"
            ).forEach { phone ->
                typePhoneNumber(phone)
                submit()
                verify {
                    phoneHasNoError()
                }
            }
        }
    }
}
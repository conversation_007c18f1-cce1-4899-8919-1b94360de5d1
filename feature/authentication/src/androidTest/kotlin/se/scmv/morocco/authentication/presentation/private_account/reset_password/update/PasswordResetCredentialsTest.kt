package se.scmv.morocco.authentication.presentation.private_account.reset_password.update

import androidx.activity.ComponentActivity
import androidx.compose.ui.test.junit4.createAndroidComposeRule
import org.junit.Rule
import org.junit.Test
import se.scmv.morocco.authentication.data.repository.AuthenticationRepositoryConfigurableImpl

class PasswordResetCredentialsTest {

    @get:Rule
    val composeTestRule = createAndroidComposeRule<ComponentActivity>()

    @Test
    fun resetPassword_passwordRequired() {
        launchPasswordResetScreen(
            composeTestRule
        ) {
            typePassword("")
            typePasswordConfirm("Azerty@123")
            submit()
        } verify {
            passwordRequiredErrorIsDisplayed()
        }
    }

    @Test
    fun resetPassword_passwordConfirmRequired() {
        launchPasswordResetScreen(
            composeTestRule
        ) {
            typePassword("Azerty@123")
            typePasswordConfirm("")
            submit()
        } verify {
            passwordConfirmRequiredErrorIsDisplayed()
        }
    }

    @Test
    fun resetPassword_passwordsDoNotMatch() {
        launchPasswordResetScreen(
            rule = composeTestRule,
            authenticationRepository = AuthenticationRepositoryConfigurableImpl()
        ) {
            listOf(
                "Azerty@123" to "Azerty@12",
                "Azerty@123" to "Azerty@",
                "Azerty@" to "Azerty@123",
                "Azerty@12" to "Azerty@13",
                "azerty@123" to "Azerty@123",
            ).forEach {
                typePassword(it.first)
                typePasswordConfirm(it.second)
                submit()
                verify {
                    passwordDoNotMatchErrorIsDisplayed()
                }
            }
        }
    }
}
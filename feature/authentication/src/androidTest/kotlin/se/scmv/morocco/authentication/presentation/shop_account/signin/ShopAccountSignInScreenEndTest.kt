package se.scmv.morocco.authentication.presentation.shop_account.signin

import androidx.activity.ComponentActivity
import androidx.compose.ui.test.junit4.createAndroidComposeRule
import org.junit.Rule
import org.junit.Test
import se.scmv.morocco.authentication.data.repository.AuthenticationRepositoryConfigurableImpl
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.models.NetworkAndBackendErrors
import se.scmv.morocco.domain.models.NetworkErrors

class ShopAccountSignInScreenEndTest {

    @get:Rule(order = 0)
    val composeTestRule = createAndroidComposeRule<ComponentActivity>()

    @Test
    fun signIn_backendError() {
        launchShopAccountSignInScreen(
            rule = composeTestRule,
            authenticationRepository = AuthenticationRepositoryConfigurableImpl(
                signInResponse = Resource.Failure(
                    NetworkAndBackendErrors.Backend(AuthenticationRepositoryConfigurableImpl.SIGN_IN_ERROR)
                )
            )
        ) {
            typeEmail("<EMAIL>")
            typePassword("Azerty@123")
            submit()
        } verify {
            emailHasNoError()
            passwordHasNoError()
            backendErrorIsDisplayed()
        }
    }

    @Test
    fun signIn_networkError() {
        launchShopAccountSignInScreen(
            rule = composeTestRule,
            authenticationRepository = AuthenticationRepositoryConfigurableImpl(
                signInResponse = Resource.Failure(NetworkAndBackendErrors.Network(NetworkErrors.NO_INTERNET))
            )
        ) {
            typeEmail("<EMAIL>")
            typePassword("Azerty@123")
            submit()
        } verify {
            emailHasNoError()
            passwordHasNoError()
            networkErrorIsDisplayed()
        }
    }

    @Test
    fun signIn_success() {
        launchShopAccountSignInScreen(
            rule = composeTestRule,
            authenticationRepository = AuthenticationRepositoryConfigurableImpl()
        ) {
            typeEmail("<EMAIL>")
            typePassword("Azerty@123")
            submit()
        } verify {
            emailHasNoError()
            passwordHasNoError()
        }
    }
}
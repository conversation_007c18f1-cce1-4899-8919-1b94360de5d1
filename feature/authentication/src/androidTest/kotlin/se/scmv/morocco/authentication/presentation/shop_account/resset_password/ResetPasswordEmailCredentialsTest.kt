package se.scmv.morocco.authentication.presentation.shop_account.resset_password

import androidx.activity.ComponentActivity
import androidx.compose.ui.test.junit4.createAndroidComposeRule
import org.junit.Rule
import org.junit.Test

class ResetPasswordEmailCredentialsTest {

    @get:Rule
    val composeTestRule = createAndroidComposeRule<ComponentActivity>()

    @Test
    fun resetPassword_required() {
        launchResetPasswordEmailScreen(
            composeTestRule
        ) {
            typeEmail("")
            submit()
        } verify {
            emailRequiredErrorIsDisplayed()
        }
    }

    @Test
    fun resetPassword_phoneNumberFormatInvalid() {
        launchResetPasswordEmailScreen(
            rule = composeTestRule,
        ) {
            listOf(
                "example.com",
                "user@@example.com",
                "user@example@com",
                "user@exam!ple.com",
                "user@ example.com",
                "  <EMAIL>",
                "<EMAIL>  ",
                "üñîçødë@example.com",
                "user@.com",
                "<EMAIL>.",
            ).forEach { phone ->
                typeEmail(phone)
                submit()
                verify {
                    emailFormatInvalidErrorIsDisplayed()
                }
            }
        }
    }

    @Test
    fun resetPassword_phoneNumberFormatValid() {
        launchResetPasswordEmailScreen(
            rule = composeTestRule,
        ) {
            listOf(
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
            ).forEach { phone ->
                typeEmail(phone)
                submit()
                verify {
                    emailHasNoError()
                }
            }
        }
    }
}
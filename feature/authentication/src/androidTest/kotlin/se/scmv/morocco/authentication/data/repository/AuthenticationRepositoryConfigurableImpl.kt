package se.scmv.morocco.authentication.data.repository

import se.scmv.morocco.domain.models.NetworkAndBackendErrors
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.models.ShopCategory
import se.scmv.morocco.domain.models.ShopSubscription
import se.scmv.morocco.domain.repositories.AuthenticationRepository

class AuthenticationRepositoryConfigurableImpl(
    private val signInResponse: Resource<String, NetworkAndBackendErrors> = Resource.Success(TOKEN),
    private val sendSmsVerificationResponse: Resource<String, NetworkAndBackendErrors> = Resource.Success(
        PHONE
    ),
    private val sendSmsVerificationForPasswordResetResponse: Resource<String, NetworkAndBackendErrors> = Resource.Success(
        PHONE
    ),
    private val sendPasswordResetLinkResponse: Resource<String, NetworkAndBackendErrors> = Resource.Success(
        EMAIL
    ),
    private val validatePhoneForPasswordResetResponse: Resource<Boolean, NetworkAndBackendErrors> = Resource.Success(true),
    private val signUpResponse: Resource<String, NetworkAndBackendErrors> = Resource.Success(PHONE),
    private val shopSignUpResponse: Resource<Unit, NetworkAndBackendErrors> = Resource.Success(Unit),
    private val resetPasswordResponse: Resource<String, NetworkAndBackendErrors> = Resource.Success(
        TOKEN
    ),
) : AuthenticationRepository {

    companion object {
        private const val TOKEN = "some fake token"
        private const val PHONE = "06444444"
        private const val EMAIL = "<EMAIL>"
        const val SIGN_IN_ERROR = "Email or password incorrect!"
        const val PHONE_NUMBER_SIGN_UP_BACKEND_ERROR = "Phone number invalid!"
        const val RESET_PASSWORD_EMAIL_BACKEND_ERROR = "Email invalid!"
        const val OTP_BACKEND_ERROR = "Verification code number invalid!"
        const val USERS_NAME_INVALID = "Le nom ne doit contenir que des lettres"
        const val USER_ACTIVATION_CODE_INVALID = "La clé d'activation a expiré."
    }

    override suspend fun signIn(
        emailOrPhone: String,
        password: String
    ): Resource<String, NetworkAndBackendErrors> {
        return signInResponse
    }

    override suspend fun signInWithGoogle(
        idToken: String
    ): Resource<String, NetworkAndBackendErrors> {
        return signInResponse
    }

    override suspend fun sendSmsVerificationForSignUp(
        phoneNumber: String
    ): Resource<String, NetworkAndBackendErrors> {
        return sendSmsVerificationResponse
    }

    override suspend fun sendSmsVerificationForPasswordReset(phoneNumber: String): Resource<String, NetworkAndBackendErrors> {
        return sendSmsVerificationForPasswordResetResponse
    }

    override suspend fun validatePhone(phoneNumber: String?, code: String): Resource<Boolean, NetworkAndBackendErrors> {
        return validatePhoneForPasswordResetResponse
    }

    override suspend fun sendPasswordResetLink(email: String): Resource<String, NetworkAndBackendErrors> {
        return sendPasswordResetLinkResponse
    }

    override suspend fun registerAccount(
        phoneNumber: String,
        otpCode: String?,
        fullName: String,
        password: String,
    ): Resource<String, NetworkAndBackendErrors> {
        return signUpResponse
    }

    override suspend fun registerAccount(
        fullName: String,
        phoneNumber: String,
        email: String,
        shopCategory: ShopCategory,
        shopSubscription: ShopSubscription
    ): Resource<Unit, NetworkAndBackendErrors> {
        return shopSignUpResponse
    }

    override suspend fun resetPassword(
        code: String,
        newPassword: String
    ): Resource<String, NetworkAndBackendErrors> {
        return resetPasswordResponse
    }

    override suspend fun updatePassword(
        currentPassword: String,
        newPassword: String
    ): Resource<Unit, NetworkAndBackendErrors> {
        return Resource.Success(Unit)
    }

    override suspend fun signOut(messagingToken: String?): Resource<Boolean, NetworkAndBackendErrors> {
        return Resource.Success(true)
    }
}
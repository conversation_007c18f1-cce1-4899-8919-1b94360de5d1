package se.scmv.morocco.authentication.presentation.shop_account.signin

import androidx.activity.ComponentActivity
import androidx.compose.ui.test.junit4.createAndroidComposeRule
import org.junit.Rule
import org.junit.Test
import se.scmv.morocco.authentication.data.repository.AuthenticationRepositoryConfigurableImpl

class ShopAccountSignInScreenCredentialsTest {

    @get:Rule(order = 0)
    val composeTestRule = createAndroidComposeRule<ComponentActivity>()

    @Test
    fun signIn_emailEmpty() {
        launchShopAccountSignInScreen(
            rule = composeTestRule,
            authenticationRepository = AuthenticationRepositoryConfigurableImpl()
        ) {
            typeEmail("")
            typePassword("123AAqq")
            submit()
        } verify {
            emailRequiredErrorIsDisplayed()
        }
    }

    @Test
    fun signIn_passwordEmpty() {
        launchShopAccountSignInScreen(
            rule = composeTestRule,
            authenticationRepository = AuthenticationRepositoryConfigurableImpl()
        ) {
            typeEmail("<EMAIL>")
            typePassword("")
            submit()
        } verify {
            passwordRequiredErrorIsDisplayed()
        }
    }

    @Test
    fun signIn_emailFormatInvalid() {
        launchShopAccountSignInScreen(
            rule = composeTestRule,
            authenticationRepository = AuthenticationRepositoryConfigurableImpl()
        ) {
            listOf(
                "example.com",
                "user@@example.com",
                "user@example@com",
                "user@exam!ple.com",
                "user@ example.com",
                "  <EMAIL>",
                "<EMAIL>  ",
                "üñîçødë@example.com",
                "user@.com",
                "<EMAIL>.",
            ).forEach { email ->
                typeEmail(email)
                typePassword("123AAqq")
                submit()
                verify {
                    emailFormatInvalidErrorIsDisplayed()
                }
            }
        }
    }

    @Test
    fun signIn_emailFormatValid() {
        launchShopAccountSignInScreen(
            rule = composeTestRule,
            authenticationRepository = AuthenticationRepositoryConfigurableImpl()
        ) {
            listOf(
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
            ).forEach { email ->
                typeEmail(email)
                typePassword("123AAqq")
                submit()
                verify {
                    emailHasNoError()
                }
            }
        }
    }

    @Test
    fun signIn_passwordFormatValid() {
        launchShopAccountSignInScreen(
            rule = composeTestRule,
            authenticationRepository = AuthenticationRepositoryConfigurableImpl()
        ) {
            listOf(
                "1",
                "12",
                "123",
                "123AAqq",
            ).forEach { password ->
                typeEmail("<EMAIL>")
                typePassword(password)
                submit()
                verify {
                    passwordHasNoError()
                }
            }
        }
    }
}
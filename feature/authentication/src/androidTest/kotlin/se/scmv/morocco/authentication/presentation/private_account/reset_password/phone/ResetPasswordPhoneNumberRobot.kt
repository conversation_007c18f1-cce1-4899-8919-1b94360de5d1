package se.scmv.morocco.authentication.presentation.private_account.reset_password.phone

import androidx.activity.ComponentActivity
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.ui.Modifier
import androidx.compose.ui.test.assertIsDisplayed
import androidx.compose.ui.test.assertIsNotDisplayed
import androidx.compose.ui.test.junit4.AndroidComposeTestRule
import androidx.compose.ui.test.onNodeWithTag
import androidx.compose.ui.test.onNodeWithText
import androidx.compose.ui.test.performClick
import androidx.compose.ui.test.performTextClearance
import androidx.compose.ui.test.performTextInput
import androidx.test.ext.junit.rules.ActivityScenarioRule
import se.scmv.morocco.authentication.R
import se.scmv.morocco.authentication.data.repository.AuthenticationRepositoryConfigurableImpl
import se.scmv.morocco.authentication.domain.validators.CredentialsValidator
import se.scmv.morocco.authentication.presentation.common.TestTags
import se.scmv.morocco.authentication.presentation.resset_password.private_account.phone.ResetPasswordPhoneNumberRoute
import se.scmv.morocco.authentication.presentation.resset_password.private_account.phone.ResetPasswordPhoneNumberViewModel
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.domain.repositories.AuthenticationRepository
import se.scmv.morocco.ui.SnackBarHostForSnackBarController

fun launchResetPasswordPhoneNumberScreen(
    rule: AndroidComposeTestRule<ActivityScenarioRule<ComponentActivity>, ComponentActivity>,
    authenticationRepository: AuthenticationRepository = AuthenticationRepositoryConfigurableImpl(),
    block: ResetPasswordPhoneNumberRobot.() -> Unit
): ResetPasswordPhoneNumberRobot {
    val viewModel = ResetPasswordPhoneNumberViewModel(
        credentialsValidator = CredentialsValidator(),
        authenticationRepository = authenticationRepository
    )
    rule.setContent {
        AvitoTheme {
            Scaffold(
                snackbarHost = { SnackBarHostForSnackBarController() }
            ) {
                ResetPasswordPhoneNumberRoute(
                    modifier = Modifier.padding(it),
                    viewModel = viewModel,
                    navigateToOtpValidation = {}
                )
            }
        }
    }
    return ResetPasswordPhoneNumberRobot(rule).apply(block)
}

class ResetPasswordPhoneNumberRobot(
    private val rule: AndroidComposeTestRule<ActivityScenarioRule<ComponentActivity>, ComponentActivity>
) {
    fun typePhoneNumber(phone: String) {
        rule.onNodeWithTag(TestTags.TEST_TAG_PHONE_NUMBER)
            .apply {
                performTextClearance()
                performTextInput(phone)
            }
    }

    fun submit() {
        val buttonText = rule.activity.getString(R.string.common_continue)
        rule.onNodeWithText(buttonText)
            .performClick()
    }

    infix fun verify(
        block: ResetPasswordPhoneNumberVerification.() -> Unit
    ): ResetPasswordPhoneNumberVerification {
        return ResetPasswordPhoneNumberVerification(rule).apply { block() }
    }
}

class ResetPasswordPhoneNumberVerification(
    private val rule: AndroidComposeTestRule<ActivityScenarioRule<ComponentActivity>, ComponentActivity>
) {
    fun phoneRequiredErrorIsDisplayed() {
        val phoneRequired = rule.activity.getString(R.string.common_phone_number_field_required)
        rule.onNodeWithText(phoneRequired)
            .assertIsDisplayed()
    }

    fun phoneNumberFormatInvalidErrorIsDisplayed() {
        val phoneInvalid =
            rule.activity.getString(R.string.common_phone_number_field_format_invalid)
        rule.onNodeWithText(phoneInvalid)
            .assertIsDisplayed()
    }

    fun phoneHasNoError() {
        val phoneInvalid =
            rule.activity.getString(R.string.common_phone_number_field_format_invalid)
        rule.onNodeWithText(phoneInvalid)
            .assertIsNotDisplayed()
    }

    fun backendErrorIsDisplayed() {
        rule.onNodeWithText(AuthenticationRepositoryConfigurableImpl.PHONE_NUMBER_SIGN_UP_BACKEND_ERROR)
            .assertIsDisplayed()
    }

    fun backendErrorIsNotDisplayed() {
        rule.onNodeWithText(AuthenticationRepositoryConfigurableImpl.PHONE_NUMBER_SIGN_UP_BACKEND_ERROR)
            .assertIsNotDisplayed()
    }

    fun networkErrorIsDisplayed() {
        val networkError =
            rule.activity.getString(R.string.common_network_error_verify_and_try_later)
        rule.onNodeWithText(networkError)
            .assertIsDisplayed()
    }

    fun networkErrorIsNotDisplayed() {
        val networkError =
            rule.activity.getString(R.string.common_network_error_verify_and_try_later)
        rule.onNodeWithText(networkError)
            .assertIsNotDisplayed()
    }
}
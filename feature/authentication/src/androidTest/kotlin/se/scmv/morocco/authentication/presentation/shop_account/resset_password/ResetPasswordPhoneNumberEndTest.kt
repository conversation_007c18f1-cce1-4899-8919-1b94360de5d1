package se.scmv.morocco.authentication.presentation.shop_account.resset_password

import androidx.activity.ComponentActivity
import androidx.compose.ui.test.junit4.createAndroidComposeRule
import org.junit.Rule
import org.junit.Test
import se.scmv.morocco.authentication.data.repository.AuthenticationRepositoryConfigurableImpl
import se.scmv.morocco.domain.models.NetworkAndBackendErrors
import se.scmv.morocco.domain.models.NetworkErrors
import se.scmv.morocco.domain.models.Resource

class ResetPasswordPhoneNumberEndTest {

    @get:Rule
    val composeTestRule = createAndroidComposeRule<ComponentActivity>()

    @Test
    fun resetPassword_BackendError() {
        launchResetPasswordEmailScreen(
            rule = composeTestRule,
            authenticationRepository = AuthenticationRepositoryConfigurableImpl(
                sendPasswordResetLinkResponse = Resource.Failure(
                    NetworkAndBackendErrors.Backend(
                        AuthenticationRepositoryConfigurableImpl.RESET_PASSWORD_EMAIL_BACKEND_ERROR
                    )
                )
            )
        ) {
            typeEmail("<EMAIL>")
            submit()
        } verify {
            emailHasNoError()
            backendErrorIsDisplayed()
        }
    }

    @Test
    fun resetPassword_NetworkError() {
        launchResetPasswordEmailScreen(
            rule = composeTestRule,
            authenticationRepository = AuthenticationRepositoryConfigurableImpl(
                sendPasswordResetLinkResponse = Resource.Failure(
                    NetworkAndBackendErrors.Network(NetworkErrors.NO_INTERNET)
                )
            )
        ) {
            typeEmail("<EMAIL>")
            submit()
        } verify {
            emailHasNoError()
            networkErrorIsDisplayed()
        }
    }

    @Test
    fun resetPassword_success() {
        launchResetPasswordEmailScreen(
            rule = composeTestRule,
            authenticationRepository = AuthenticationRepositoryConfigurableImpl()
        ) {
            typeEmail("<EMAIL>")
            submit()
        } verify {
            emailHasNoError()
            networkErrorIsNotDisplayed()
            backendErrorIsNotDisplayed()
        }
    }
}
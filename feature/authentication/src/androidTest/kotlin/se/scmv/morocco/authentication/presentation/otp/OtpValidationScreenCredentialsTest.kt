package se.scmv.morocco.authentication.presentation.otp

import androidx.activity.ComponentActivity
import androidx.compose.ui.test.junit4.createAndroidComposeRule
import org.junit.Rule
import org.junit.Test

class OtpValidationScreenCredentialsTest {

    @get:Rule
    val rule = createAndroidComposeRule<ComponentActivity>()

    @Test
    fun emptyCode() {
        launchOtpValidationScreen(rule) {
            typeCode("")
            submit()
        } verify {
            codeRequiredErrorIsDisplayed()
        }
    }

    @Test
    fun incompleteCode() {
        launchOtpValidationScreen(rule) {
            listOf(
                "1",
                "12",
                "123",
                "12345",
            ).forEach {
                typeCode(it)
                submit()
                verify {
                    codeIncompleteErrorIsDisplayed()
                }
            }
        }
    }

    @Test
    fun completeCode() {
        launchOtpValidationScreen(rule) {
            listOf(
                "123455",
                "12ssdd",
                "123Add",
                "123451",
            ).forEach {
                typeCode(it)
                submit()
                verify {
                    codeRequiredErrorIsNotDisplayed()
                    codeIncompleteErrorIsNotDisplayed()
                }
            }
        }
    }
}
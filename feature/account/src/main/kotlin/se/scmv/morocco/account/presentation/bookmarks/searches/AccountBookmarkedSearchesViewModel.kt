package se.scmv.morocco.account.presentation.bookmarks.searches

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import se.scmv.morocco.account.R
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.domain.models.BookmarkedSearch
import se.scmv.morocco.domain.models.BookmarkedSearchQuery
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.repositories.AccountRepository
import se.scmv.morocco.ui.asUiText
import se.scmv.morocco.ui.renderFailure
import se.scmv.morocco.ui.renderSuccess
import javax.inject.Inject

@OptIn(ExperimentalCoroutinesApi::class)
@HiltViewModel
class AccountBookmarkedSearchesViewModel @Inject constructor(
    private val accountRepository: AccountRepository
) : ViewModel() {

    private val refreshTrigger = MutableSharedFlow<Unit>()

    private val _viewState = MutableStateFlow(AccountBookmarkedSearchesViewState(loading = true))
    val viewState = _viewState.asStateFlow()

    private val _oneTimeEvents = MutableSharedFlow<AccountBookmarkedSearchesDeleteSearchStatus>()
    val oneTimeEvents = _oneTimeEvents.asSharedFlow()

    init {
        viewModelScope.launch {
            refreshTrigger.onStart { emit(Unit) }.flatMapLatest {
                showLoading()
                accountRepository.getBookmarkedSearches()
            }.collectLatest { result ->
                hideLoading()
                _viewState.update { state ->
                    when (result) {
                        is Resource.Success -> AccountBookmarkedSearchesViewState(
                            searches = result.data.sortedByDescending { it.date }.toImmutableList(),
                            loading = false
                        )

                        is Resource.Failure -> state.copy(
                            errorMessage = result.error.asUiText(),
                            loading = false
                        )
                    }
                }
            }
        }
    }

    fun onRefresh() {
        viewModelScope.launch { refreshTrigger.emit(Unit) }
    }

    fun onUnBookmarkSearch(bookmarkedSearch: BookmarkedSearch) {
        viewModelScope.launch {
            _oneTimeEvents.emit(AccountBookmarkedSearchesDeleteSearchStatus(true))
            val searchQuery = with(bookmarkedSearch) { BookmarkedSearchQuery(id, searchQuery) }
            val result = accountRepository.unBookmarkSearch(searchQuery)
            _oneTimeEvents.emit(AccountBookmarkedSearchesDeleteSearchStatus(false))
            when (result) {
                is Resource.Success -> {
                    renderSuccess(UiText.FromRes(R.string.bookmarked_searches_screen_delete_search_success))
                }

                is Resource.Failure -> renderFailure(error = result.error)
            }
        }
    }

    private fun showLoading() {
        _viewState.update { it.copy(loading = true) }
    }

    private fun hideLoading() {
        _viewState.update { it.copy(loading = false) }
    }
}
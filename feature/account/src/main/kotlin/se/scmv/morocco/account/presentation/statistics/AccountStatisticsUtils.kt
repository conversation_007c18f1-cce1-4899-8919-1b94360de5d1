package se.scmv.morocco.account.presentation.statistics

import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import kotlinx.datetime.Instant
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime
import se.scmv.morocco.common.lang.LocaleManager
import se.scmv.morocco.domain.models.AccountStatisticsMetricName
import java.time.format.TextStyle
import java.util.Locale

internal const val TOOLTIP_WIDTH = 120
internal const val CHART_GRID_COLOR_ALPHA = 0.3F
internal const val CHART_OVERLAY_LINE_COLOR_ALPHA = 0.3F
internal const val CHART_OVERLAY_SURFACE_COLOR_ALPHA = 0.3F

internal fun Color.toLinearGradient(): Brush = Brush.linearGradient(
    colors = listOf(this, copy(alpha = 0.7f), copy(alpha = 0.5f),)
)

internal fun AccountStatisticsMetricName.localizedName() = when (this) {
    AccountStatisticsMetricName.PUBLISH -> se.scmv.morocco.ui.R.string.statistics_name_published_ads
    AccountStatisticsMetricName.DELETE -> se.scmv.morocco.ui.R.string.statistics_name_deleted_ads
    AccountStatisticsMetricName.GALLERY -> se.scmv.morocco.ui.R.string.statistics_name_premium_ads
    AccountStatisticsMetricName.HIGHLIGHT -> se.scmv.morocco.ui.R.string.statistics_name_highlighted_ads
    AccountStatisticsMetricName.BUMP -> se.scmv.morocco.ui.R.string.statistics_name_renew
    AccountStatisticsMetricName.VIEW -> se.scmv.morocco.ui.R.string.statistics_name_views
    AccountStatisticsMetricName.PHONEVIEW -> se.scmv.morocco.ui.R.string.statistics_name_leads
    AccountStatisticsMetricName.CONVERSATION -> se.scmv.morocco.ui.R.string.statistics_name_conversations
}

internal fun AccountStatisticsMetricName.icon() = when (this) {
    AccountStatisticsMetricName.PUBLISH -> se.scmv.morocco.ui.R.drawable.ic_timer_circle
    AccountStatisticsMetricName.DELETE -> se.scmv.morocco.ui.R.drawable.ic_deleted_circle
    AccountStatisticsMetricName.GALLERY -> se.scmv.morocco.ui.R.drawable.ic_boosts_circle
    AccountStatisticsMetricName.HIGHLIGHT -> se.scmv.morocco.ui.R.drawable.ic_boosts_circle
    AccountStatisticsMetricName.BUMP -> se.scmv.morocco.ui.R.drawable.ic_boosts_circle
    AccountStatisticsMetricName.VIEW -> se.scmv.morocco.ui.R.drawable.ic_views_circle
    AccountStatisticsMetricName.PHONEVIEW -> se.scmv.morocco.ui.R.drawable.ic_calls_circle
    AccountStatisticsMetricName.CONVERSATION -> se.scmv.morocco.ui.R.drawable.ic_messages_circle
}

internal fun AccountStatisticsMetricName.color() = when (this) {
    AccountStatisticsMetricName.PUBLISH -> Color(0xFF00C160)
    AccountStatisticsMetricName.DELETE -> Color(0xFFFF4353)
    AccountStatisticsMetricName.GALLERY -> Color(0xFF8871FF)
    AccountStatisticsMetricName.HIGHLIGHT -> Color(0xFF8871FF)
    AccountStatisticsMetricName.BUMP -> Color(0xFF8871FF)
    AccountStatisticsMetricName.VIEW -> Color(0xFFFC942D)
    AccountStatisticsMetricName.PHONEVIEW -> Color(0xFF00C160)
    AccountStatisticsMetricName.CONVERSATION -> Color(0xFF2E6BFF)
}

internal fun Long.toTooltipHeaderLabel(): String = Instant.fromEpochMilliseconds(this)
    .toLocalDateTime(TimeZone.currentSystemDefault())
    .let {
        "${it.dayOfMonth} ${
            it.month.getDisplayName(
                TextStyle.FULL,
                Locale.forLanguageTag(LocaleManager.getCurrentLanguage())
            )
        }"
    }

internal fun Long.toXAxisLabel(): String = Instant.fromEpochMilliseconds(this)
    .toLocalDateTime(TimeZone.currentSystemDefault())
    .let {
        "${it.dayOfMonth} ${
            it.month.getDisplayName(
                TextStyle.SHORT,
                Locale.forLanguageTag(LocaleManager.getCurrentLanguage())
            )
        }"
    }
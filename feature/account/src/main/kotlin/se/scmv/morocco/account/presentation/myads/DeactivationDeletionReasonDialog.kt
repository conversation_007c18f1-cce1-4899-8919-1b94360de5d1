package se.scmv.morocco.account.presentation.myads

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.RadioButton
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.window.Dialog
import se.scmv.morocco.account.R
import se.scmv.morocco.designsystem.components.AvConfirmationAlertDialog
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.designsystem.utils.localizedNameRes
import se.scmv.morocco.designsystem.utils.localizedResId
import se.scmv.morocco.domain.models.AccountAdDeactivationReason
import se.scmv.morocco.domain.models.AccountAdDeactivationSoldOnSiteDuration
import se.scmv.morocco.domain.models.AccountDeactivationReasonInput

@Composable
fun DeactivationReasonDialog(
    isDeactivateDialogVisible: Boolean,
    onDismiss: () -> Unit,
    onDeactivate: (AccountDeactivationReasonInput) -> Unit, // Updated callback
    selectedReason: String?,
    onReasonSelected: (String) -> Unit
) {
    val customReason = remember { mutableStateOf("") }
    val selectedDuration =
        remember { mutableStateOf<AccountAdDeactivationSoldOnSiteDuration?>(null) }

    if (isDeactivateDialogVisible) {
        Dialog(onDismissRequest = onDismiss) {
            Surface(
                shape = RoundedCornerShape(MaterialTheme.dimens.regular),
                color = MaterialTheme.colorScheme.background,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = MaterialTheme.dimens.default)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(
                            start = MaterialTheme.dimens.bottomSheetPaddingHorizontal,
                            end = MaterialTheme.dimens.bottomSheetPaddingHorizontal,
                            top = MaterialTheme.dimens.default,
                            bottom = MaterialTheme.dimens.tiny
                        ),
                    verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small)
                ) {
                    // Dialog Subtitle
                    Text(
                        text = stringResource(id = R.string.deactivation_subtitle),
                        style = MaterialTheme.typography.titleMedium,
                        modifier = Modifier.fillMaxWidth()
                    )
                    // List of reasons with radio buttons
                    val reasons = AccountAdDeactivationReason.values()

                    reasons.forEach { reason: AccountAdDeactivationReason ->
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier
                                .clickable { onReasonSelected(reason.name) }
                                .heightIn(max = MaterialTheme.dimens.extraBig) // Set the maximum height here
                                .wrapContentWidth() // Ensures the Row only takes as much width as needed

                        ) {
                            RadioButton(
                                selected = selectedReason == reason.name,
                                onClick = { onReasonSelected(reason.name) }
                            )
                            Text(
                                text = stringResource(id = reason.localizedResId()),
                                style = MaterialTheme.typography.bodySmall
                            )
                        }

                        // Show duration options if SOLD_ON_SITE is selected
                        if (reason == AccountAdDeactivationReason.SOLD_ON_SITE && selectedReason == reason.name) {
                            Column(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(start = MaterialTheme.dimens.bigger)
                            ) {
                                AccountAdDeactivationSoldOnSiteDuration.values()
                                    .forEach { duration ->
                                        Row(
                                            verticalAlignment = Alignment.CenterVertically,
                                            modifier = Modifier
                                                .clickable { selectedDuration.value = duration }
                                                .heightIn(max = MaterialTheme.dimens.extraBig) // Set the maximum height here
                                        ) {
                                            RadioButton(
                                                selected = selectedDuration.value == duration,
                                                onClick = { selectedDuration.value = duration }
                                            )
                                            Text(
                                                text = stringResource(id = duration.localizedNameRes()),
                                                style = MaterialTheme.typography.bodySmall
                                            )
                                        }
                                    }
                            }
                        }

                        // Show text input if "OTHER" is selected
                        if (reason == AccountAdDeactivationReason.OTHER && selectedReason == reason.name) {
                            Column(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(
                                        start = MaterialTheme.dimens.bigger,
                                        top = MaterialTheme.dimens.small
                                    )
                            ) {
                                OutlinedTextField(
                                    value = customReason.value,
                                    onValueChange = { customReason.value = it },
                                    modifier = Modifier.fillMaxWidth(),
                                    placeholder = {
                                        Text(
                                            text = stringResource(id = R.string.other_placeholder),
                                            style = MaterialTheme.typography.bodySmall
                                        )
                                    },
                                    textStyle = MaterialTheme.typography.bodySmall
                                )
                            }
                        }
                    }

                    // Action Buttons with adjusted spacing
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = MaterialTheme.dimens.small),
                        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small)
                    ) {
                        TextButton(onClick = onDismiss) {
                            Text(text = stringResource(id = R.string.cancel))
                        }
                        Spacer(modifier = Modifier.width(MaterialTheme.dimens.small))
                        TextButton(
                            onClick = {
                                val reasonEnum = mapReasonToEnum(selectedReason)
                                val input = AccountDeactivationReasonInput(
                                    reason = reasonEnum,
                                    soldOnSiteDuration = if (reasonEnum == AccountAdDeactivationReason.SOLD_ON_SITE) selectedDuration.value else null,
                                    otherReasonText = if (reasonEnum == AccountAdDeactivationReason.OTHER) customReason.value else null
                                )
                                onDeactivate(input)
                            }
                        ) {
                            Text(text = stringResource(id = R.string.deactivate))
                        }
                    }
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun PreviewDeactivationReasonDialog() {
    var isDialogVisible by remember { mutableStateOf(true) }
    val selectedReason = remember { mutableStateOf<String?>(null) }
    val customReason = remember { mutableStateOf("") }
    val selectedDuration =
        remember { mutableStateOf<AccountAdDeactivationSoldOnSiteDuration?>(null) }

    DeactivationReasonDialog(
        isDeactivateDialogVisible = isDialogVisible,
        onDismiss = { isDialogVisible = false },
        onDeactivate = { input ->
            // Handle deactivation logic here
        },
        selectedReason = selectedReason.value,
        onReasonSelected = { reason -> selectedReason.value = reason }
    )
}


@Composable
fun DeletionReasonDialog(
    isDeletionDialogVisible: Boolean,
    onDismiss: () -> Unit,
    onDelete: (AccountDeactivationReasonInput) -> Unit,
    selectedReason: String?,
    onReasonSelected: (String) -> Unit
) {
    val customReason = remember { mutableStateOf("") }
    val selectedDuration =
        remember { mutableStateOf<AccountAdDeactivationSoldOnSiteDuration?>(null) }

    if (isDeletionDialogVisible) {
        Dialog(onDismissRequest = onDismiss) {
            Surface(
                shape = RoundedCornerShape(MaterialTheme.dimens.regular),
                color = MaterialTheme.colorScheme.background,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = MaterialTheme.dimens.default)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(
                            start = MaterialTheme.dimens.bottomSheetPaddingHorizontal,
                            end = MaterialTheme.dimens.bottomSheetPaddingHorizontal,
                            top = MaterialTheme.dimens.default,
                            bottom = MaterialTheme.dimens.tiny
                        ),
                    verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small)
                ) {
                    // Dialog Subtitle
                    Text(
                        text = stringResource(id = R.string.deletion_subtitle),
                        style = MaterialTheme.typography.titleMedium,
                        modifier = Modifier.fillMaxWidth()
                    )
                    // List of reasons with radio buttons
                    val reasons = AccountAdDeactivationReason.values()

                    reasons.forEach { reason: AccountAdDeactivationReason ->
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier
                                .clickable { onReasonSelected(reason.name) }
                                .heightIn(max = MaterialTheme.dimens.extraBig) // Set the maximum height here
                                .wrapContentWidth() // Ensures the Row only takes as much width as needed
                        ) {
                            RadioButton(
                                selected = selectedReason == reason.name,
                                onClick = { onReasonSelected(reason.name) }
                            )
                            Text(
                                text = stringResource(id = reason.localizedResId()),
                                style = MaterialTheme.typography.bodySmall
                            )
                        }

                        // Show duration options if SOLD_ON_SITE is selected
                        if (reason == AccountAdDeactivationReason.SOLD_ON_SITE && selectedReason == reason.name) {
                            Column(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(start = MaterialTheme.dimens.bigger)
                            ) {
                                AccountAdDeactivationSoldOnSiteDuration.values()
                                    .forEach { duration ->
                                        Row(
                                            verticalAlignment = Alignment.CenterVertically,
                                            modifier = Modifier
                                                .clickable { selectedDuration.value = duration }
                                                .heightIn(max = MaterialTheme.dimens.extraBig) // Set the maximum height here
                                        ) {
                                            RadioButton(
                                                selected = selectedDuration.value == duration,
                                                onClick = { selectedDuration.value = duration }
                                            )
                                            Text(
                                                text = stringResource(id = duration.localizedNameRes()),
                                                style = MaterialTheme.typography.bodySmall
                                            )
                                        }
                                    }
                            }
                        }

                        // Show text input if "OTHER" is selected
                        if (reason == AccountAdDeactivationReason.OTHER && selectedReason == reason.name) {
                            Column(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(
                                        start = MaterialTheme.dimens.bigger,
                                        top = MaterialTheme.dimens.small
                                    )
                            ) {
                                OutlinedTextField(
                                    value = customReason.value,
                                    onValueChange = { customReason.value = it },
                                    modifier = Modifier.fillMaxWidth(),
                                    placeholder = {
                                        Text(
                                            text = stringResource(id = R.string.other_placeholder),
                                            style = MaterialTheme.typography.bodySmall
                                        )
                                    },
                                    textStyle = MaterialTheme.typography.bodySmall
                                )
                            }
                        }
                    }

                    // Action Buttons with adjusted spacing
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = MaterialTheme.dimens.small),
                        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small)
                    ) {
                        TextButton(onClick = onDismiss) {
                            Text(text = stringResource(id = R.string.cancel))
                        }
                        Spacer(modifier = Modifier.width(MaterialTheme.dimens.small))
                        TextButton(
                            onClick = {
                                val reasonEnum = mapReasonToEnum(selectedReason)
                                val input = AccountDeactivationReasonInput(
                                    reason = reasonEnum,
                                    soldOnSiteDuration = if (reasonEnum == AccountAdDeactivationReason.SOLD_ON_SITE) selectedDuration.value else null,
                                    otherReasonText = if (reasonEnum == AccountAdDeactivationReason.OTHER) customReason.value else null
                                )
                                onDelete(input)
                            }
                        ) {
                            Text(text = stringResource(id = R.string.delete))
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun ShowDeletePopup(remainingSeconds: Int, onDismiss: () -> Unit) {
    // Convert seconds to days, hours, minutes
    val days = remainingSeconds / (24 * 3600)
    val hours = (remainingSeconds % (24 * 3600)) / 3600
    val minutes = (remainingSeconds % 3600) / 60

    val readableTime = buildString {
        if (days > 0) append("$days ${stringResource(R.string.days)} ")
        if (hours > 0) append("$hours ${stringResource(R.string.hours)} ")
        if (minutes > 0) append(
            "$minutes ${stringResource(R.string.minutes)} "
        )
    }.trim()

    AvConfirmationAlertDialog(
        title = stringResource(R.string.delete_popup_title),
        description = stringResource(R.string.delete_popup_message, readableTime),
        onConfirm = {
            onDismiss()
        },
        confirmText = stringResource(R.string.ok),
        cancelText = stringResource(R.string.cancel),
        onCancel = {
            onDismiss()
        },
    )
}

// Helper function to map string reason to enum
private fun mapReasonToEnum(reason: String?): AccountAdDeactivationReason {
    return when (reason) {
        null -> AccountAdDeactivationReason.OTHER
        else -> AccountAdDeactivationReason.valueOf(reason)
    }
}
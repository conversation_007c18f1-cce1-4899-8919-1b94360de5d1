package se.scmv.morocco.account.presentation.edit_account

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import se.scmv.morocco.account.R
import se.scmv.morocco.account.presentation.edit_account.EditAccountViewState.Nothing
import se.scmv.morocco.account.presentation.edit_account.EditAccountViewState.Private
import se.scmv.morocco.account.presentation.edit_account.EditAccountViewState.Shop
import se.scmv.morocco.authentication.domain.validators.CredentialsValidator
import se.scmv.morocco.designsystem.components.DropdownData
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.domain.models.Account
import se.scmv.morocco.domain.models.NetworkAndBackendErrors
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.repositories.AccountRepository
import se.scmv.morocco.domain.repositories.ConfigRepository
import se.scmv.morocco.domain.usecases.LogoutUseCase
import se.scmv.morocco.ui.SnackBarAction
import se.scmv.morocco.ui.SnackBarController
import se.scmv.morocco.ui.SnackBarEvent
import se.scmv.morocco.ui.SnackBarType
import se.scmv.morocco.ui.renderFailure
import se.scmv.morocco.ui.renderSuccess
import javax.inject.Inject

@HiltViewModel
class EditAccountViewModel @Inject constructor(
    private val accountRepository: AccountRepository,
    private val configRepository: ConfigRepository,
    private val credentialsValidator: CredentialsValidator,
    private val logoutUseCase: LogoutUseCase,
) : ViewModel() {

    private val _viewState = MutableStateFlow<EditAccountViewState>(Nothing)
    val viewState = _viewState.asStateFlow()

    private val _oneTimeEvents = MutableSharedFlow<EditAccountOneTimeEvents>()
    val oneTimeEvents = _oneTimeEvents.asSharedFlow()

    init {
        getCurrentAccount()
    }

    fun onNameChanged(name: String) {
        _viewState.update { state ->
            when (state) {
                Nothing -> state
                is Private -> state.copy(name = name, nameError = null)
                is Shop -> state.copy(name = name, nameError = null)
            }
        }
    }

    fun onEmailChanged(email: String) {
        _viewState.update { state -> (state as Private).copy(email = email, emailError = null) }
    }

    fun onPhoneNumberChanged(phone: String) {
        _viewState.update { state -> (state as Private).copy(phone = phone, phoneError = null) }
    }

    fun onPhoneVisibilityChanged(phoneVisibility: Boolean) {
        _viewState.update { state -> (state as Private).copy(phoneVisibility = phoneVisibility) }
    }

    fun onCityChanged(selectedCity: DropdownData) {
        _viewState.update { state ->
            (state as Private).copy(selectedCity = selectedCity, cityError = null)
        }
    }

    fun onWebsiteChanged(website: String) {
        _viewState.update { state -> (state as Shop).copy(website = website) }
    }

    fun onShortDescriptionChanged(description: String) {
        _viewState.update { state ->
            (state as Shop).copy(shortDescription = description, shortDescriptionError = null)
        }
    }

    fun onLongDescriptionChanged(description: String) {
        _viewState.update { state -> (state as Shop).copy(longDescription = description) }
    }

    fun onAddressChanged(address: String) {
        _viewState.update { state -> (state as Shop).copy(address = address) }
    }

    fun onSubmit() {
        when (val state = _viewState.value) {
            is Private -> if (validatePrivateAccountForm(state).not()) return
            is Shop -> if (validateShopAccountForm(state).not()) return
            // Shouldn't happen, but as security!
            else -> return
        }
        viewModelScope.launch {
            val result = when (val state = _viewState.value) {
                is Private -> {
                    showHideLoading(true)
                    val (name, email, phone, phoneVisibility, city) = state
                    accountRepository.updatePrivateAccount(
                        name = name,
                        email = if (state.emailEditEnabled) email else null,
                        phone = phone,
                        phoneVisibility = phoneVisibility,
                        // Shouldn't be null because in validatePrivateAccountForm
                        // we don't accept empty city, But as security.
                        cityId = city?.id.orEmpty()
                    )
                }

                is Shop -> {
                    showHideLoading(true)
                    val (accountId, name, website, shortDescription, longDescription, address, phones) = state
                    accountRepository.updateShopAccount(
                        accountId = accountId,
                        name = name,
                        website = website,
                        shortDescription = shortDescription,
                        longDescription = longDescription,
                        address = address,
                        phones = phones
                    )
                }
                // Shouldn't happen, but as security!
                else -> return@launch
            }
            showHideLoading(false)
            updateFor(result)
        }
    }

    fun onLogout() {
        viewModelScope.launch {
            showHideProgress(true)
            val result = logoutUseCase()
            showHideProgress(false)
            when (result) {
                is Resource.Success -> {
                    renderSuccess(message = UiText.FromRes(R.string.common_logout_success_message))
                    _oneTimeEvents.emit(EditAccountOneTimeEvents.LogoutSuccess)
                }

                is Resource.Failure -> renderFailure(result.error)
            }
        }
    }

    private fun getCurrentAccount() {
        viewModelScope.launch {
            accountRepository.currentAccount
                .collectLatest { account ->
                    when (account) {
                        is Account.Connected.Private -> {
                            _viewState.value = Private(
                                name = account.contact.name,
                                email = account.contact.email,
                                emailEditEnabled = account.contact.email.isBlank(),
                                phone = account.contact.phone.orEmpty(),
                                phoneVisibility = account.isPhoneHidden,
                                cities = emptyList<DropdownData>().toImmutableList(),
                                selectedCity = account.contact.location?.let { city ->
                                    DropdownData(id = city.id, name = city.name)
                                },
                                loading = false
                            )
                            coroutineScope {
                                launch {
                                    val cities = configRepository.getCities()
                                        .map { city -> DropdownData(city.id, city.name) }
                                        .sortedBy { dropdownData -> dropdownData.name }
                                        .toImmutableList()
                                    _viewState.update { state ->
                                        (state as Private).copy(cities = cities)
                                    }
                                }
                            }
                        }

                        is Account.Connected.Shop -> {
                            _viewState.value = with(account) {
                                Shop(
                                    accountId = contact.accountId,
                                    name = contact.name,
                                    website = store.website.orEmpty(),
                                    email = contact.email,
                                    shortDescription = store.shortDescription.orEmpty(),
                                    longDescription = store.longDescription.orEmpty(),
                                    phones = store.phones.toImmutableList(),
                                    address = contact.location?.address.orEmpty(),
                                    loading = false
                                )
                            }
                        }

                        // This can happen if the session expires at the same time the user enters to this screen!
                        Account.NotConnected -> SnackBarController.showSnackBar(
                            event = SnackBarEvent(
                                message = UiText.FromRes(R.string.common_session_expired),
                                type = SnackBarType.ERROR,
                                action = SnackBarAction(
                                    name = UiText.FromRes(R.string.common_login),
                                    onClicked = {
                                        launch {
                                            _oneTimeEvents.emit(EditAccountOneTimeEvents.OpenLogin)
                                        }
                                    }
                                )
                            )
                        )
                    }
                }
        }
    }

    private fun validatePrivateAccountForm(state: Private): Boolean {
        if (credentialsValidator.validateFullName(state.name).not()) {
            _viewState.update {
                state.copy(nameError = UiText.FromRes(R.string.common_full_name_field_required))
            }
            return false
        }
        if (state.emailEditEnabled) {
            when {
                state.email.isBlank() -> R.string.common_email_field_required
                credentialsValidator.validateEmail(state.email)
                    .not() -> R.string.common_email_field_format_invalid

                else -> null
            }?.let { errorMsg ->
                _viewState.update { state.copy(emailError = UiText.FromRes(errorMsg)) }
                return false
            }
        }
        when {
            state.phone.isBlank() -> R.string.common_phone_number_field_required
            credentialsValidator.validatePhone(state.phone)
                .not() -> R.string.common_phone_number_field_format_invalid

            else -> null
        }?.let { errorMsg ->
            _viewState.update { state.copy(phoneError = UiText.FromRes(errorMsg)) }
            return false
        }
        if (state.selectedCity == null) {
            _viewState.update {
                state.copy(cityError = UiText.FromRes(R.string.common_city_field_required))
            }
            return false
        }
        return true
    }

    private fun validateShopAccountForm(state: Shop): Boolean {
        if (credentialsValidator.validateFullName(state.name).not()) {
            _viewState.update {
                state.copy(nameError = UiText.FromRes(R.string.common_full_name_field_required))
            }
            return false
        }
        if (state.shortDescription.isBlank()) {
            _viewState.update {
                state.copy(shortDescriptionError = UiText.FromRes(R.string.edit_shop_account_screen_name_required))
            }
            return false
        }
        return true
    }

    private fun updateFor(result: Resource<Unit, NetworkAndBackendErrors>) {
        when (result) {
            is Resource.Success -> renderSuccess(UiText.FromRes(R.string.edit_shop_account_screen_success_message))
            is Resource.Failure -> renderFailure(error = result.error)
        }
    }

    private fun showHideLoading(show: Boolean) {
        _viewState.update { state ->
            when (state) {
                Nothing -> state
                is Private -> state.copy(loading = show)
                is Shop -> state.copy(loading = show)
            }
        }
    }

    private suspend fun showHideProgress(isLoading: Boolean) {
        _oneTimeEvents.emit(EditAccountOneTimeEvents.ShowHideProgress(isLoading = isLoading))
    }
}
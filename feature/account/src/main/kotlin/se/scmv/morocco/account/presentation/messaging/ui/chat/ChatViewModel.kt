package se.scmv.morocco.account.presentation.messaging.ui.chat

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import androidx.paging.insertHeaderItem
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.launch
import se.scmv.morocco.account.R
import se.scmv.morocco.domain.Attachment
import se.scmv.morocco.domain.ChatRepository
import se.scmv.morocco.domain.Conversation
import se.scmv.morocco.domain.Message
import se.scmv.morocco.domain.RealtimeChatEvent
import se.scmv.morocco.ui.renderSuccess
import se.scmv.morocco.ui.renderFailure
import se.scmv.morocco.designsystem.utils.UiText
import java.io.File
import java.util.Date
import java.util.UUID
import javax.inject.Inject


data class ChatState(
    val conversation: Conversation? = null,
    val isLoading: Boolean = false,
    val error: String? = null,
    val isSending: Boolean = false,
    val isBlocking: Boolean = false
)

@HiltViewModel
class ChatViewModel @Inject constructor(
    private val chatRepository: ChatRepository, ) : ViewModel() {

    private var conversationId: String? = null

    private val _state = MutableStateFlow(ChatState())
    val state: StateFlow<ChatState> = _state.asStateFlow()


    // Paging3 Flow for backend messages
    private val _refreshTrigger = MutableSharedFlow<Unit>()
    private var _messagesPaging: Flow<PagingData<Message>>? = null

    // Local messages (sent and recived)
    private val localMessages = MutableStateFlow<List<Message>>(emptyList())

    // Final merged flow for UI (backend + local)
    private val _mergedMessages = MutableStateFlow<PagingData<Message>>(PagingData.empty())
    val mergedMessages: StateFlow<PagingData<Message>> = _mergedMessages.asStateFlow()

    private var isSubscribed = false

    fun setConversationId(id: String) {
        conversationId = id
        if (_state.value.conversation == null) {
            loadConversationById(id)
        }
    }

    private fun loadConversationById(id: String) {
        viewModelScope.launch {
            _state.value = _state.value.copy(isLoading = true, error = null)

            chatRepository.getChatConversation(id, 20).collect { result ->
                result.fold(
                    onSuccess = { conversation ->
                        _state.value = _state.value.copy(
                            conversation = conversation,
                            isLoading = false
                        )

                        // Start Paging for backend messages
                        _messagesPaging = _refreshTrigger
                            .onStart { emit(Unit) }
                            .flatMapLatest {
                                chatRepository.getMessagesPaging(conversation.id)
                            }
                            .cachedIn(viewModelScope)

                        // Combine backend paging with local messages
                        combineBackendAndLocal()

                        // Start chat subscription for real-time updates
                        subscribeToChat()

                        markAsRead()
                    },
                    onFailure = { error ->
                        _state.value = _state.value.copy(
                            error = error.message ?: "Failed to load conversation",
                            isLoading = false
                        )
                    }
                )
            }
        }
    }

    /**
     * Combine PagingData (backend messages) with local messages
     */
    private fun combineBackendAndLocal() {
        viewModelScope.launch {
            if (_messagesPaging == null) return@launch

            combine(
                _messagesPaging!!,
                localMessages
            ) { backendPaging, localList ->
                var merged = backendPaging
                localList.forEach { localMsg ->
                    merged = merged.insertHeaderItem(item = localMsg)
                }
                merged
            }.collect { mergedPaging ->
                _mergedMessages.value = mergedPaging
            }
        }
    }




    private fun subscribeToChat() {
        if (isSubscribed) {
            return
        }

        viewModelScope.launch {
            try {
                chatRepository.subscribeToChat()
                    .catch { e ->
                        renderFailure(UiText.Text(e.message ?: "Error subscribing to chat"))
                        isSubscribed = false
                        // Retry subscription after a delay
                        delay(5000) // 5 seconds delay
                        subscribeToChat()
                    }
                    .collect { event ->
                        handleChatEvent(event)
                    }
                isSubscribed = true
            } catch (e: Exception) {
                renderFailure(UiText.Text(e.message ?: "Error subscribing to chat"))
                isSubscribed = false
                // Retry subscription after a delay
                delay(5000) // 5 seconds delay
                subscribeToChat()
            }
        }
    }

    private fun handleChatEvent(event: RealtimeChatEvent) {
        val message = event.message
        if (message.conversationId == conversationId) {
            // Remove any local message that matches this one (it's now confirmed by backend)
            localMessages.value = localMessages.value.filter { localMessage ->
                localMessage.id != message.id && localMessage.time != message.time
            }
            
            // Add the new message to local messages for immediate display
            localMessages.value = localMessages.value + message
            
            // Trigger a gentle refresh that doesn't clear existing messages
            refreshMessagesGently()
            
            if (!message.isMine) {
                markAsRead()
            }
        }
    }
    
    private fun refreshMessagesGently() {
        // Trigger refresh
        _refreshTrigger.tryEmit(Unit)
    }
    


    companion object {
        private const val MAX_FILE_SIZE_MB = 4
        private const val MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024
    }

    fun sendMessage(text: String, attachment: Attachment? = null) {
        val currentId = conversationId ?: return

        viewModelScope.launch {
            try {
                // STEP 1: Validate file if present
                if (attachment != null) {
                    val file = File(attachment.filePath)
                    if (!file.exists()) {
                        renderFailure(UiText.FromRes(R.string.chat_screen_file_not_found))
                        return@launch
                    }

                    if (file.length() > MAX_FILE_SIZE_BYTES) {
                        renderFailure(
                            UiText.FromRes(
                                R.string.chat_screen_file_too_large,
                                listOf(MAX_FILE_SIZE_MB)
                            )
                        )
                        return@launch
                    }
                }

                // STEP 2: Create a temporary message for instant UI feedback
                val tempId = UUID.randomUUID().toString()
                val tempMessage = Message(
                    id = tempId,
                    conversationId = currentId,
                    text = text,
                    attachment = attachment,
                    isUnread = false,
                    isMine = true,
                    time = Date(),
                )

                // STEP 3: Add temp message to local state
                localMessages.value = localMessages.value + tempMessage

                // STEP 4: Start network request to send the message
                chatRepository.sendMessage(
                    conversationId = currentId,
                    text = text,
                    attachment = attachment
                ).collect { result ->
                    result.fold(
                        onSuccess = { backendMessage ->
                            // STEP 5: Replace local temp message with real backend message
                            replaceLocalWithBackend(tempId, backendMessage)
                        },
                        onFailure = { error ->
                            // STEP 6: Mark message as failed
                            markMessageAsFailed(tempId, error)
                        }
                    )
                }

            } catch (e: Exception) {
                handleError(e)
            }
        }
    }


    private fun replaceLocalWithBackend(tempId: String, backendMessage: Message) {
        localMessages.value = localMessages.value.map { message ->
            if (message.id == tempId) backendMessage else message
        }
    }

    private fun markMessageAsFailed(tempId: String, error: Throwable) {
        localMessages.value = localMessages.value.map { message ->
            if (message.id == tempId) {
                message            } else {
                message
            }
        }
    }


    private fun markAsRead() {
        val currentId = conversationId ?: return

        viewModelScope.launch {
            try {
                chatRepository.markConversationAsRead(currentId)
                    .collect { result ->
                        result.fold(
                            onSuccess = { marked ->
                                if (marked) {
                                    // Update local state
                                    _state.value = _state.value.copy(
                                        conversation = _state.value.conversation?.copy(unreadCount = 0)
                                    )
                                }
                            },
                            onFailure = { error ->
                                handleError(error)
                            }
                        )
                    }
            } catch (e: Exception) {
                handleError(e)
            }
        }
    }

    fun blockUser() {
        conversationId?.let { id ->
            viewModelScope.launch {
                _state.value = _state.value.copy(isBlocking = true)
                chatRepository.blockConversation(id).collect { result ->
                    result.fold(
                        onSuccess = { blocked ->
                            if (blocked) {
                                _state.value = _state.value.copy(
                                    conversation = _state.value.conversation?.copy(isBlockedByMe = true)
                                )
                                renderSuccess(UiText.FromRes(R.string.has_been_blocked))
                            }
                        },
                        onFailure = { error ->
                            renderFailure(UiText.Text(error.message ?: "Failed to block user"))
                        }
                    )
                }
                _state.value = _state.value.copy(isBlocking = false)
            }
        }
    }

    fun unblockUser() {
        conversationId?.let { id ->
            viewModelScope.launch {
                _state.value = _state.value.copy(isBlocking = true)
                chatRepository.unblockConversation(id).collect { result ->
                    result.fold(
                        onSuccess = { unblocked ->
                            if (unblocked) {
                                _state.value = _state.value.copy(
                                    conversation = _state.value.conversation?.copy(isBlockedByMe = false)
                                )
                                renderSuccess(UiText.FromRes(R.string.has_been_unblocked))
                            }
                        },
                        onFailure = { error ->
                            renderFailure(UiText.Text(error.message ?: "Failed to unblock user"))
                        }
                    )
                }
                _state.value = _state.value.copy(isBlocking = false)
            }
        }
    }

    fun clearChat() {
        conversationId?.let { id ->
            viewModelScope.launch {
                chatRepository.clearConversation(id).collect { result ->
                    result.fold(
                        onSuccess = { cleared ->
                            if (cleared) {
                                // Clear local messages
                                localMessages.value = emptyList()
                                
                                // Trigger refresh to reload messages from backend
                                _refreshTrigger.tryEmit(Unit)
                                
                                renderSuccess(UiText.FromRes(R.string.has_been_deleted))
                            }
                        },
                        onFailure = { error ->
                            renderFailure(UiText.Text(error.message ?: "Failed to clear chat"))
                        }
                    )
                }
            }
        }
    }

    fun onMessageCopied() {
        renderSuccess(UiText.FromRes(R.string.chat_screen_message_copied))
    }

    fun retry() {
        _state.value = _state.value.copy(error = null, isLoading = true)
        
        // Reload the conversation and messages
        conversationId?.let { id ->
            loadConversationById(id)
        }
    }

    fun refresh() {
        conversationId?.let { id ->
            // Reset state and reload everything
            _state.value = _state.value.copy(error = null, isLoading = true)
            loadConversationById(id)
        }
    }

    private fun handleError(error: Throwable) {
        _state.value = _state.value.copy(
            error = error.message ?: "Failed to send message",
            isSending = false
        )

        viewModelScope.launch {
            renderFailure(UiText.Text(error.message ?: "Failed to send message"))
        }
    }

} 
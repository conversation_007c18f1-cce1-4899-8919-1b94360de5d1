package se.scmv.morocco.account.presentation.myads

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Switch
import androidx.compose.material3.SwitchDefaults
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import se.scmv.morocco.account.R
import se.scmv.morocco.designsystem.theme.dimens

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun UrgentPromoBottomSheet(
    isUrgent: Boolean,
    onUrgentChanged: (Boolean) -> Unit,
    selectedPromo: Int,
    onPromoChanged: (Int) -> Unit,
    onApplyChanges: () -> Unit,
    onDismiss: () -> Unit
) {
    ModalBottomSheet(
        onDismissRequest = onDismiss,
        sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true),
    ) {
        Column(
            modifier = Modifier
                .padding(MaterialTheme.dimens.default)
                .fillMaxWidth(),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.default)
        ) {
            // Urgent Section
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                LabelChip(
                    text = stringResource(R.string.urgent),
                    backgroundColor = Color(0xFF3A6FF6),
                    icon = painterResource(id = R.drawable.ic_urgent)
                )

                Spacer(modifier = Modifier.width(MaterialTheme.dimens.medium))

                Text(
                    text = stringResource(R.string.add_urgent),
                    style = MaterialTheme.typography.titleSmall,
                    modifier = Modifier.weight(1f)
                )
                Switch(
                    checked = isUrgent,
                    onCheckedChange = { newIsUrgent ->
                        if (newIsUrgent) onPromoChanged(0) // Reset promotion if urgent is selected
                        onUrgentChanged(newIsUrgent)
                    },
                    colors = SwitchDefaults.colors(
                        checkedThumbColor = Color.White,
                        checkedTrackColor = Color(0xFF3A6FF6),
                        uncheckedThumbColor = Color.Gray,
                        uncheckedTrackColor = Color.LightGray
                    )
                )
            }

            // Promo Section
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                LabelChip(
                    text = stringResource(R.string.hot_deal),
                    backgroundColor = Color(0xFFec5a5f),
                    icon = painterResource(id = R.drawable.ic_promotion)
                )

                Spacer(modifier = Modifier.width(MaterialTheme.dimens.medium))

                Text(
                    text = stringResource(R.string.add_hot_deal),
                    style = MaterialTheme.typography.titleSmall,
                    modifier = Modifier.weight(1f)
                )
                PromoDropdown(
                    selectedPromo = selectedPromo,
                    onPromoChanged = { newPromo ->
                        if (newPromo != 0) onUrgentChanged(false) // Disable urgent if promo is selected
                        onPromoChanged(newPromo)
                    }
                )
            }

            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = MaterialTheme.dimens.none),
                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium)
            ) {
                // Cancel Button
                Button(
                    onClick = onDismiss,
                    modifier = Modifier
                        .weight(1f)
                        .fillMaxWidth(),
                    colors = ButtonDefaults.outlinedButtonColors(containerColor = Color.Transparent),
                    border = BorderStroke(1.dp, Color(0xFFFC942D)),
                    shape = RoundedCornerShape(MaterialTheme.dimens.regular),
                    contentPadding = PaddingValues(
                        horizontal = MaterialTheme.dimens.default,
                        vertical = MaterialTheme.dimens.regular
                    )
                ) {
                    Icon(
                        imageVector = Icons.Filled.Close,
                        contentDescription = "Cancel",
                        tint = Color(0xFFFC942D),
                        modifier = Modifier.size(MaterialTheme.dimens.default)
                    )
                    Spacer(modifier = Modifier.width(MaterialTheme.dimens.medium))
                    Text(
                        text = stringResource(R.string.cancel),
                        color = Color(0xFFFC942D),
                        style = MaterialTheme.typography.bodyMedium,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }

                // Apply Button
                Button(
                    onClick = onApplyChanges,
                    modifier = Modifier
                        .weight(1f)
                        .fillMaxWidth(),
                    colors = ButtonDefaults.buttonColors(containerColor = Color(0xFFFC942D)),
                    shape = RoundedCornerShape(MaterialTheme.dimens.regular),
                    contentPadding = PaddingValues(
                        horizontal = MaterialTheme.dimens.default,
                        vertical = MaterialTheme.dimens.regular
                    )
                ) {
                    Icon(
                        imageVector = Icons.Filled.Check,
                        contentDescription = "Apply Changes",
                        modifier = Modifier.size(MaterialTheme.dimens.default)
                    )
                    Spacer(modifier = Modifier.width(MaterialTheme.dimens.medium))
                    Text(
                        text = stringResource(R.string.common_apply),
                        color = Color.White,
                        style = MaterialTheme.typography.bodyMedium,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }
        }
    }
}

@Composable
fun PromoDropdown(selectedPromo: Int, onPromoChanged: (Int) -> Unit) {
    var expanded by remember { mutableStateOf(false) }
    val promoOptions = listOf(0, 5, 10, 15, 20, 30, 40, 50, 60, 70, 80, 90) // Percentages for promo

    Box {
        OutlinedButton(onClick = { expanded = true }) {
            Text(text = "-$selectedPromo%")
        }
        DropdownMenu(
            expanded = expanded,
            onDismissRequest = { expanded = false }
        ) {
            promoOptions.forEach { promo ->
                DropdownMenuItem(
                    onClick = {
                        onPromoChanged(promo)
                        expanded = false
                    },
                    text = { Text("-$promo%") },
                )
            }
        }
    }
}
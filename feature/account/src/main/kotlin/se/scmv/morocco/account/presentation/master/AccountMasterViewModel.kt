package se.scmv.morocco.account.presentation.master

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import se.scmv.morocco.account.R
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.usecases.LogoutUseCase
import se.scmv.morocco.ui.renderFailure
import se.scmv.morocco.ui.renderSuccess
import javax.inject.Inject

@HiltViewModel
class AccountMasterViewModel @Inject constructor(
    private val logoutUseCase: LogoutUseCase,
    private val intentsTrigger: IntentsTrigger,
) : ViewModel() {

    private val _oneTimeEvents = MutableSharedFlow<AccountMasterOneTimeEvents>()
    val oneTimeEvents = _oneTimeEvents.asSharedFlow()

    fun onLogout() {
        viewModelScope.launch {
            showHideLoading(true)
            val result = logoutUseCase()
            showHideLoading(false)
            when (result) {
                is Resource.Success -> renderSuccess(message = UiText.FromRes(R.string.common_logout_success_message))
                is Resource.Failure -> renderFailure(result.error)
            }
        }
    }

    private suspend fun showHideLoading(isLoading: Boolean) {
        _oneTimeEvents.emit(AccountMasterOneTimeEvents(showHideProgress = isLoading))
    }

    fun contactSupportThroughEmail(data: ContactSupportData, onError: () -> Unit) {
        intentsTrigger.contactSupportThroughEmail(data, onError)
    }

    fun contactSupportThroughWhatsapp(phone: String, onError: () -> Unit) {
        intentsTrigger.contactSupportThroughWhatsapp(phone, onError)
    }
}

package se.scmv.morocco.account.presentation

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.layout.consumeWindowInsets
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.fragment.app.Fragment
import androidx.navigation.compose.rememberNavController
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.collectLatest
import se.scmv.morocco.account.presentation.navigation.AccountNavHost
import se.scmv.morocco.analytics.AnalyticsHelper
import se.scmv.morocco.analytics.LocalAnalyticsHelper
import se.scmv.morocco.authentication.presentation.AuthenticationActivity
import se.scmv.morocco.designsystem.components.NotConnectedScreen
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.domain.models.Account
import se.scmv.morocco.domain.repositories.AccountRepository
import se.scmv.morocco.ui.SnackBarHostForSnackBarController
import javax.inject.Inject

// TODO Remove this fragment and add AccountNavHost to AppNavHost, once all the app is in compose
@AndroidEntryPoint
class AccountFragment : Fragment() {

    @Inject
    lateinit var accountRepository: AccountRepository

    @Inject
    lateinit var analyticsHelper: AnalyticsHelper

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed)
            setContent {
                AvitoTheme {
                    CompositionLocalProvider(LocalAnalyticsHelper provides analyticsHelper) {
                        Scaffold(
                            snackbarHost = { SnackBarHostForSnackBarController() }
                        ) { paddingValues ->
                            val accountState = remember { mutableStateOf<Account?>(null) }
                            when (val account = accountState.value) {
                                is Account.Connected -> {
                                    val navController = rememberNavController()
                                    AccountNavHost(
                                        modifier = Modifier.consumeWindowInsets(paddingValues),
                                        account = account,
                                        navHostController = navController
                                    )
                                }

                                Account.NotConnected -> {
                                    NotConnectedScreen(
                                        modifier = Modifier.consumeWindowInsets(paddingValues),
                                        onLoginClicked = ::navigateToAuthentication
                                    )
                                }

                                null -> {}
                            }
                            LaunchedEffect(Unit) {
                                accountRepository.currentAccount.collectLatest {
                                    accountState.value = it
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private fun navigateToAuthentication() {
        val intent = Intent(requireContext(), AuthenticationActivity::class.java)
        requireActivity().startActivityForResult(
            intent,
            AuthenticationActivity.REQUEST_SIGN_IN_MY_ACCOUNT
        )
    }
}
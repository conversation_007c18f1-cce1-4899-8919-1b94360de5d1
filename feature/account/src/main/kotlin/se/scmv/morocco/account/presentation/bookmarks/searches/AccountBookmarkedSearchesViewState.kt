package se.scmv.morocco.account.presentation.bookmarks.searches

import androidx.compose.runtime.Stable
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.domain.models.BookmarkedSearch

@Stable
data class AccountBookmarkedSearchesViewState(
    val searches: ImmutableList<BookmarkedSearch> = persistentListOf(),
    val loading: Boolean = true,
    val errorMessage: UiText? = null
)

@JvmInline
value class AccountBookmarkedSearchesDeleteSearchStatus(val isLoading: Boolean)
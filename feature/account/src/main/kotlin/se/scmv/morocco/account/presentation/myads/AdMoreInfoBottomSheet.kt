package se.scmv.morocco.account.presentation.myads

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import se.scmv.morocco.account.R
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.designsystem.utils.relativeValue
import se.scmv.morocco.designsystem.utils.toRelativeTimeRes
import se.scmv.morocco.domain.models.AccountAd
import se.scmv.morocco.domain.models.MyAccountAdStatus
import se.scmv.morocco.domain.models.VasPacksApplication

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AdMoreInfoBottomSheet(
    ad: AccountAd,
    onDeactivateClick: (String) -> Unit,
    onActivateClick: (String) -> Unit,
    onDeleteClick: (String) -> Unit,
    onEditClick: (adId: String, adCategoryKey: String, adType: String, toImageStep: Boolean) -> Unit,
    onAdDetailClick: (adListId: String) -> Unit,
    onBoostClick: (adId: String, adCategoryKey: String, adType: String, application: VasPacksApplication) -> Unit,
    onPatchClick: (adId: String, urgent: Boolean, hotDeal: Boolean, discount: Int?) -> Unit,
    onDismiss: () -> Unit
) {
    ModalBottomSheet(
        onDismissRequest = onDismiss,
        sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true),
    ) {
        Column(
            modifier = Modifier
                .padding(horizontal = MaterialTheme.dimens.bottomSheetPaddingHorizontal)
                .padding(bottom = MaterialTheme.dimens.big)
        ) {
            // Image Section
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(MaterialTheme.dimens.screenImageHeight)
                    .clip(RoundedCornerShape(MaterialTheme.dimens.medium))
                    .background(if (ad.imageUrl.isNullOrEmpty()) Color(0xFFf6f6f6) else Color.Transparent)
                    .clickable {
                        // Handle add image action
                    }
            ) {
                if (ad.imageUrl.isNullOrEmpty()) {
                    if (ad.myAdStatus == MyAccountAdStatus.ACTIVE || ad.myAdStatus == MyAccountAdStatus.REFUSED) {
                        Column(
                            modifier = Modifier.align(Alignment.Center),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Icon(
                                painter = painterResource(R.drawable.ic_upload),
                                contentDescription = "Add Image",
                                modifier = Modifier
                                    .size(MaterialTheme.dimens.mediumBig)
                                    .clickable {
                                        onEditClick(
                                            ad.adId,
                                            ad.category.id.toString(),
                                            ad.myAdType.key?.name.toString(),
                                            true
                                        )
                                    },
                                tint = Color.Gray
                            )
                            Text(
                                text = stringResource(R.string.add_image),
                                color = Color.Gray,
                                fontSize = 12.sp
                            )
                        }
                    } else {
                        Column(
                            modifier = Modifier.align(Alignment.Center),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Icon(
                                painter = painterResource(R.drawable.ic_upload),
                                contentDescription = "Add Image",
                                modifier = Modifier
                                    .size(MaterialTheme.dimens.mediumBig),
                                tint = Color.Gray
                            )
                        }
                    }

                } else {
                    AsyncImage(
                        model = ad.imageUrl,
                        contentDescription = stringResource(R.string.add_image),
                        modifier = Modifier
                            .fillMaxSize()
                            .clip(RoundedCornerShape(MaterialTheme.dimens.medium)),
                        contentScale = ContentScale.Crop
                    )
                }

            }

            Spacer(modifier = Modifier.height(MaterialTheme.dimens.medium))

            // Title and Category Section
            Column(
                modifier = Modifier
                    .fillMaxWidth()
            ) {
                // Price
                Text(
                    text = ad.priceWithCurrency ?: stringResource(R.string.price_not_specified),
                    style = MaterialTheme.typography.titleLarge.copy(
                        fontSize = 20.sp, // Larger text size for prominence
                        fontWeight = FontWeight.Bold
                    ),
                    modifier = Modifier.fillMaxWidth(),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                Spacer(modifier = Modifier.height(MaterialTheme.dimens.medium))

                // Title
                Text(
                    text = ad.title,
                    style = MaterialTheme.typography.titleMedium.copy(
                        fontSize = 18.sp, // Big and bold for the title
                        fontWeight = FontWeight.SemiBold
                    ),
                    modifier = Modifier.fillMaxWidth(),
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )
                Spacer(modifier = Modifier.height(MaterialTheme.dimens.medium))

                // Category
                Text(
                    text = ad.category.name.toString(),
                    style = MaterialTheme.typography.bodySmall.copy(
                        color = MaterialTheme.colorScheme.onSurfaceVariant // Subtle color for category
                    ),
                    modifier = Modifier.fillMaxWidth()
                )
                Spacer(modifier = Modifier.height(MaterialTheme.dimens.regular))

                // Time and Location Row
                Row(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    // Time
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Icon(
                            painter = painterResource(id = R.drawable.ic_time),
                            contentDescription = "Time Icon",
                            modifier = Modifier.size(MaterialTheme.dimens.default),
                            tint = MaterialTheme.colorScheme.onSurface
                        )
                        Spacer(modifier = Modifier.width(MaterialTheme.dimens.small))
                        Text(
                            text = (ad.publishedAt?.relativeValue() ?: ad.lastStateTime?.relativeValue())?.let {
                                (ad.publishedAt?.toRelativeTimeRes()
                                    ?: ad.lastStateTime?.toRelativeTimeRes())?.let { it1 ->
                                    stringResource(
                                        id = it1,
                                        it
                                    )
                                }
                            }.orEmpty(),
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurface
                        )
                    }

                    // Location
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Icon(
                            painter = painterResource(id = R.drawable.outline_location_on_24),
                            contentDescription = "Location Icon",
                            modifier = Modifier.size(MaterialTheme.dimens.default),
                            tint = MaterialTheme.colorScheme.onSurface
                        )
                        Spacer(modifier = Modifier.width(MaterialTheme.dimens.small))
                        Text(
                            text = ad.location.name,
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurface
                        )
                    }
                }
                Spacer(modifier = Modifier.height(MaterialTheme.dimens.regular))

                // Time and Location Row
                Row(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    // Ad Labels
                    AdLabels(ad)
                    Spacer(modifier = Modifier.width(MaterialTheme.dimens.medium)) // Add space between AdLabels and AdStatus
                    AdStatus(ad)
                }
                ad.refusalReason?.let {
                    Spacer(modifier = Modifier.height(MaterialTheme.dimens.medium))
                    AdRefusalReason(it)
                }
            }

            Spacer(modifier = Modifier.height(MaterialTheme.dimens.betweenSmallMedium))

            // Ad Status and Performance
            ad.vasPackages?.packages?.forEach { myAdVasPack ->
                Spacer(modifier = Modifier.height(MaterialTheme.dimens.medium))
                VASInfo(myAdVasPack, null)
            }

            if (ad.myAdStatus != MyAccountAdStatus.PENDING_REVIEW && ad.myAdStatus != MyAccountAdStatus.DEACTIVATED)
                ad.performance?.let {
                    Spacer(modifier = Modifier.height(MaterialTheme.dimens.betweenRegularDefault))
                    AdPerformance(ad)
                }

            Spacer(modifier = Modifier.height(MaterialTheme.dimens.default))

            // Action Buttons
            ActionButtons(
                onEditClick = {
                    onEditClick(
                        ad.adId,
                        ad.category.id.toString(),
                        ad.myAdType.key?.name.toString(),
                        false
                    )
                },
                onBoostClick = { application ->
                    onBoostClick(
                        ad.adId,
                        ad.category.id.toString(),
                        ad.myAdType.key?.name.toString(),
                        application
                    )
                },
                onActivateClick = {
                    onActivateClick(ad.adId)
                },
                ad
            )
        }
    }
}
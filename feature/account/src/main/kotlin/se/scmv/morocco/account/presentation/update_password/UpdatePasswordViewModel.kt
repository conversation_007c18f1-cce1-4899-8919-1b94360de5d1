package se.scmv.morocco.account.presentation.update_password

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.toRoute
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import se.scmv.morocco.account.R
import se.scmv.morocco.account.presentation.navigation.UpdatePasswordRoute
import se.scmv.morocco.account.presentation.update_password.UpdatePasswordOneTimeEvents.DismissDialog
import se.scmv.morocco.account.presentation.update_password.UpdatePasswordOneTimeEvents.NavigateToLogin
import se.scmv.morocco.account.presentation.update_password.UpdatePasswordOneTimeEvents.ShowSnackBar
import se.scmv.morocco.authentication.domain.validators.CredentialsValidator
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.models.UpdatePasswordErrors
import se.scmv.morocco.domain.usecases.UpdatePasswordAndSignInUseCase
import se.scmv.morocco.ui.SnackBarController
import se.scmv.morocco.ui.SnackBarEvent
import se.scmv.morocco.ui.SnackBarType
import se.scmv.morocco.ui.asUiText
import javax.inject.Inject

@HiltViewModel
class UpdatePasswordViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle,
    private val credentialsValidator: CredentialsValidator,
    private val updatePasswordAndSignInUseCase: UpdatePasswordAndSignInUseCase
) : ViewModel() {

    private val route = savedStateHandle.toRoute<UpdatePasswordRoute>()

    private val _viewState = MutableStateFlow(UpdatePasswordViewState())
    val viewState = _viewState.asStateFlow()

    private val _oneTimeEvents = MutableSharedFlow<UpdatePasswordOneTimeEvents>()
    val oneTimeEvents = _oneTimeEvents.asSharedFlow()

    fun onOldPasswordChanged(oldPassword: String) {
        _viewState.update { it.copy(oldPassword = oldPassword, oldPasswordError = null) }
    }

    fun onNewPasswordChanged(newPassword: String) {
        _viewState.update { it.copy(newPassword = newPassword, newPasswordError = null) }
    }

    fun onSubmit() {
        if (validateForm().not()) return
        clearErrors()
        val (oldPassword, _, newPassword) = _viewState.value
        viewModelScope.launch {
            showHideLoading(true)
            val result = updatePasswordAndSignInUseCase(
                email = route.email,
                currentPassword = oldPassword,
                newPassword = newPassword
            )
            showHideLoading(false)
            updateFor(result)
        }
    }

    private suspend fun updateFor(result: Resource<Unit, UpdatePasswordErrors>) {
        when (result) {
            is Resource.Success -> {
                SnackBarController.showSnackBar(
                    event = SnackBarEvent(
                        message = UiText.FromRes(R.string.reset_password_success_screen_title),
                        type = SnackBarType.SUCCESS
                    )
                )
                _oneTimeEvents.emit(DismissDialog)
            }
            is Resource.Failure -> when (val error = result.error) {
                is UpdatePasswordErrors.NetworkOrBackend -> {
                    val errorMsg = error.error.asUiText()
                    _oneTimeEvents.emit(ShowSnackBar(isSuccess = false, message = errorMsg))
                }
                is UpdatePasswordErrors.NeedToReLogin -> {
                    _oneTimeEvents.emit(
                        ShowSnackBar(
                            isSuccess = false,
                            message = UiText.FromRes(id = R.string.update_password_screen_password_updated_re_login_required)
                        )
                    )
                    _oneTimeEvents.emit(NavigateToLogin)
                }
            }
        }
    }

    private fun validateForm(): Boolean {
        val (oldPassword, _, newPassword) = _viewState.value
        if (credentialsValidator.validatePassword(oldPassword).not()) {
            _viewState.update {
                it.copy(oldPasswordError = UiText.FromRes(R.string.common_password_field_required))
            }
            return false
        }
        when {
            credentialsValidator.validatePassword(newPassword)
                .not() -> R.string.common_password_field_required

            oldPassword == newPassword -> R.string.common_password_confirm_field_shold_not_match
            else -> null
        }?.let { errorMsg ->
            _viewState.update { it.copy(newPasswordError = UiText.FromRes(errorMsg)) }
            return false
        }
        return true
    }

    private fun clearErrors() {
        _viewState.update { it.copy(oldPasswordError = null, newPasswordError = null) }
    }

    private fun showHideLoading(show: Boolean) {
        _viewState.update { it.copy(loading = show) }
    }
}
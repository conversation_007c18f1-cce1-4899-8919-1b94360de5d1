package se.scmv.morocco.account.presentation.messaging.ui.chat

import android.content.Context
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.ViewModelComponent
import dagger.hilt.android.qualifiers.ApplicationContext

@Module
@InstallIn(ViewModelComponent::class)
object ChatModule {
    @Provides
    fun provideContext(@ApplicationContext context: Context): Context = context
} 
package se.scmv.morocco.account.presentation.messaging.ui.chat

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.provider.OpenableColumns
import androidx.activity.compose.BackHandler
import androidx.activity.compose.ManagedActivityResultLauncher
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.DpOffset
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.paging.LoadState
import androidx.paging.compose.LazyPagingItems
import androidx.paging.compose.collectAsLazyPagingItems
import coil.compose.AsyncImage
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import se.scmv.morocco.account.R
import se.scmv.morocco.common.lang.LocaleManager
import se.scmv.morocco.common.utils.IOUtils
import se.scmv.morocco.designsystem.components.AvConfirmationAlertDialog
import se.scmv.morocco.designsystem.components.AvDynamicText
import se.scmv.morocco.designsystem.components.AvScreenSubTitle
import se.scmv.morocco.designsystem.components.AvTextWithEndIcon
import se.scmv.morocco.designsystem.components.ChatInput
import se.scmv.morocco.designsystem.components.IndeterminateLoading
import se.scmv.morocco.designsystem.components.ScreenEmptyState
import se.scmv.morocco.designsystem.components.ScreenErrorState
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.domain.Attachment
import se.scmv.morocco.domain.Conversation
import se.scmv.morocco.domain.Message
import se.scmv.morocco.ui.SnackBarController
import se.scmv.morocco.ui.SnackBarEvent
import se.scmv.morocco.ui.SnackBarType
import se.scmv.morocco.ui.getErrorAsUiText
import se.scmv.morocco.ui.isEmpty
import se.scmv.morocco.ui.isLoading
import java.io.File
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale


@Composable
fun ChatScreen(
    conversationId: String,
    onNavigateUp: () -> Unit,
    onOpenAd: (adId: String) -> Unit,
    modifier: Modifier = Modifier,
    viewModel: ChatViewModel = hiltViewModel()
) {
    // Handle back button press to navigate back to messages list
    BackHandler {
        onNavigateUp()
    }

    // Set conversation ID when screen is loaded
    LaunchedEffect(conversationId) {
        viewModel.setConversationId(conversationId)
    }

    val state by viewModel.state.collectAsState()
    val messagesPaging = if (state.conversation != null) {
        viewModel.mergedMessages.collectAsLazyPagingItems()
    } else {
        null
    }

    // Handle refresh when conversation changes
    LaunchedEffect(state.conversation) {
        if (state.conversation != null) {
            // Conversation loaded successfully, clear any previous errors
            if (state.error != null) {
                viewModel.retry()
            }
        }
    }


    var messageText by remember { mutableStateOf("") }
    val scope = rememberCoroutineScope()
    var showImagePreview by remember { mutableStateOf<String?>(null) }
    var showImageOptions by remember { mutableStateOf(false) }
    val context = LocalContext.current
    var currentPhotoFile by remember { mutableStateOf<File?>(null) }
    var showBlockConfirmation by remember { mutableStateOf(false) }
    var showClearConfirmation by remember { mutableStateOf(false) }
    var pendingAttachments by remember { mutableStateOf<List<Attachment>>(emptyList()) }

    val cameraLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.TakePicture(),
        onResult = { it: Boolean ->
            currentPhotoFile?.let { file ->
                if (file.exists() && file.canRead()) {
                    val pictureUri: Uri = FileProvider.getUriForFile(
                        context,
                        context.packageName + ".imagesprovider",
                        file
                    )
                    pendingAttachments = pendingAttachments + Attachment(
                        filePath = file.absolutePath,
                        previewUri = pictureUri.toString(),
                        type = "image/jpeg"
                    )
                }
            }
        }
    )

    val galleryLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetMultipleContents()
    ) { uris ->
        // Check if user selected any images (not cancelled)
        if (uris.isNotEmpty()) {
            uris.first().let { uri ->
                val inputStream = context.contentResolver.openInputStream(uri)
                val file = File(context.cacheDir, "image_${System.currentTimeMillis()}.jpg")
                inputStream?.use { input ->
                    file.outputStream().use { output ->
                        input.copyTo(output)
                    }
                }
                if (file.exists() && file.canRead()) {
                    pendingAttachments = pendingAttachments + Attachment(
                        filePath = file.absolutePath,
                        previewUri = Uri.fromFile(file).toString(),
                        type = "image/jpeg"
                    )
                }
            }
        }
    }

    val documentLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri ->
        uri?.let {
            val inputStream = context.contentResolver.openInputStream(uri)
            val fileName = getFileName(context, uri)
            val file = File(context.cacheDir, fileName)
            inputStream?.use { input ->
                file.outputStream().use { output ->
                    input.copyTo(output)
                }
            }
            if (file.exists() && file.canRead()) {
                val mimeType = context.contentResolver.getType(uri) ?: "application/octet-stream"
                pendingAttachments = pendingAttachments + Attachment(
                    filePath = file.absolutePath,
                    previewUri = Uri.fromFile(file).toString(),
                    type = mimeType
                )
            }
        }
    }


    Scaffold(
        topBar = {
            ChatTopBar(
                conversation = state.conversation,
                onNavigateUp = onNavigateUp,
                onBlockUser = {
                    showBlockConfirmation = true
                },
                onClearChat = {
                    showClearConfirmation = true
                },
                onOpenAd = { adId ->
                    onOpenAd(adId)
                }
            )
        },
        containerColor = MaterialTheme.colorScheme.background,
        modifier = modifier
    ) { padding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(padding)
                .background(MaterialTheme.colorScheme.background)
        ) {
            // Main content area
            Box(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth()
            ) {
                when {
                    // Show error state for conversation loading errors
                    state.error != null && state.conversation == null -> {
                        ScreenErrorState(
                            title = stringResource(R.string.chat_screen_error_state_title),
                            description = state.error
                                ?: stringResource(R.string.common_unknown_error),
                            actionText = stringResource(R.string.common_refresh),
                            onActionClicked = {
                                viewModel.retry()
                            }
                        )
                    }

                    // Show loading state
                    state.isLoading && state.conversation == null -> {
                        IndeterminateLoading()
                    }

                    // Show empty state when no conversation
                    state.conversation == null -> {
                        ScreenEmptyState(
                            title = R.string.chat_screen_empty_state_title,
                            description = R.string.chat_screen_empty_state_description,
                            actionText = R.string.chat_screen_empty_state_action,
                            onActionClicked = {
                                // This will be handled by the parent component
                                // when a conversation is selected
                            }
                        )
                    }

                    // Check for Paging3 error states when we have a conversation
                    messagesPaging != null && messagesPaging.loadState.refresh is LoadState.Error -> {
                        ScreenErrorState(
                            title = stringResource(R.string.chat_screen_error_state_title),
                            description = messagesPaging.getErrorAsUiText().getValue(context),
                            actionText = stringResource(R.string.common_refresh),
                            onActionClicked = {
                                messagesPaging.retry()
                            }
                        )
                    }

                    // Show messages when everything is loaded successfully
                    messagesPaging != null -> {
                        MessageListPaging(
                            messagesPaging = messagesPaging,
                            onMessageCopied = { message ->
                                viewModel.onMessageCopied()
                            },
                            onImageClick = { imageUrl ->
                                showImagePreview = imageUrl
                            }
                        )
                    }

                    // Fallback empty state
                    else -> {
                        ScreenEmptyState(
                            title = R.string.chat_screen_empty_state_title,
                            description = R.string.chat_screen_empty_state_description,
                            actionText = R.string.chat_screen_empty_state_action,
                            onActionClicked = {
                                // This will be handled by the parent component
                                // when a conversation is selected
                            }
                        )
                    }
                }
            }

            // Chat input at the bottom with keyboard handling
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .windowInsetsPadding(WindowInsets.ime)
            ) {
                ChatInput(
                    text = messageText,
                    onTextChange = { messageText = it },
                    onSendMessage = {
                        if (pendingAttachments.isNotEmpty()) {
                            pendingAttachments.forEach { attachment ->
                                viewModel.sendMessage(
                                    messageText,
                                    Attachment(
                                        attachment.filePath,
                                        attachment.previewUri,
                                        attachment.type
                                    )
                                )
                            }
                            pendingAttachments = emptyList()
                            messageText = ""
                        } else if (messageText.isNotBlank()) {
                            viewModel.sendMessage(messageText)
                            messageText = ""
                        }
                    },
                    onAttachmentClick = { showImageOptions = true },
                    isBlocked = state.conversation?.isBlockedByMe == true,
                    isSending = state.isSending,
                    modifier = Modifier.fillMaxWidth(),
                    attachments = pendingAttachments,
                    onRemoveAttachment = { toRemove ->
                        pendingAttachments = pendingAttachments.filter { it != toRemove }
                    },
                    showAttachmentButton = true
                )
            }
        }
    }
    // Permission launchers
    val cameraPermissionLauncher = rememberCameraPermissionLauncher(
        context = context,
        scope = scope,
        cameraLauncher = cameraLauncher,
        setFilePath = { currentPhotoFile = it }
    )

    val galleryPermissionLauncher = rememberMultipleContentPermissionLauncher(
        mimeType = "image/*",
        scope = scope,
        galleryLauncher = galleryLauncher
    )

    if (showImageOptions) {
        ImageOptionsBottomSheet(
            onDismiss = { showImageOptions = false },
            onOptionSelected = { option ->
                showImageOptions = false
                when (option) {
                    PickImageChooserType.GALLERY -> {
                        checkImageGalleryPermissionGranted(
                            mimeType = "image/*",
                            context = context,
                            galleryPermissionLauncher = galleryPermissionLauncher,
                            galleryLauncher = galleryLauncher
                        )
                    }

                    PickImageChooserType.CAMERA -> {
                        handleCameraSelection(
                            context = context,
                            scope = scope,
                            cameraPermissionLauncher = cameraPermissionLauncher,
                            cameraLauncher = cameraLauncher,
                            setFilePath = { currentPhotoFile = it }
                        )
                    }

                    PickImageChooserType.DOCUMENT -> {
                        documentLauncher.launch("*/*")
                    }
                }
            }
        )
    }

    showImagePreview?.let { imageUrl ->
        ImagePreviewScreen(
            imageUrl = imageUrl,
            onDismiss = { showImagePreview = null }
        )
    }

    // Block/Unblock confirmation dialog
    if (showBlockConfirmation) {
        AvConfirmationAlertDialog(
            title = if (state.conversation?.isBlockedByMe == true) stringResource(R.string.conversation_list_unblock_conversation_title) else stringResource(
                R.string.conversation_list_block_conversation_title
            ),
            description = if (state.conversation?.isBlockedByMe == true) stringResource(R.string.conversation_list_unblock_conversation_description) else stringResource(
                R.string.conversation_list_block_conversation_description
            ),
            onConfirm = {
                if (state.conversation?.isBlockedByMe == true) {
                    viewModel.unblockUser()
                } else {
                    viewModel.blockUser()
                }
                showBlockConfirmation = false
            },
            onCancel = { showBlockConfirmation = false }
        )
    }

    // Clear chat confirmation dialog
    if (showClearConfirmation) {
        AvConfirmationAlertDialog(
            title = stringResource(R.string.chat_screen_clear_chat_title),
            description = stringResource(R.string.chat_screen_clear_chat_description),
            onConfirm = {
                viewModel.clearChat()
                showClearConfirmation = false
            },
            onCancel = { showClearConfirmation = false }
        )
    }
}

fun checkImageGalleryPermissionGranted(
    mimeType: String,
    context: Context,
    galleryPermissionLauncher: ManagedActivityResultLauncher<String, Boolean>,
    galleryLauncher: ManagedActivityResultLauncher<String, List<@JvmSuppressWildcards Uri>>
) {
    // For Android 13+ (API 33+), use READ_MEDIA_IMAGES permission
    val permission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
        Manifest.permission.READ_MEDIA_IMAGES
    } else {
        Manifest.permission.READ_EXTERNAL_STORAGE
    }

    when {
        ContextCompat.checkSelfPermission(
            context,
            permission
        ) == PackageManager.PERMISSION_GRANTED -> {
            galleryLauncher.launch(mimeType)
        }

        else -> galleryPermissionLauncher.launch(permission)
    }
}

fun handleCameraSelection(
    context: Context,
    scope: CoroutineScope,
    cameraPermissionLauncher: ManagedActivityResultLauncher<String, Boolean>,
    cameraLauncher: ManagedActivityResultLauncher<Uri, Boolean>,
    setFilePath: (File) -> Unit
) {
    when (PackageManager.PERMISSION_GRANTED) {
        ContextCompat.checkSelfPermission(context, Manifest.permission.CAMERA) -> {
            launchCamera(
                context = context,
                cameraLauncher = cameraLauncher,
                scope = scope,
                setFilePath = setFilePath
            )
        }

        else -> cameraPermissionLauncher.launch(Manifest.permission.CAMERA)
    }
}

@Composable
fun rememberMultipleContentPermissionLauncher(
    mimeType: String,
    scope: CoroutineScope,
    galleryLauncher: ManagedActivityResultLauncher<String, List<Uri>>,
    notGrantedMessage: Int = R.string.common_image_gallery_permissions_required
): ManagedActivityResultLauncher<String, Boolean> {
    return rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            galleryLauncher.launch(mimeType)
        } else {
            scope.launch {
                SnackBarController.showSnackBar(
                    SnackBarEvent(
                        message = UiText.FromRes(notGrantedMessage),
                        type = SnackBarType.WARNING
                    )
                )
            }
        }
    }
}

@Composable
fun rememberCameraPermissionLauncher(
    context: Context,
    scope: CoroutineScope,
    cameraLauncher: ManagedActivityResultLauncher<Uri, Boolean>,
    setFilePath: (File) -> Unit
): ManagedActivityResultLauncher<String, Boolean> {
    return rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            launchCamera(
                context = context,
                cameraLauncher = cameraLauncher,
                scope = scope,
                setFilePath = setFilePath
            )
        } else {
            scope.launch {
                SnackBarController.showSnackBar(
                    SnackBarEvent(
                        message = UiText.FromRes(R.string.common_camera_permissions_required),
                        type = SnackBarType.WARNING
                    )
                )
            }
        }
    }
}

fun launchCamera(
    context: Context,
    cameraLauncher: ManagedActivityResultLauncher<Uri, Boolean>,
    scope: CoroutineScope,
    setFilePath: (File) -> Unit
) {
    try {
        IOUtils.createCameraImageTempFile(context)?.let { file ->
            setFilePath(file)
            val pictureUri: Uri = FileProvider.getUriForFile(
                context,
                context.packageName + ".imagesprovider",
                file
            )
            cameraLauncher.launch(pictureUri)
        }
    } catch (e: IOException) {
        scope.launch {
            SnackBarController.showSnackBar(
                SnackBarEvent(
                    message = UiText.FromRes(R.string.common_error_camera_launch),
                    type = SnackBarType.ERROR
                )
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ChatTopBar(
    conversation: Conversation?,
    onNavigateUp: () -> Unit,
    onBlockUser: () -> Unit,
    onClearChat: () -> Unit,
    onOpenAd: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    var showMenu by remember { mutableStateOf(false) }

    TopAppBar(
        title = {
            if (conversation == null) {
                // Show loading state when conversation is null
                Text(
                    text = stringResource(R.string.common_loading),
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.onSurface
                )
            } else {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium),
                    modifier = Modifier.clickable {
                        conversation.ad?.listId?.let { adId ->
                            onOpenAd(adId)
                        }
                    }
                ) {
                    if (conversation.ad?.media?.defaultImage?.paths?.smallThumbnail != null) {
                        AsyncImage(
                            model = conversation.ad!!.media!!.defaultImage!!.paths.smallThumbnail,
                            contentDescription = "Ad image",
                            modifier = Modifier
                                .size(MaterialTheme.dimens.mediumBig)
                                .clip(RoundedCornerShape(MaterialTheme.dimens.small)),
                            contentScale = ContentScale.Crop
                        )
                    } else {
                        Icon(
                            painter = painterResource(id = R.drawable.ic_no_image),
                            contentDescription = "No image",
                            modifier = Modifier
                                .size(MaterialTheme.dimens.mediumBig)
                                .clip(RoundedCornerShape(MaterialTheme.dimens.small)),
                            tint = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                    Column {
                        AvDynamicText(
                            text = conversation.ad?.title
                                ?: stringResource(id = R.string.common_ad_not_available),
                            color = MaterialTheme.colorScheme.onSurface,
                            style = MaterialTheme.typography.bodyLarge,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        )
                        AvScreenSubTitle(
                            title = if (conversation.ad?.price?.withCurrency.isNullOrBlank()) {
                                R.string.common_price_not_specified
                            } else {
                                // Since we need to handle dynamic price, we'll use a different approach
                                R.string.common_price_not_specified
                            },
                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }
            }
        },
        navigationIcon = {
            val layoutDirection = androidx.compose.ui.platform.LocalLayoutDirection.current
            IconButton(onClick = onNavigateUp) {
                Icon(
                    imageVector = if (layoutDirection == androidx.compose.ui.unit.LayoutDirection.Rtl) Icons.AutoMirrored.Filled.ArrowBack else Icons.Default.ArrowBack,
                    contentDescription = stringResource(R.string.chat_screen_back),
                    tint = MaterialTheme.colorScheme.onSurface
                )
            }
        },
        actions = {
            IconButton(onClick = { showMenu = true }) {
                Icon(
                    imageVector = Icons.Default.MoreVert,
                    contentDescription = stringResource(R.string.chat_screen_more_options),
                    tint = MaterialTheme.colorScheme.onSurface
                )
            }
            DropdownMenu(
                expanded = showMenu,
                onDismissRequest = { showMenu = false },
                offset = DpOffset(
                    x = (-MaterialTheme.dimens.medium),
                    y = MaterialTheme.dimens.none
                ),
                modifier = Modifier.background(MaterialTheme.colorScheme.surfaceContainerHighest)
            ) {

                val itemModifier = Modifier
                    .fillMaxWidth() // Ensure each item takes full width
                    .padding(vertical = MaterialTheme.dimens.none) // Reduce vertical padding between items

                DropdownMenuItem(
                    text = {
                        Text(
                            text = if (conversation?.isBlockedByMe == true) stringResource(R.string.conversation_list_unblock_conversation_title) else stringResource(
                                R.string.conversation_list_block_conversation_title
                            )
                        )
                    },
                    onClick = {
                        showMenu = false
                        onBlockUser()
                    },
                    itemModifier
                )
                DropdownMenuItem(
                    text = {
                        Text(
                            text = stringResource(R.string.chat_screen_clear_chat_title)
                        )
                    },
                    onClick = {
                        showMenu = false
                        onClearChat()
                    },
                    itemModifier
                )
            }
        },
        colors = TopAppBarDefaults.topAppBarColors(
            containerColor = MaterialTheme.colorScheme.surface,
            titleContentColor = MaterialTheme.colorScheme.onSurface,
            navigationIconContentColor = MaterialTheme.colorScheme.onSurface,
            actionIconContentColor = MaterialTheme.colorScheme.onSurface
        ),
        modifier = modifier.shadow(MaterialTheme.dimens.small),
        windowInsets = WindowInsets(MaterialTheme.dimens.none)
    )
}

@Composable
private fun MessageListPaging(
    messagesPaging: LazyPagingItems<Message>,
    onMessageCopied: (String) -> Unit,
    onImageClick: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    val listState = rememberLazyListState()


    // Auto-scroll when Paging3 messages are first loaded
    LaunchedEffect(messagesPaging.loadState.refresh) {
        if (messagesPaging.loadState.refresh is LoadState.NotLoading && messagesPaging.itemCount > 0) {
            listState.animateScrollToItem(0) // Scroll to first item (most recent due to reverseLayout)
        }
    }

    LazyColumn(
        state = listState,
        modifier = Modifier
            .fillMaxSize()
            .padding(horizontal = MaterialTheme.dimens.screenPaddingHorizontal),
        reverseLayout = true, // Restore reverseLayout for proper Paging3 pagination
        contentPadding = PaddingValues(vertical = MaterialTheme.dimens.screenPaddingVertical),
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium)
    ) {
        // Show refresh indicator at the top when refreshing (even if itemCount is 0 temporarily)
        if (messagesPaging.isLoading()) {
            item {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(MaterialTheme.dimens.medium),
                    contentAlignment = Alignment.Center
                ) {
                    IndeterminateLoading()
                }
            }
        }

        // Display Paging3 messages
        items(
            count = messagesPaging.itemCount,
            key = { index ->
                val message = messagesPaging[index]
                val messageId = message?.id ?: "null"
                val timestamp = message?.time?.time ?: 0L
                "paging-$messageId-$timestamp-$index"
            }
        ) { index ->
            val message = messagesPaging[index]
            if (message != null) {
                MessageItem(
                    message = message,
                    onCopy = { onMessageCopied(message.text) },
                    onImageClick = onImageClick
                )
            }
        }

        // Show empty state when no messages and not refreshing
        if (messagesPaging.isEmpty() && messagesPaging.loadState.refresh is LoadState.NotLoading) {
            item {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(MaterialTheme.dimens.extraBig),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.default)
                    ) {
                        Text(
                            text = "No messages yet",
                            style = MaterialTheme.typography.headlineSmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Text(
                            text = "Start the conversation by sending a message",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                            textAlign = TextAlign.Center
                        )
                    }
                }
            }
        }

        // Loading indicator for append state
        if (messagesPaging.loadState.append is LoadState.Loading) {
            item {
                Box(
                    modifier = Modifier.fillMaxWidth(),
                    contentAlignment = Alignment.Center
                ) {
                    IndeterminateLoading()
                }
            }
        }
    }
}


@OptIn(ExperimentalFoundationApi::class)
@Composable
private fun MessageItem(
    message: Message,
    onCopy: () -> Unit,
    onImageClick: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    val appLocale = remember {
        val lang = LocaleManager.getCurrentLanguage()
        when (lang) {
            "ar" -> Locale("ar", "MA")
            "fr" -> Locale("fr", "MA")
            else -> Locale("fr", "MA")
        }
    }
    val timeFormat = remember(appLocale) { SimpleDateFormat("HH:mm", appLocale) }
    val dateTimeFormat = remember(appLocale) { SimpleDateFormat("dd MMM", appLocale) }
    val fullDateFormat = remember(appLocale) { SimpleDateFormat("dd MMM yyyy", appLocale) }
    val formattedDate = remember(message.time) {
        val now = Calendar.getInstance()
        val messageTime = Calendar.getInstance().apply { time = message.time }

        when {
            // Same day
            now.get(Calendar.YEAR) == messageTime.get(Calendar.YEAR) &&
                    now.get(Calendar.DAY_OF_YEAR) == messageTime.get(Calendar.DAY_OF_YEAR) -> {
                timeFormat.format(message.time)
            }
            // Same year
            now.get(Calendar.YEAR) == messageTime.get(Calendar.YEAR) -> {
                dateTimeFormat.format(message.time)
            }
            // Different year
            else -> {
                fullDateFormat.format(message.time)
            }
        }
    }
    val clipboardManager = LocalClipboardManager.current
    val interactionSource = remember { MutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()
    val context = LocalContext.current

    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = if (message.isMine) Arrangement.End else Arrangement.Start
    ) {
        Card(
            modifier = Modifier
                .widthIn(max = MaterialTheme.dimens.extraExtraBig + MaterialTheme.dimens.extraBig)
                .combinedClickable(
                    interactionSource = interactionSource,
                    indication = null,
                    onClick = {
                        message.attachment?.let { attachment ->
                            if (attachment.type.startsWith("image/")) {
                                onImageClick(attachment.previewUri)
                            } else {
                                // Open file in browser or appropriate app
                                val intent =
                                    Intent(Intent.ACTION_VIEW, Uri.parse(attachment.previewUri))
                                context.startActivity(intent)
                            }
                        }
                    },
                    onLongClick = {
                        clipboardManager.setText(AnnotatedString(message.text))
                        onCopy()
                    }
                ),
            colors = CardDefaults.cardColors(
                containerColor = if (isPressed) {
                    if (message.isMine) {
                        MaterialTheme.colorScheme.primary
                    } else {
                        MaterialTheme.colorScheme.surfaceContainer.copy(alpha = 0.7f)
                    }
                } else {
                    if (message.isMine) {
                        MaterialTheme.colorScheme.primary
                    } else {
                        MaterialTheme.colorScheme.surfaceContainer
                    }
                }
            ),
            shape = MaterialTheme.shapes.medium
        ) {
            Column(
                modifier = Modifier.padding(MaterialTheme.dimens.medium)
            ) {
                if (message.text.isNotBlank()) {
                    Text(
                        text = message.text,
                        style = MaterialTheme.typography.bodyMedium,
                        color = if (message.isMine) MaterialTheme.colorScheme.onPrimary else MaterialTheme.colorScheme.onSurface
                    )
                }
                message.attachment?.let { attachment ->
                    when {
                        attachment.type.startsWith("image/") -> {
                            AsyncImage(
                                model = attachment.previewUri,
                                contentDescription = "Image attachment",
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(MaterialTheme.dimens.screenImageHeight)
                                    .clip(MaterialTheme.shapes.medium)
                                    .clickable { onImageClick(attachment.previewUri) },
                                contentScale = ContentScale.Crop
                            )
                        }

                        else -> {
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = MaterialTheme.dimens.small)
                            ) {
                                AvTextWithEndIcon(
                                    text = stringResource(R.string.chat_screen_file),
                                    iconRes = R.drawable.ic_attachment,
                                    textColor = MaterialTheme.colorScheme.onSurfaceVariant,
                                    iconTint = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                        }
                    }
                }
                Text(
                    text = formattedDate,
                    style = MaterialTheme.typography.bodySmall,
                    color = if (message.isMine) MaterialTheme.colorScheme.onPrimary.copy(alpha = 0.7f) else MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.align(Alignment.End)
                )
            }
        }
    }
}

private fun getFileName(context: Context, uri: Uri): String {
    var result: String? = null
    if (uri.scheme == "content") {
        val cursor = context.contentResolver.query(uri, null, null, null, null)
        try {
            if (cursor != null && cursor.moveToFirst()) {
                result = cursor.getString(cursor.getColumnIndex(OpenableColumns.DISPLAY_NAME))
            }
        } finally {
            cursor?.close()
        }
    }
    if (result == null) {
        result = uri.path
        val cut = result?.lastIndexOf(File.separator)
        if (cut != -1) {
            result = result?.substring(cut!! + 1)
        }
    }
    return result ?: "file_${System.currentTimeMillis()}"
} 
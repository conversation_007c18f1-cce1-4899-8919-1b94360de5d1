package se.scmv.morocco.account.presentation.orders

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import se.scmv.morocco.account.R
import se.scmv.morocco.analytics.AnalyticsHelper
import se.scmv.morocco.analytics.models.AnalyticsEvent
import se.scmv.morocco.analytics.models.Param
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.domain.models.AccountOrder
import se.scmv.morocco.domain.models.AccountOrderStatus
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.repositories.AccountRepository
import se.scmv.morocco.ui.renderFailure
import se.scmv.morocco.ui.renderSuccess
import javax.inject.Inject

@HiltViewModel
class AccountOrdersViewModel @Inject constructor(
    private val accountRepository: AccountRepository,
    private val analyticsHelper: AnalyticsHelper
) : ViewModel() {

    private val _selectedOrderStatus = MutableStateFlow(AccountOrderStatus.INITIATED)
    val selectedOrderStatus = _selectedOrderStatus.asStateFlow()

    // No replay needed for one-time events.
    private val refreshTrigger = MutableSharedFlow<Unit>()

    private val _oneTimeEvents = MutableSharedFlow<AccountOrdersCancelOrderStatus>()
    val oneTimeEvents = _oneTimeEvents.asSharedFlow()

    @OptIn(ExperimentalCoroutinesApi::class)
    val orders: Flow<PagingData<AccountOrder>> = refreshTrigger
        // Emit initially to load data on first launch
        .onStart { emit(Unit) }
        .flatMapLatest {
            _selectedOrderStatus.flatMapLatest {
                accountRepository.getAccountOrders(it).cachedIn(viewModelScope)
            }
        }

    fun onStatusChanged(status: AccountOrderStatus) {
        if (_selectedOrderStatus.value == status) return

        viewModelScope.launch {
            _selectedOrderStatus.update { status }
            trackSelectStatus(status)
        }
    }

    fun onCancelOrder(orderId: String) {
        viewModelScope.launch {
            _oneTimeEvents.emit(AccountOrdersCancelOrderStatus(true))
            val result = accountRepository.cancelOrder(orderId)
            _oneTimeEvents.emit(AccountOrdersCancelOrderStatus(false))
            when (result) {
                is Resource.Success -> {
                    refreshTrigger.emit(Unit)
                    renderSuccess(UiText.FromRes(R.string.order_screen_cancel_order_success))
                    trackCancelOrder()
                }

                is Resource.Failure -> renderFailure(result.error)
            }
        }
    }

    private fun trackCancelOrder() {
        analyticsHelper.logEvent(
            event = AnalyticsEvent(
                name = AnalyticsEvent.Types.ELEMENT_CLICKED,
                properties = setOf(
                    Param(
                        key = AnalyticsEvent.ParamKeys.ELEMENT_NAME,
                        value = AnalyticsEvent.ParamValues.CANCEL_ORDER
                    )
                )
            )
        )
    }

    private fun trackSelectStatus(status: AccountOrderStatus) {
        analyticsHelper.logEvent(
            event = AnalyticsEvent(
                name = AnalyticsEvent.Types.ELEMENT_CLICKED,
                properties = setOf(
                    Param(
                        key = AnalyticsEvent.ParamKeys.ELEMENT_NAME,
                        value = AnalyticsEvent.ParamValues.SELECT_STATUS
                    ),
                    Param(
                        key = AnalyticsEvent.ParamKeys.PAGE_NAME,
                        value = AnalyticsEvent.ScreensNames.ORDERS
                    ),
                    Param(
                        key = AnalyticsEvent.ParamKeys.VALUE,
                        value = status.getAnalyticsName()
                    )
                )
            )
        )
    }
}

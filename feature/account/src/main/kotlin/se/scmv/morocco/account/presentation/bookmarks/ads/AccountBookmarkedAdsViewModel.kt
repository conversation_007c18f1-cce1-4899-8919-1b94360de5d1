package se.scmv.morocco.account.presentation.bookmarks.ads

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.cachedIn
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import se.scmv.morocco.account.R
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.domain.models.ListingAd
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.repositories.AccountRepository
import se.scmv.morocco.domain.shared.BookmarkAdEvent
import se.scmv.morocco.domain.shared.BookmarkAdsEventManager
import se.scmv.morocco.ui.renderFailure
import se.scmv.morocco.ui.renderSuccess
import javax.inject.Inject

@HiltViewModel
class AccountBookmarkedAdsViewModel @Inject constructor(
    private val accountRepository: AccountRepository,
    private val bookmarkAdsEventManager: BookmarkAdsEventManager,
) : ViewModel() {

    private val _oneTimeEvents = MutableSharedFlow<AccountBookmarkedAdsOneTimeEvents>()
    val oneTimeEvents = _oneTimeEvents.asSharedFlow()

    val ads = accountRepository.getBookmarkedAds().cachedIn(viewModelScope)

    fun onUnBookmarkAd(ad: ListingAd.Published) {
        viewModelScope.launch {
            when (val result = accountRepository.unBookmarkAd(ad.listId)) {
                is Resource.Success -> {
                    renderSuccess(UiText.FromRes(R.string.bookmarked_ads_screen_delete_ad_success))
                    _oneTimeEvents.emit(
                        AccountBookmarkedAdsOneTimeEvents.DeleteAdSuccess(adId = ad.id)
                    )
                    bookmarkAdsEventManager.sendEvent(
                        event = BookmarkAdEvent(listId = ad.listId, isFavorite = false)
                    )
                }

                is Resource.Failure -> renderFailure(result.error)
            }
        }
    }
}
package se.scmv.morocco.account.presentation.master

import android.content.res.Configuration
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Call
import androidx.compose.material.icons.filled.Email
import androidx.compose.material.icons.filled.LocationOn
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedIconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalUriHandler
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import coil.compose.AsyncImage
import coil.decode.SvgDecoder
import coil.request.ImageRequest
import kotlinx.coroutines.flow.collectLatest
import kotlinx.datetime.LocalDate
import se.scmv.morocco.account.R
import se.scmv.morocco.analytics.TrackScreenViewEvent
import se.scmv.morocco.analytics.models.AnalyticsEvent
import se.scmv.morocco.analytics.models.Param
import se.scmv.morocco.designsystem.components.AvAlertDialog
import se.scmv.morocco.designsystem.components.AvAlertType
import se.scmv.morocco.designsystem.components.AvConfirmationAlertDialog
import se.scmv.morocco.designsystem.components.AvProgressBar
import se.scmv.morocco.designsystem.components.RedirectionItem
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.Black
import se.scmv.morocco.designsystem.theme.Brown500
import se.scmv.morocco.designsystem.theme.Gray100
import se.scmv.morocco.designsystem.theme.Green100
import se.scmv.morocco.designsystem.theme.Green500
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.domain.models.Account
import se.scmv.morocco.domain.models.AccountInfo
import se.scmv.morocco.domain.models.AllowedAccess
import se.scmv.morocco.domain.models.Category
import se.scmv.morocco.domain.models.City
import se.scmv.morocco.domain.models.StoreInfo

@Composable
fun AccountMasterRoute(
    account: Account.Connected,
    navigateToAccountEdit: () -> Unit,
    navigateToAccountAds: () -> Unit,
    navigateToAccountOrders: () -> Unit,
    navigateToUpdatePassword: (String) -> Unit,
    viewModel: AccountMasterViewModel = hiltViewModel<AccountMasterViewModel>(),
) {
    val contactSupportData = createContactSupportData()
    val contactAgentPhone = stringResource(se.scmv.morocco.ui.R.string.customer_services_phone)
    var showLogoutConfirmationAlert by remember { mutableStateOf(false) }
    var showContactSupportAlert by remember { mutableStateOf(false) }
    var showContactAgentAlert by remember { mutableStateOf(false) }
    when (account) {
        is Account.Connected.Private -> PrivateAccountScreen(
            info = account.contact,
            openAccountAds = navigateToAccountAds,
            openAccountOrders = navigateToAccountOrders,
            onSettingsClicked = navigateToAccountEdit,
            openPasswordChange = { navigateToUpdatePassword(account.contact.email) },
            openContactSupport = {
                viewModel.contactSupportThroughEmail(
                    data = contactSupportData,
                    onError = { showContactSupportAlert = true }
                )
            },
            logoutUser = { showLogoutConfirmationAlert = true },
        )

        is Account.Connected.Shop -> ShopAccountScreen(
            accountInfo = account.contact,
            storeInfo = account.store,
            onSettingsClicked = navigateToAccountEdit,
            openMyAds = navigateToAccountAds,
            openPasswordChange = { navigateToUpdatePassword(account.contact.email) },
            openContactSupport = {
                viewModel.contactSupportThroughEmail(
                    data = contactSupportData,
                    onError = { showContactSupportAlert = true }
                )
            },
            openContactAgent = {
                viewModel.contactSupportThroughWhatsapp(
                    phone = contactAgentPhone,
                    onError = { showContactAgentAlert = true }
                )
            },
            logoutUser = { showLogoutConfirmationAlert = true }
        )
    }
    if (showLogoutConfirmationAlert) {
        AvConfirmationAlertDialog(
            title = stringResource(R.string.account_master_screen_log_out_alert_title),
            description = stringResource(R.string.account_master_screen_log_out_alert_description),
            onConfirm = {
                viewModel.onLogout()
                showLogoutConfirmationAlert = false
            },
            onCancel = { showLogoutConfirmationAlert = false },
        )
    }
    if (showContactSupportAlert) {
        AvAlertDialog(
            text = stringResource(R.string.customer_services_email_no_apps_description),
            type = AvAlertType.Warning,
            onDismissRequest = { showContactSupportAlert = false }
        )
    }
    if (showContactAgentAlert) {
        AvAlertDialog(
            text = stringResource(R.string.customer_services_whatsapp_not_installed_description),
            type = AvAlertType.Warning,
            onDismissRequest = { showContactAgentAlert = false }
        )
    }
    var showProgressBar by remember { mutableStateOf(false) }
    if (showProgressBar) {
        AvProgressBar(text = stringResource(R.string.common_logout_processing))
    }
    LaunchedEffect(key1 = Unit) {
        viewModel.oneTimeEvents.collectLatest {
            showProgressBar = it.showHideProgress
        }
    }
    TrackScreenViewEvent(
        screenName = AnalyticsEvent.ScreensNames.ACCOUNT,
        properties = setOf(
            Param(
                key = AnalyticsEvent.ParamKeys.ACCOUNT_TYPE,
                value = account.analyticsAccountType()
            )
        )
    )
}

@Composable
private fun PrivateAccountScreen(
    modifier: Modifier = Modifier,
    info: AccountInfo,
    onSettingsClicked: () -> Unit,
    openAccountAds: () -> Unit,
    openAccountOrders: () -> Unit,
    openPasswordChange: () -> Unit,
    openContactSupport: () -> Unit,
    logoutUser: () -> Unit,
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(horizontal = MaterialTheme.dimens.screenPaddingHorizontal),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.big),
    ) {
        AccountInfo(
            imageUrl = null,
            accountName = info.name,
            email = info.email,
            phone = info.phone,
            location = info.location?.name,
            onSettingsClicked = onSettingsClicked
        )
        // TODO check if ecommerce_enabled = true in remote config to show/hide onOpenMyOrdersClicked
        Redirections(
            onOpenMyAdsClicked = openAccountAds,
            onOpenMyOrdersClicked = openAccountOrders,
            onOpenPasswordChangeClicked = openPasswordChange,
            onOpenContactSupportClicked = openContactSupport,
            onLogoutUserClicked = logoutUser
        )
    }
}

@Preview(uiMode = Configuration.UI_MODE_NIGHT_YES or Configuration.UI_MODE_TYPE_NORMAL)
@Composable
private fun PrivateAccountScreenPreview() {
    AvitoTheme {
        PrivateAccountScreen(
            info = AccountInfo(
                name = "Jhon DOE",
                email = "<EMAIL>",
                phone = "+212 666-666666",
                location = City(
                    id = "nisl",
                    name = "Lelia Palmer",
                    trackingName = "Krystal Clements"
                ),
                accountId = "natum",
                creationDate = "02-02-2022"
            ),
            onSettingsClicked = {},
            openAccountAds = {},
            openAccountOrders = {},
            openPasswordChange = {},
            openContactSupport = {},
            logoutUser = {}
        )
    }
}

@Composable
private fun ShopAccountScreen(
    modifier: Modifier = Modifier,
    accountInfo: AccountInfo,
    storeInfo: StoreInfo,
    onSettingsClicked: () -> Unit,
    openMyAds: () -> Unit,
    openPasswordChange: () -> Unit,
    openContactSupport: () -> Unit,
    openContactAgent: () -> Unit,
    logoutUser: () -> Unit,
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(horizontal = MaterialTheme.dimens.screenPaddingHorizontal,),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.big)
    ) {
        AccountInfo(
            imageUrl = storeInfo.logoUrl,
            accountName = accountInfo.name,
            isVerifiedSeller = storeInfo.verified,
            email = accountInfo.email,
            phone = accountInfo.phone,
            location = accountInfo.location?.address,
            onSettingsClicked = onSettingsClicked
        )
        ShopInfos(storeInfo = storeInfo, openContactAgent = openContactAgent)
        Redirections(
            onOpenMyAdsClicked = openMyAds,
            onOpenMyOrdersClicked = null,
            onOpenPasswordChangeClicked = openPasswordChange,
            onOpenContactSupportClicked = openContactSupport,
            onLogoutUserClicked = logoutUser
        )
    }
}

@Preview(uiMode = Configuration.UI_MODE_NIGHT_YES or Configuration.UI_MODE_TYPE_NORMAL)
@Composable
private fun ShopAccountScreenPreview() {
    AvitoTheme {
        ShopAccountScreen(
            accountInfo = AccountInfo(
                name = "Jhon DOE",
                email = "<EMAIL>",
                phone = "+212 666-666666",
                location = City(
                    id = "nisl",
                    name = "Free Palestine",
                    address = "20 rue Free Palestine",
                    trackingName = "Krystal Clements"
                ),
                accountId = "patrioque",
                creationDate = "02-02-2022"
            ),
            storeInfo = StoreInfo(
                logoUrl = null,
                points = 1200000,
                membership = "Base",
                category = Category(
                    id = "malesuada",
                    name = "Aurora Allen",
                    icon = "",
                    trackingName = "Shauna Robertson",
                    parent = null
                ),
                website = "https://www.avito.ma",
                verified = true,
                shortDescription = null,
                longDescription = null,
                cities = emptyList(),
                phones = emptyList(),
                pointsExpirationDate = LocalDate.parse("2025-12-12"),
                startDate = LocalDate.parse("2025-12-12"),
                expirationDate = LocalDate.parse("2025-12-12"),
                allowedAccess = AllowedAccess.default.copy(supportViaWhatsappAllowed = true)
            ),
            onSettingsClicked = {},
            openMyAds = {},
            openPasswordChange = {},
            openContactSupport = {},
            openContactAgent = {},
            logoutUser = {},
        )
    }
}

@Composable
private fun AccountInfo(
    modifier: Modifier = Modifier,
    imageUrl: String?,
    accountName: String,
    isVerifiedSeller: Boolean = false,
    email: String,
    phone: String?,
    location: String?,
    onSettingsClicked: () -> Unit
) {
    Card {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(MaterialTheme.dimens.large),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.large),
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.large),
                verticalAlignment = Alignment.Top
            ) {
                Card(
                    shape = CircleShape,
                    elevation = CardDefaults.elevatedCardElevation(defaultElevation = 2.dp)
                ) {
                    imageUrl?.let {
                        AsyncImage(
                            model = it,
                            contentDescription = null,
                            contentScale = ContentScale.Crop,
                            error = painterResource(id = R.drawable.img_user_placeholder)
                        )
                    } ?: Image(
                        modifier = Modifier.size(60.dp),
                        painter = painterResource(id = R.drawable.img_user_placeholder),
                        contentDescription = null,
                    )
                }
                Row {
                    Column(
                        modifier = modifier.weight(1f),
                        horizontalAlignment = Alignment.Start,
                        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium)
                    ) {
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = accountName,
                                style = MaterialTheme.typography.titleLarge,
                                fontWeight = FontWeight.Bold,
                            )
                            if (isVerifiedSeller) {
                                Icon(
                                    painter = painterResource(R.drawable.ic_verified),
                                    contentDescription = null,
                                    tint = Brown500
                                )
                            }
                        }
                        Column(
                            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small)
                        ) {
                            Contact(contact = email, icon = Icons.Default.Email)
                            if (phone != null) {
                                Contact(contact = phone, icon = Icons.Default.Call)
                            }
                            if (location != null) {
                                Contact(contact = location, icon = Icons.Default.LocationOn)
                            }
                        }
                    }
                    IconButton(
                        onClick = onSettingsClicked,
                        colors = IconButtonDefaults.filledIconButtonColors(
                            containerColor = Gray100
                        )
                    ) {
                        Icon(
                            imageVector = Icons.Default.Settings,
                            contentDescription = null,
                            tint = Black
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun Contact(
    contact: String,
    icon: ImageVector
) {
    Row(horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small)) {
        Icon(
            modifier = Modifier.size(MaterialTheme.dimens.default),
            imageVector = icon,
            contentDescription = null
        )
        Text(
            text = contact,
            style = MaterialTheme.typography.bodyMedium,
        )
    }
}

@Composable
private fun Redirections(
    modifier: Modifier = Modifier,
    onOpenMyAdsClicked: () -> Unit,
    onOpenMyOrdersClicked: (() -> Unit)? = null,
    onOpenPasswordChangeClicked: () -> Unit,
    onOpenContactSupportClicked: () -> Unit,
    onLogoutUserClicked: () -> Unit,
) {
    Card(
        modifier = modifier
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    horizontal = MaterialTheme.dimens.default,
                    vertical = MaterialTheme.dimens.medium
                ),
        ) {
            RedirectionItem(
                title = R.string.account_master_screen_redirection_my_ads,
                icon = R.drawable.ic_shop,
                iconTint = Black,
                onClick = onOpenMyAdsClicked
            )
            onOpenMyOrdersClicked?.let {
                HorizontalDivider()
                RedirectionItem(
                    title = R.string.account_master_screen_redirection_my_orders,
                    icon = R.drawable.ic_shopping_bag,
                    onClick = it
                )
            }
            HorizontalDivider()
            RedirectionItem(
                title = R.string.account_master_screen_redirection_password_update,
                icon = R.drawable.ic_change_password,
                onClick = onOpenPasswordChangeClicked
            )
            HorizontalDivider()
            RedirectionItem(
                title = R.string.account_master_screen_redirection_contact_us,
                icon = R.drawable.ic_support,
                onClick = onOpenContactSupportClicked
            )
            HorizontalDivider()
            RedirectionItem(
                title = R.string.account_master_screen_redirection_log_out,
                icon = R.drawable.ic_logout,
                onClick = onLogoutUserClicked,
                iconTint = MaterialTheme.colorScheme.error
            )
        }
    }
}

@Composable
private fun ShopInfos(
    modifier: Modifier = Modifier,
    storeInfo: StoreInfo,
    openContactAgent: () -> Unit,
) {
    Card {
        Column(
            modifier = modifier
                .fillMaxWidth()
                .padding(MaterialTheme.dimens.large),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.large)
        ) {
            Text(
                modifier = Modifier.fillMaxWidth(),
                text = stringResource(R.string.account_master_screen_shop_info),
                style = MaterialTheme.typography.bodyLarge,
            )
            ShopSubscription(points = storeInfo.points, membership = storeInfo.membership)
            Column {
                ShopInfo(
                    label = R.string.common_category,
                    name = storeInfo.category.name,
                    buttonEnabled = false,
                    buttonIcon = {
                        AsyncImage(
                            modifier = Modifier.size(30.dp),
                            model = ImageRequest.Builder(LocalContext.current)
                                .data(storeInfo.category.icon)
                                .decoderFactory(SvgDecoder.Factory())
                                .build(),
                            contentDescription = null
                        )
                    },
                )
                val website = storeInfo.website
                if (!website.isNullOrBlank()) {
                    val uriHandler = LocalUriHandler.current
                    var showCantOpenWebsiteWarning by remember { mutableStateOf(false) }
                    ShopInfo(
                        label = R.string.account_master_screen_shop_website,
                        name = website,
                        buttonEnabled = true,
                        buttonIcon = {
                            Icon(
                                painter = painterResource(R.drawable.ic_external_link),
                                contentDescription = null
                            )
                        },
                        buttonClick = {
                            try {
                                uriHandler.openUri(website)
                            } catch (e: Exception) {
                                showCantOpenWebsiteWarning = true
                            }
                        }
                    )
                    if (showCantOpenWebsiteWarning) {
                        AvAlertDialog(
                            text = stringResource(R.string.account_master_screen_shop_cant_open_website_warning),
                            type = AvAlertType.Warning,
                            onDismissRequest = { showCantOpenWebsiteWarning = false }
                        )
                    }
                }
            }
            if (storeInfo.allowedAccess.supportViaWhatsappAllowed) {
                Button(
                    onClick = openContactAgent,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Green100
                    ),
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small)
                    ) {
                        Icon(
                            painter = painterResource(R.drawable.ic_whatsapp),
                            contentDescription = null,
                            tint = Green500
                        )
                        Text(
                            text = stringResource(R.string.account_master_screen_shop_contact_support),
                            color = Green500
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun ShopInfo(
    label: Int,
    name: String,
    buttonEnabled: Boolean = true,
    buttonIcon: @Composable () -> Unit,
    buttonClick: (() -> Unit)? = null,
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(
            modifier = Modifier.fillMaxHeight(),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small)
        ) {
            Text(
                text = stringResource(label),
                style = MaterialTheme.typography.bodySmall,
            )
            Text(text = name, style = MaterialTheme.typography.bodyMedium)
        }
        OutlinedIconButton(
            onClick = buttonClick ?: {},
            enabled = buttonEnabled
        ) {
            buttonIcon()
        }
    }
}

@Composable
private fun ShopSubscription(
    modifier: Modifier = Modifier,
    points: Int,
    membership: String,
) {
    Row(
        modifier = modifier
            .clip(MaterialTheme.shapes.medium)
            .border(1.dp, Gray100, MaterialTheme.shapes.medium),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium)
    ) {
        Text(
            modifier = Modifier.padding(start = MaterialTheme.dimens.default),
            text = "$points",
            style = MaterialTheme.typography.headlineLarge,
            color = MaterialTheme.colorScheme.primary
        )
        Text(
            text = stringResource(R.string.common_avitokens),
            style = MaterialTheme.typography.bodyLarge,
        )
        Text(
            modifier = Modifier
                .background(color = MaterialTheme.colorScheme.primary.copy(alpha = 0.2f))
                .padding(MaterialTheme.dimens.default),
            text = membership,
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.primary,
            fontWeight = FontWeight.Bold
        )
    }
}


package se.scmv.morocco.account.presentation.edit_account

import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.consumeWindowInsets
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.window.DialogProperties
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.dialog
import androidx.navigation.compose.rememberNavController
import dagger.hilt.android.AndroidEntryPoint
import se.scmv.morocco.account.presentation.navigation.AccountScreen
import se.scmv.morocco.account.presentation.update_password.ARG_EMAIL
import se.scmv.morocco.account.presentation.update_password.UpdatePasswordRoute
import se.scmv.morocco.authentication.presentation.AuthenticationActivity
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.domain.models.Account
import se.scmv.morocco.domain.repositories.AccountRepository
import se.scmv.morocco.ui.SnackBarHostForSnackBarController
import javax.inject.Inject

// TODO Remove this activity and add EditAccountRoute composable to AccountNavHost, once all the app is in compose
@AndroidEntryPoint
class EditAccountActivity : ComponentActivity() {

    @Inject
    lateinit var accountRepository: AccountRepository

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setContent {
            AvitoTheme {
                Scaffold(
                    snackbarHost = { SnackBarHostForSnackBarController() }
                ) {
                    val account = accountRepository.currentAccount.collectAsStateWithLifecycle(null).value
                    when (account) {
                        is Account.Connected -> TemporaryNavHost(
                            modifier = Modifier.consumeWindowInsets(it),
                            account = account
                        )
                        else -> Unit
                    }
                }
            }
        }
    }

    @Composable
    private fun TemporaryNavHost(
        modifier: Modifier,
        account: Account.Connected
    ) {
        val navHostController = rememberNavController()
        NavHost(
            modifier = modifier,
            navController = navHostController,
            startDestination = AccountScreen.EDIT_ACCOUNT.route
        ) {
            composable(route = AccountScreen.EDIT_ACCOUNT.route) {
                EditAccountRoute(
                    account= account,
                    navigateBack = { onBackPressedDispatcher.onBackPressed() },
                    navigateToUpdatePassword = { email ->
                        val route = AccountScreen.UPDATE_PASSWORD.route
                            .replace("{$ARG_EMAIL}", email)
                        navHostController.navigate(route)
                    },
                    navigateToAuthentication = ::navigateToAuthentication
                )
            }
            dialog(
                route = AccountScreen.UPDATE_PASSWORD.route,
                dialogProperties = DialogProperties(
                    dismissOnClickOutside = false,
                    dismissOnBackPress = false,
                )
            ) {
                UpdatePasswordRoute(
                    account = account,
                    onFinished = { navHostController.popBackStack() },
                    navigateToAuthentication = ::navigateToAuthentication
                )
            }
        }
    }


    // TODO replace this by navigating to auth nestedNavHost, once all the app is in compose
    private fun navigateToAuthentication() {
        val intent = Intent(this, AuthenticationActivity::class.java)
        startActivityForResult(intent, AuthenticationActivity.REQUEST_SIGN_IN_MY_ACCOUNT)
    }

    companion object {
        const val REQUEST_UPDATE_ACCOUNT = 367
        const val RESULT_LOGOUT = 36
    }
}
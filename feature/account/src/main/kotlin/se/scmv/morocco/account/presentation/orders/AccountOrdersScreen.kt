package se.scmv.morocco.account.presentation.orders

import android.content.Intent
import android.net.Uri
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Done
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FilterChip
import androidx.compose.material3.FilterChipDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.OutlinedCard
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.pulltorefresh.PullToRefreshBox
import androidx.compose.material3.pulltorefresh.rememberPullToRefreshState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.paging.PagingData
import androidx.paging.compose.LazyPagingItems
import androidx.paging.compose.collectAsLazyPagingItems
import androidx.paging.compose.itemKey
import coil.compose.AsyncImage
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.flowOf
import kotlinx.datetime.LocalDateTime
import kotlinx.datetime.format
import kotlinx.datetime.format.char
import se.scmv.morocco.account.R
import se.scmv.morocco.analytics.TrackScreenViewEvent
import se.scmv.morocco.analytics.models.AnalyticsEvent
import se.scmv.morocco.analytics.models.Param
import se.scmv.morocco.designsystem.components.AvConfirmationAlertDialog
import se.scmv.morocco.designsystem.components.AvProgressBar
import se.scmv.morocco.designsystem.components.AvTopAppBar
import se.scmv.morocco.designsystem.components.ScreenEmptyState
import se.scmv.morocco.designsystem.components.ScreenErrorState
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.domain.models.AccountOrder
import se.scmv.morocco.domain.models.AccountOrderProduct
import se.scmv.morocco.domain.models.AccountOrderStatus
import se.scmv.morocco.ui.SnackBarHostForSnackBarController
import se.scmv.morocco.ui.getErrorAsUiText
import se.scmv.morocco.ui.isEmpty
import se.scmv.morocco.ui.isError
import se.scmv.morocco.ui.isLoading

@Composable
fun AccountOrdersRoute(
    navigateBack: () -> Unit,
    modifier: Modifier = Modifier,
    viewModel: AccountOrdersViewModel = hiltViewModel()
) {
    val orderPagingItems = viewModel.orders.collectAsLazyPagingItems()
    val selectedStatus = viewModel.selectedOrderStatus.collectAsStateWithLifecycle().value

    var orderToCancelId by remember { mutableStateOf<String?>(null) }
    Scaffold(
        modifier = modifier,
        topBar = {
            AvTopAppBar(
                modifier = Modifier.shadow(1.dp),
                onNavigationIconClicked = navigateBack,
                titleRes = R.string.account_master_screen_redirection_my_orders,
            )
        },
        snackbarHost = { SnackBarHostForSnackBarController() },
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .padding(innerPadding)
                .padding(
                    start = MaterialTheme.dimens.large,
                    end = MaterialTheme.dimens.large,
                )
                .fillMaxSize(),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small)
        ) {
            AccountOrdersStatusFilter(
                modifier = Modifier.fillMaxWidth(),
                selectedStatus = selectedStatus,
                onStatusChanged = viewModel::onStatusChanged
            )
            AccountOrderScreen(
                modifier = Modifier.fillMaxSize(),
                pagingItems = orderPagingItems,
                onCancelOrder = { orderId -> orderToCancelId = orderId }
            )
        }
    }
    if (orderToCancelId != null) {
        AvConfirmationAlertDialog(
            title = stringResource(R.string.order_screen_cancel_order),
            description = stringResource(R.string.order_screen_cancel_order_description),
            onConfirm = {
                orderToCancelId?.let { orderId ->
                    viewModel.onCancelOrder(orderId)
                    orderToCancelId = null
                }
            },
            onCancel = { orderToCancelId = null },
        )
    }
    var showProgressBar by remember { mutableStateOf(false) }
    if (showProgressBar) {
        AvProgressBar(text = stringResource(se.scmv.morocco.designsystem.R.string.order_screen_cancel_order_processing))
    }
    LaunchedEffect(key1 = Unit) {
        viewModel.oneTimeEvents.collectLatest {
            showProgressBar = it.isLoading
        }
    }
    TrackScreenViewEvent(
        screenName = AnalyticsEvent.ScreensNames.ORDERS,
        properties = setOf(
            Param(
                AnalyticsEvent.ParamKeys.ACCOUNT_TYPE,
                AnalyticsEvent.ParamValues.ACCOUNT_TYPE_PRIVATE
            )
        )
    )
}

@Composable
private fun AccountOrderScreen(
    modifier: Modifier = Modifier,
    pagingItems: LazyPagingItems<AccountOrder>,
    onCancelOrder: (String) -> Unit
) {
    val context = LocalContext.current
    when {
        pagingItems.isEmpty() -> ScreenEmptyState(
            modifier = modifier,
            onActionClicked = {
                val ecommerceIntent = Intent(
                    Intent.ACTION_VIEW,
                    Uri.parse("scm://app.avito/listing?category=5010&dlvr=1")
                )
                context.startActivity(ecommerceIntent)
            }
        )

        pagingItems.isError() -> {
            ScreenErrorState(
                modifier = modifier,
                title = stringResource(R.string.common_oups),
                description = pagingItems.getErrorAsUiText().getValue(context),
                actionText = stringResource(R.string.common_refresh),
                onActionClicked = pagingItems::refresh
            )
        }

        else -> AccountOrdersList(
            modifier = modifier,
            pagingItems = pagingItems,
            onCancelOrder = onCancelOrder
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun OrderScreenStatesPreview() {
    AvitoTheme {
        // Uncomment this code to preview empty state
        // val orders = PagingData.emptyLazyPagingItems<AccountOrder>()
        val orders = flowOf(
            PagingData.from(
                List(100) {
                    AccountOrder(
                        id = "singulis$it",
                        product = AccountOrderProduct(
                            listId = "$it",
                            name = "Product $it",
                            imageUrl = null,
                        ),
                        unitsPurchased = 3,
                        status = AccountOrderStatus.INITIATED,
                        date = LocalDateTime.parse("2024-11-15T18:46:59"),
                        total = 7204,
                    )
                }
            )
        ).collectAsLazyPagingItems()
        AccountOrderScreen(
            modifier = Modifier.fillMaxSize(),
            pagingItems = orders,
            onCancelOrder = {}
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AccountOrdersList(
    modifier: Modifier = Modifier,
    pagingItems: LazyPagingItems<AccountOrder>,
    onCancelOrder: (String) -> Unit
) {
    PullToRefreshBox(
        modifier = modifier,
        isRefreshing = pagingItems.isLoading(),
        state = rememberPullToRefreshState(),
        onRefresh = { pagingItems.refresh() }
    ) {
        LazyColumn(
            modifier = modifier,
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.large),
            contentPadding = PaddingValues(
                bottom = MaterialTheme.dimens.extraExtraBig,
                top = MaterialTheme.dimens.medium
            )
        ) {
            items(
                count = pagingItems.itemCount,
                key = pagingItems.itemKey { it.id + it.product.listId },
            ) {
                pagingItems[it]?.let { order ->
                    Order(
                        modifier = Modifier.animateItem(),
                        order = order,
                        onCancelClicked = { onCancelOrder(order.id) }
                    )
                }
            }
        }
    }
}

@Composable
private fun Order(
    modifier: Modifier = Modifier,
    order: AccountOrder,
    onCancelClicked: () -> Unit
) {
    OutlinedCard(
        modifier = modifier.fillMaxWidth(),
        shape = MaterialTheme.shapes.medium,
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(MaterialTheme.dimens.large),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium)
        ) {
            AsyncImage(
                modifier = Modifier
                    .weight(3f)
                    .aspectRatio(1f)
                    .clip(MaterialTheme.shapes.small),
                model = order.product.imageUrl,
                placeholder = painterResource(R.drawable.img_no_image_placeholder),
                error = painterResource(R.drawable.img_no_image_placeholder),
                contentDescription = null,
                contentScale = ContentScale.Crop,
            )
            Column(
                modifier = Modifier.weight(7f),
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium),
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = stringResource(R.string.common_price_with_currency, order.total),
                        style = MaterialTheme.typography.titleMedium
                    )
                    Text(
                        modifier = Modifier
                            .background(
                                color = order.status.bgColor(),
                                shape = MaterialTheme.shapes.extraSmall
                            )
                            .padding(
                                horizontal = MaterialTheme.dimens.large,
                                vertical = MaterialTheme.dimens.small
                            ),
                        text = stringResource(order.status.localizedNameForCard()),
                        style = MaterialTheme.typography.labelSmall,
                        color = order.status.textColor()
                    )
                }
                Column(
                    modifier = Modifier.fillMaxWidth(),
                    verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small)
                ) {
                    Text(
                        text = order.product.name,
                        style = MaterialTheme.typography.titleSmall,
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis
                    )
                    val context = LocalContext.current
                    Text(
                        text = stringResource(
                            R.string.order_screen_order_date,
                            order.date.format(
                                format = LocalDateTime.Format {
                                    // Example of display: Effectuée le 18 Décembre 2025 à 10:33
                                    dayOfMonth()
                                    char('-')
                                    monthNumber()
                                    char('-')
                                    year()
                                    char(' ')
                                    chars(context.getString(R.string.common_at))
                                    char(' ')
                                    hour()
                                    char(':')
                                    minute()
                                }
                            )
                        ),
                        style = MaterialTheme.typography.labelMedium,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                    Text(
                        text = stringResource(
                            R.string.order_screen_order_quantity,
                            order.unitsPurchased
                        ),
                        style = MaterialTheme.typography.labelMedium,
                    )
                }
                if (order.cancellable()) {
                    Box(
                        modifier = Modifier.fillMaxWidth(),
                        contentAlignment = Alignment.TopEnd
                    ) {
                        OutlinedButton(
                            shape = MaterialTheme.shapes.small,
                            colors = ButtonDefaults.outlinedButtonColors(
                                contentColor = MaterialTheme.colorScheme.onBackground
                            ),
                            onClick = onCancelClicked,
                        ) {
                            Text(text = stringResource(R.string.order_screen_cancel_order))
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun AccountOrdersStatusFilter(
    modifier: Modifier = Modifier,
    selectedStatus: AccountOrderStatus,
    onStatusChanged: (AccountOrderStatus) -> Unit
) {
    LazyRow(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small)
    ) {
        items(AccountOrderStatus.entries) { status ->
            val selected = status == selectedStatus
            FilterChip(
                modifier = Modifier.testTag(status.name),
                selected = selected,
                label = {
                    Text(text = stringResource(status.localizedNameForFilter()))
                },
                leadingIcon = if (selected) {
                    {
                        Icon(
                            imageVector = Icons.Filled.Done,
                            contentDescription = "Done icon",
                            modifier = Modifier.size(FilterChipDefaults.IconSize)
                        )
                    }
                } else {
                    null
                },
                shape = MaterialTheme.shapes.large,
                colors = FilterChipDefaults.filterChipColors(
                    selectedContainerColor = MaterialTheme.colorScheme.primary,
                    selectedLabelColor = MaterialTheme.colorScheme.onPrimary,
                    selectedLeadingIconColor = MaterialTheme.colorScheme.onPrimary
                ),
                onClick = { onStatusChanged(status) }
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun OrdersStatusFilterPreview() {
    AvitoTheme {
        var state by remember { mutableStateOf(AccountOrderStatus.INITIATED) }
        AccountOrdersStatusFilter(
            selectedStatus = state,
            onStatusChanged = { state = it }
        )
    }
}
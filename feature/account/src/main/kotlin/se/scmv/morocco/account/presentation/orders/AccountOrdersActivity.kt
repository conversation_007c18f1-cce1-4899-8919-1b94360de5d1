package se.scmv.morocco.account.presentation.orders

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.runtime.CompositionLocalProvider
import dagger.hilt.android.AndroidEntryPoint
import se.scmv.morocco.analytics.AnalyticsHelper
import se.scmv.morocco.analytics.LocalAnalyticsHelper
import se.scmv.morocco.designsystem.theme.AvitoTheme
import javax.inject.Inject

// TODO Remove this activity and add OrdersRoute composable to navigation, once all the app is in compose
@AndroidEntryPoint
class AccountOrdersActivity : ComponentActivity() {

    @Inject
    lateinit var analyticsHelper: AnalyticsHelper

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setContent {
            AvitoTheme {
                CompositionLocalProvider(LocalAnalyticsHelper provides analyticsHelper) {
                    AccountOrdersRoute (navigateBack = { onBackPressedDispatcher.onBackPressed() })
                }
            }
        }
    }
}
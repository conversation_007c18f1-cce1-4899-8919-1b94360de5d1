package se.scmv.morocco.account.presentation.statistics

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import se.scmv.morocco.domain.models.AccountStatisticsRange
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.repositories.AccountRepository
import se.scmv.morocco.ui.asUiText
import javax.inject.Inject

@HiltViewModel
class AccountStatisticsViewModel @Inject constructor(
    private val accountRepository: AccountRepository
) : ViewModel() {

    private val _viewState = MutableStateFlow(AccountStatisticsViewState())
    val viewState = _viewState.asStateFlow()

    init {
        getStatistics()
    }

    fun onRefresh() {
        getStatistics()
    }

    private fun getStatistics() {
        viewModelScope.launch {
            showLoading()
            val result = accountRepository.getStatistics(AccountStatisticsRange.LAST_30_DAYS)
            hideLoading()
            when (result) {
                is Resource.Success -> _viewState.update { state ->
                    state.copy(
                        metrics = result.data.toImmutableList(),
                        errorMessage = null
                    )
                }

                is Resource.Failure -> _viewState.update { state ->
                    state.copy(errorMessage = result.error.asUiText())
                }
            }
        }
    }

    private fun showLoading() {
        _viewState.update { it.copy(loading = true) }
    }

    private fun hideLoading() {
        _viewState.update { it.copy(loading = false) }
    }
}
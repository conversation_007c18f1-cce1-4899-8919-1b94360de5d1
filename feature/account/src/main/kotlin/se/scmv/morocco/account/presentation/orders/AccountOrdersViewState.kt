package se.scmv.morocco.account.presentation.orders

import androidx.compose.ui.graphics.Color
import se.scmv.morocco.designsystem.R
import se.scmv.morocco.domain.models.AccountOrderStatus

@JvmInline
value class AccountOrdersCancelOrderStatus(val isLoading: Boolean)

internal fun AccountOrderStatus.localizedNameForFilter(): Int = when (this) {
    AccountOrderStatus.INITIATED -> R.string.order_status_initiated
    AccountOrderStatus.PREPARING -> R.string.order_status_preparing
    AccountOrderStatus.DELIVERING -> R.string.order_status_delivering
    AccountOrderStatus.DELIVERED -> R.string.order_status_delivered
    AccountOrderStatus.CANCELLED -> R.string.order_status_canceled
}

internal fun AccountOrderStatus.localizedNameForCard(): Int = when (this) {
    AccountOrderStatus.INITIATED -> R.string.order_card_status_initiated
    AccountOrderStatus.PREPARING -> R.string.order_card_status_preparing
    AccountOrderStatus.DELIVERING -> R.string.order_card_status_delivering
    AccountOrderStatus.DELIVERED -> R.string.order_card_status_delivered
    AccountOrderStatus.CANCELLED -> R.string.order_card_status_canceled
}

internal fun AccountOrderStatus.bgColor(): Color = when (this) {
    AccountOrderStatus.INITIATED -> Color(0XFFFBEAD8)
    AccountOrderStatus.PREPARING -> Color(0XFFFBEAD8)
    AccountOrderStatus.DELIVERING -> Color(0XFFFBEAD8)
    AccountOrderStatus.DELIVERED -> Color(0XFFE1FEEC)
    AccountOrderStatus.CANCELLED -> Color(0XFFEEEEEE)
}

internal fun AccountOrderStatus.textColor(): Color = when (this) {
    AccountOrderStatus.INITIATED -> Color(0XFFFC942D)
    AccountOrderStatus.PREPARING -> Color(0XFFFC942D)
    AccountOrderStatus.DELIVERING -> Color(0XFFFC942D)
    AccountOrderStatus.DELIVERED -> Color(0XFF2EC966)
    AccountOrderStatus.CANCELLED -> Color(0XFF545454)
}
package se.scmv.morocco.account.presentation.bookmarks.ads

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.pulltorefresh.PullToRefreshBox
import androidx.compose.material3.pulltorefresh.rememberPullToRefreshState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.paging.PagingData
import androidx.paging.compose.LazyPagingItems
import androidx.paging.compose.collectAsLazyPagingItems
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.flowOf
import se.scmv.morocco.account.R
import se.scmv.morocco.account.presentation.bookmarks.ads.AccountBookmarkedAdsOneTimeEvents.DeleteAdSuccess
import se.scmv.morocco.account.presentation.bookmarks.ads.AccountBookmarkedAdsOneTimeEvents.ShowHideProgress
import se.scmv.morocco.analytics.TrackScreenViewEvent
import se.scmv.morocco.analytics.models.AnalyticsEvent
import se.scmv.morocco.designsystem.components.AvConfirmationAlertDialog
import se.scmv.morocco.designsystem.components.AvProgressBar
import se.scmv.morocco.designsystem.components.ChatHandler
import se.scmv.morocco.designsystem.components.ListingCard
import se.scmv.morocco.designsystem.components.ScreenEmptyState
import se.scmv.morocco.designsystem.components.ScreenErrorState
import se.scmv.morocco.designsystem.components.ShowWarningPhoneCallDialog
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.designsystem.utils.ContactActionUtils
import se.scmv.morocco.domain.models.Account
import se.scmv.morocco.domain.models.ContactMethod
import se.scmv.morocco.domain.models.ListingAd
import se.scmv.morocco.domain.models.createMockListingAds
import se.scmv.morocco.ui.getErrorAsUiText
import se.scmv.morocco.ui.isEmpty
import se.scmv.morocco.ui.isError
import se.scmv.morocco.ui.isLoading

@Composable
fun AccountBookmarkedAdsRoute(
    modifier: Modifier = Modifier,
    viewModel: AccountBookmarkedAdsViewModel = hiltViewModel(),
    navigateToListing: () -> Unit,
    onAdRemovedFromFavorites: (String) -> Unit,
    onAdClicked: (String) -> Unit,
    navigateToAuthentication: () -> Unit,
    account: Account,
    onSendMessage: (String) -> Unit,
) {
    val adPagingItems = viewModel.ads.collectAsLazyPagingItems()
    var adToUnBookmark by remember { mutableStateOf<ListingAd.Published?>(null) }

    AccountBookmarkedAdsScreen(
        modifier = modifier.fillMaxSize(),
        pagingItems = adPagingItems,
        onEmptyStateActionClicked = navigateToListing,
        onUnBookmarkAd = viewModel::onUnBookmarkAd,
        onAdClicked = onAdClicked,
        navigateToAuthentication = navigateToAuthentication,
        account = account,
        onSendMessage = { message ->
            onSendMessage(message)
        }
    )
    if (adToUnBookmark != null) {
        AvConfirmationAlertDialog(
            title = stringResource(R.string.bookmarked_ads_screen_delete_ad),
            description = stringResource(R.string.bookmarked_ads_screen_delete_ad_description),
            onConfirm = {
                adToUnBookmark?.let { ad ->
                    viewModel.onUnBookmarkAd(ad)
                    adToUnBookmark = null
                }
            },
            onCancel = { adToUnBookmark = null },
        )
    }
    var showProgressBar by remember { mutableStateOf(false) }
    if (showProgressBar) {
        AvProgressBar(text = stringResource(R.string.bookmarked_ads_screen_delete_ad_processing))
    }
    LaunchedEffect(key1 = Unit) {
        viewModel.oneTimeEvents.collectLatest {
            when (it) {
                is ShowHideProgress -> showProgressBar = it.isLoading
                is DeleteAdSuccess -> onAdRemovedFromFavorites(it.adId)
            }
        }
    }
    TrackScreenViewEvent(screenName = AnalyticsEvent.ScreensNames.BOOKMARKED_ADS)
}

@Composable
private fun AccountBookmarkedAdsScreen(
    modifier: Modifier = Modifier,
    pagingItems: LazyPagingItems<ListingAd.Published>,
    onEmptyStateActionClicked: () -> Unit,
    onUnBookmarkAd: (ListingAd.Published) -> Unit,
    onAdClicked: (String) -> Unit,
    navigateToAuthentication: () -> Unit,
    account: Account,
    onSendMessage: (String) -> Unit,
) {
    val context = LocalContext.current
    when {
        pagingItems.isEmpty() -> ScreenEmptyState(
            modifier = modifier,
            image = R.drawable.img_empty_bookmarked_ads,
            title = R.string.bookmarked_ads_screen_empty_state_title,
            description = R.string.bookmarked_ads_screen_empty_state_description,
            actionText = R.string.common_search,
            onActionClicked = onEmptyStateActionClicked
        )

        pagingItems.isError() -> ScreenErrorState(
            modifier = modifier,
            title = stringResource(R.string.common_oups),
            description = pagingItems.getErrorAsUiText().getValue(context),
            actionText = stringResource(R.string.common_refresh),
            onActionClicked = pagingItems::refresh
        )

        else -> AccountBookmarkedAdsList(
            modifier = modifier,
            pagingItems = pagingItems,
            onUnBookmarkAd = onUnBookmarkAd,
            onAdClicked = onAdClicked,
            navigateToAuthentication = navigateToAuthentication,
            account = account,
            onSendMessage = { message ->
                onSendMessage(message)
            }
        )
    }
}

@Preview
@Composable
private fun AccountBookmarkedSearchesScreenPreview() {
    AvitoTheme {
        Surface {
            // Uncomment this code to preview empty state
            // val ads = PagingData.emptyLazyPagingItems<ListingAd.Published>()
            val ads = flowOf(
                PagingData.from(createMockListingAds(20))
            ).collectAsLazyPagingItems()
            // Uncomment this code to preview error state
            //val ads = PagingData.errorLazyPagingItems<ListingAd.Published>()
            AccountBookmarkedAdsScreen(
                pagingItems = ads,
                onEmptyStateActionClicked = {},
                onUnBookmarkAd = {},
                onAdClicked = {},
                navigateToAuthentication = {},
                account = Account.NotConnected,
                onSendMessage = { _ -> }
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun AccountBookmarkedAdsList(
    modifier: Modifier = Modifier,
    pagingItems: LazyPagingItems<ListingAd.Published>,
    onUnBookmarkAd: (ListingAd.Published) -> Unit,
    onAdClicked: (String) -> Unit,
    navigateToAuthentication: () -> Unit,
    account: Account,
    onSendMessage: (String) -> Unit,
) {
    val context = LocalContext.current
    var showPhoneDialog by remember { mutableStateOf(false) }
    var phoneNumberToCall by remember { mutableStateOf("") }
    var vendorNameToCall by remember { mutableStateOf("") }
    var currentChatAdId by remember { mutableStateOf<String?>(null) }
    var currentChatAdDetails by remember { mutableStateOf<ListingAd.Published?>(null) }

    PullToRefreshBox(
        modifier = modifier,
        isRefreshing = pagingItems.isLoading(),
        state = rememberPullToRefreshState(),
        onRefresh = { pagingItems.refresh() }
    ) {
        LazyColumn(
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.large),
            contentPadding = PaddingValues(bottom = 120.dp)
        ) {
            items(
                count = pagingItems.itemCount,
            ) {
                pagingItems[it]?.let { ad ->
                    ListingCard(
                        listingAd = ad,
                        onFavoriteClick = { onUnBookmarkAd(ad) },
                        onAdClick = onAdClicked,
                        showCTAButtons = false,
                        onPriceInquiry = { contactMethod ->
                            when(contactMethod){
                                is ContactMethod.Chat -> {
                                    ContactActionUtils.handleChatCTA(context, contactMethod.adId) { adId ->
                                        currentChatAdId = adId
                                        currentChatAdDetails = ad
                                    }
                                }
                                is ContactMethod.WhatsApp -> {
                                    ContactActionUtils.handleWhatsAppCTA(context, contactMethod.phoneNumber)
                                }
                                is ContactMethod.PhoneCall -> {
                                    phoneNumberToCall = contactMethod.phoneNumber
                                    vendorNameToCall = ad.sellerName ?: "Vendeur"
                                    showPhoneDialog = true
                                }
                            }
                        }
                    )
                }
            }
        }
    }

    // Chat handler for FirstMessageBottomSheet
    ChatHandler(
        account = account,
        adId = currentChatAdId,
        adDetails = currentChatAdDetails,
        onNavigateToAuthentication = navigateToAuthentication,
        onSendMessage = { message ->
            onSendMessage(message)
        },
        onDismiss = {
            currentChatAdId = null
            currentChatAdDetails = null
        }
    )

    // Phone call confirmation dialog
    if (showPhoneDialog) {
        ShowWarningPhoneCallDialog(
            vendorName = vendorNameToCall,
            phoneNumber = phoneNumberToCall,
            onDismissRequest = { showPhoneDialog = false },
            onCallClick = {
                showPhoneDialog = false
                ContactActionUtils.launchPhoneCall(context, phoneNumberToCall)
            }
        )
    }
}
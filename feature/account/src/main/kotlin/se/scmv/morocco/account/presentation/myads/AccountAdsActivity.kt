package se.scmv.morocco.account.presentation.myads

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.domain.models.Account
import se.scmv.morocco.domain.models.AllowedAccess
import se.scmv.morocco.domain.repositories.AccountRepository
import javax.inject.Inject


@AndroidEntryPoint
class AccountAdsActivity : ComponentActivity() {

    @Inject
    lateinit var accountRepository: AccountRepository

    //    viewModel: AccountAdsViewModel = hiltViewModel(),
    private val viewModel: AccountAdsViewModel by viewModels()

    private var allowedAccess: AllowedAccess = AllowedAccess.default


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Setup lifecycle observer to collect the current account
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.CREATED) {
                accountRepository.currentAccount.collect { account: Account ->
                    allowedAccess = getAllowedAccess(account)
                    if (account is Account.Connected) {
                        updateUI(account)  // Call updateUI with the collected account
                    }
                }
            }
        }
    }

    // Method to manage UI updates
    private fun updateUI(account: Account.Connected) {
        setContent {
            AvitoTheme {
                AdsRoute(
                    navigateBack = { onBackPressedDispatcher.onBackPressed() },
                    viewModel = viewModel,
                    navigateToNewInsert = { adId, adCategoryKey, adType, toImageStep ->},
                    navigateToVasActivity = { adId, adCategoryKey, adType, application ->
                    },
                    navigateToAdView = { adListId, imageUrl, title, date, imageCount, videoCount, videoUrl, isStore, price, oldPrice, location, category, categoryIcon, isUrgent, isHotDeal, discount ->

                    },
                    account = account
                )
            }
        }
    }

    // Helper method to extract AllowedAccess based on account type
    private fun getAllowedAccess(account: Account): AllowedAccess {
        return when (account) {
            is Account.Connected.Shop -> account.store.allowedAccess
            else -> AllowedAccess.default // Default for private or not connected
        }
    }

    override fun onResume() {
        super.onResume()
        viewModel.refresh()
    }
}
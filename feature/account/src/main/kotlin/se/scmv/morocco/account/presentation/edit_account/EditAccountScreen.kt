package se.scmv.morocco.account.presentation.edit_account

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Card
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.datasource.LoremIpsum
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.flow.collectLatest
import se.scmv.morocco.account.R
import se.scmv.morocco.account.presentation.edit_account.EditAccountOneTimeEvents.LogoutSuccess
import se.scmv.morocco.account.presentation.edit_account.EditAccountOneTimeEvents.OpenLogin
import se.scmv.morocco.account.presentation.edit_account.EditAccountOneTimeEvents.ShowHideProgress
import se.scmv.morocco.account.presentation.edit_account.EditAccountViewState.Nothing
import se.scmv.morocco.account.presentation.edit_account.EditAccountViewState.Private
import se.scmv.morocco.account.presentation.edit_account.EditAccountViewState.Shop
import se.scmv.morocco.analytics.TrackScreenViewEvent
import se.scmv.morocco.analytics.models.AnalyticsEvent
import se.scmv.morocco.analytics.models.Param
import se.scmv.morocco.designsystem.components.AvAlert
import se.scmv.morocco.designsystem.components.AvAlertType
import se.scmv.morocco.designsystem.components.AvConfirmationAlertDialog
import se.scmv.morocco.designsystem.components.AvDropdown
import se.scmv.morocco.designsystem.components.AvPrimaryButton
import se.scmv.morocco.designsystem.components.AvProgressBar
import se.scmv.morocco.designsystem.components.AvSecondaryButton
import se.scmv.morocco.designsystem.components.AvSwitch
import se.scmv.morocco.designsystem.components.AvTextField
import se.scmv.morocco.designsystem.components.AvTopAppBar
import se.scmv.morocco.designsystem.components.DropdownData
import se.scmv.morocco.designsystem.components.RedirectionItem
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.domain.models.Account


@Composable
fun EditAccountRoute(
    account: Account.Connected,
    navigateBack: () -> Unit,
    navigateToUpdatePassword: (String) -> Unit,
    navigateToAuthentication: () -> Unit,
    modifier: Modifier = Modifier,
    viewModel: EditAccountViewModel = hiltViewModel(),
) {
    val state = viewModel.viewState.collectAsStateWithLifecycle().value
    var showLogoutConfirmationAlert by remember { mutableStateOf(false) }
    Scaffold(
        modifier = modifier,
        topBar = {
            AvTopAppBar(
                modifier = Modifier.shadow(1.dp),
                onNavigationIconClicked = navigateBack,
                titleRes = when (state) {
                    is Private -> R.string.edit_private_account_screen_title
                    is Shop -> R.string.edit_shop_account_screen_title
                    else -> null
                }
            )
        }
    ) {
        Column(
            modifier = Modifier
                .padding(it)
                .verticalScroll(rememberScrollState())
                .padding(top = MaterialTheme.dimens.big)
                .padding(bottom = MaterialTheme.dimens.big)
                .padding(horizontal = MaterialTheme.dimens.screenPaddingHorizontal),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.big),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            when (state) {
                is Private -> EditPrivateAccountScreen(
                    modifier = Modifier,
                    state = state,
                    onNameChanged = viewModel::onNameChanged,
                    onEmailChanged = viewModel::onEmailChanged,
                    onPhoneNumberChanged = viewModel::onPhoneNumberChanged,
                    onPhoneVisibilityChanged = viewModel::onPhoneVisibilityChanged,
                    onCityChanged = viewModel::onCityChanged,
                    onOpenPasswordChangeClicked = { navigateToUpdatePassword(state.email) },
                    onLogoutClicked = {
                        showLogoutConfirmationAlert = true
                    }
                )

                is Shop -> EditShopAccountScreen(
                    state = state,
                    onNameChanged = viewModel::onNameChanged,
                    onWebsiteChanged = viewModel::onWebsiteChanged,
                    onShortDescriptionChanged = viewModel::onShortDescriptionChanged,
                    onLongDescriptionChanged = viewModel::onLongDescriptionChanged,
                    onAddressChanged = viewModel::onAddressChanged
                )

                Nothing -> Unit
            }
            if (state !is Nothing) {
                Row(
                    horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium)
                ) {
                    AvSecondaryButton(
                        modifier = Modifier.weight(1f),
                        text = stringResource(R.string.common_cancel),
                        onClick = navigateBack,
                    )
                    AvPrimaryButton(
                        modifier = Modifier.weight(1f),
                        text = stringResource(R.string.common_validate),
                        onClick = viewModel::onSubmit,
                        loading = (state as? Private)?.loading == true || (state as? Shop)?.loading == true
                    )
                }
            }
        }
    }
    if (showLogoutConfirmationAlert) {
        AvConfirmationAlertDialog(
            title = stringResource(R.string.account_master_screen_log_out_alert_title),
            description = stringResource(R.string.account_master_screen_log_out_alert_description),
            onConfirm = {
                viewModel.onLogout()
                showLogoutConfirmationAlert = false
            },
            onCancel = { showLogoutConfirmationAlert = false },
        )
    }
    var showProgressBar by remember { mutableStateOf(false) }
    if (showProgressBar) {
        AvProgressBar(text = stringResource(R.string.common_logout_processing))
    }
    LaunchedEffect(Unit) {
        viewModel.oneTimeEvents.collectLatest {
            when (it) {
                is ShowHideProgress -> {
                    showProgressBar = it.isLoading
                }

                LogoutSuccess -> navigateBack()
                OpenLogin -> navigateToAuthentication()
            }
        }
    }
    TrackScreenViewEvent(
        screenName = AnalyticsEvent.ScreensNames.ACCOUNT_EDIT,
        properties = setOf(
            Param(
                key = AnalyticsEvent.ParamKeys.ACCOUNT_TYPE,
                value = account.analyticsAccountType()
            )
        )
    )
}

@Composable
private fun EditPrivateAccountScreen(
    modifier: Modifier = Modifier,
    state: Private,
    onNameChanged: (String) -> Unit,
    onEmailChanged: (String) -> Unit,
    onPhoneNumberChanged: (String) -> Unit,
    onPhoneVisibilityChanged: (Boolean) -> Unit,
    onCityChanged: (DropdownData) -> Unit,
    onOpenPasswordChangeClicked: () -> Unit,
    onLogoutClicked: () -> Unit,
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.default)
    ) {
        AvTextField(
            modifier = Modifier.fillMaxWidth(),
            value = state.name,
            onValueChanged = onNameChanged,
            required = true,
            title = R.string.common_full_name_field_label,
            placeholder = R.string.common_full_name_field_placeholder,
            error = state.nameError?.getValue(LocalContext.current),
            keyboardOptions = KeyboardOptions(imeAction = ImeAction.Next)
        )
        AvTextField(
            modifier = Modifier.fillMaxWidth(),
            value = state.email,
            onValueChanged = onEmailChanged,
            enabled = state.emailEditEnabled,
            required = state.emailEditEnabled,
            title = R.string.common_email_field_label,
            placeholder = R.string.common_email_field_placeholder,
            error = state.emailError?.getValue(LocalContext.current)
        )
        AvTextField(
            modifier = Modifier.fillMaxWidth(),
            value = state.phone,
            onValueChanged = onPhoneNumberChanged,
            required = true,
            title = R.string.common_phone_number_field_label,
            placeholder = R.string.common_phone_number_field_placeholder,
            error = state.phoneError?.getValue(LocalContext.current),
            keyboardOptions = KeyboardOptions(imeAction = ImeAction.Next)
        )
        AvSwitch(
            checked = state.phoneVisibility,
            title = R.string.edit_private_account_screen_hide_phone,
            onCheckChanged = onPhoneVisibilityChanged
        )
        AvDropdown(
            modifier = Modifier.fillMaxWidth(),
            items = state.cities,
            selectedItem = state.selectedCity,
            title = stringResource(R.string.common_city_field_label),
            required = true,
            error = state.cityError?.getValue(LocalContext.current),
            onItemSelected = onCityChanged,
        )
        Card {
            RedirectionItem(
                modifier = Modifier.clip(MaterialTheme.shapes.small),
                title = R.string.account_master_screen_redirection_password_update,
                icon = R.drawable.ic_change_password,
                iconTint = null,
                onClick = onOpenPasswordChangeClicked
            )
        }
        Card {
            RedirectionItem(
                modifier = Modifier.clip(MaterialTheme.shapes.small),
                title = R.string.account_master_screen_redirection_log_out,
                icon = R.drawable.ic_logout,
                iconTint = null,
                onClick = onLogoutClicked
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun EditPrivateAccountScreenPreview() {
    AvitoTheme {
        EditPrivateAccountScreen(
            state = Private(
                name = "Jean Nash",
                email = "<EMAIL>",
                emailEditEnabled = true,
                phone = "(*************",
                phoneVisibility = false,
                cities = listOf(
                    DropdownData(
                        id = "aeque",
                        name = "Christina Griffin"
                    ),
                    DropdownData(
                        id = "aequ",
                        name = "Christina Griffin"
                    ),
                ).toImmutableList(),
                selectedCity = DropdownData(
                    id = "aeque",
                    name = "Christina Griffin"
                ),
                loading = false
            ),
            onNameChanged = {},
            onEmailChanged = {},
            onPhoneNumberChanged = {},
            onPhoneVisibilityChanged = {},
            onCityChanged = {},
            onOpenPasswordChangeClicked = {},
            onLogoutClicked = {}
        )
    }
}

@Composable
private fun EditShopAccountScreen(
    modifier: Modifier = Modifier,
    state: Shop,
    onNameChanged: (String) -> Unit,
    onWebsiteChanged: (String) -> Unit,
    onShortDescriptionChanged: (String) -> Unit,
    onLongDescriptionChanged: (String) -> Unit,
    onAddressChanged: (String) -> Unit,
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.default)
    ) {
        AvTextField(
            modifier = Modifier.fillMaxWidth(),
            value = state.name,
            onValueChanged = onNameChanged,
            required = true,
            title = R.string.edit_shop_account_screen_name_label,
            placeholder = R.string.edit_shop_account_screen_name_label,
            error = state.nameError?.getValue(LocalContext.current),
            keyboardOptions = KeyboardOptions(imeAction = ImeAction.Next)
        )
        AvTextField(
            modifier = Modifier.fillMaxWidth(),
            value = state.website,
            onValueChanged = onWebsiteChanged,
            title = R.string.edit_shop_account_screen_website_label,
            placeholder = R.string.edit_shop_account_screen_website_label,
            keyboardOptions = KeyboardOptions(imeAction = ImeAction.Next)
        )
        Column(
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.small)
        ) {
            AvTextField(
                modifier = Modifier.fillMaxWidth(),
                value = state.email,
                onValueChanged = onNameChanged,
                enabled = false,
                title = R.string.common_email,
                placeholder = R.string.common_email
            )
            AvAlert(
                text = stringResource(R.string.edit_shop_account_screen_email_warning),
                type = AvAlertType.Info
            )
        }
        AvTextField(
            modifier = Modifier.fillMaxWidth(),
            value = state.shortDescription,
            onValueChanged = onShortDescriptionChanged,
            required = true,
            title = R.string.edit_shop_account_screen_short_description_label,
            placeholder = R.string.edit_shop_account_screen_short_description_label,
            error = state.shortDescriptionError?.getValue(LocalContext.current),
            keyboardOptions = KeyboardOptions(imeAction = ImeAction.Next)
        )
        AvTextField(
            modifier = Modifier.fillMaxWidth(),
            value = state.longDescription,
            onValueChanged = onLongDescriptionChanged,
            title = R.string.edit_shop_account_screen_long_description_label,
            placeholder = R.string.edit_shop_account_screen_long_description_label,
            maxLines = 10,
            minLines = 3,
            keyboardOptions = KeyboardOptions(imeAction = ImeAction.Next)
        )
        AvAlert(
            text = stringResource(R.string.edit_shop_account_screen_phone_warning),
            type = AvAlertType.Info
        )
        state.phones.forEachIndexed { index, phone ->
            AvTextField(
                modifier = Modifier.fillMaxWidth(),
                value = phone,
                onValueChanged = { },
                enabled = false,
                title = R.string.edit_shop_account_screen_phone_label,
                titleArgs = persistentListOf(index + 1),
                placeholder = R.string.common_phone
            )
        }
        AvTextField(
            modifier = Modifier.fillMaxWidth(),
            value = state.address,
            onValueChanged = onAddressChanged,
            title = R.string.common_address,
            placeholder = R.string.common_address,
            keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done)
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun EditShopAccountScreenPreview(
    @PreviewParameter(LoremIpsum::class) loremIpsum: String
) {
    AvitoTheme {
        EditShopAccountScreen(
            modifier = Modifier.fillMaxSize().verticalScroll(rememberScrollState()),
            state = Shop(
                accountId = "fakeId",
                name = "Marguerite Browning",
                website = "https://www.avito.com",
                email = "<EMAIL>",
                shortDescription = "repudiare",
                longDescription = loremIpsum,
                phones = persistentListOf("(*************", "(*************", "(*************"),
                address = "fabellas, Morocco",
                loading = false
            ),
            onNameChanged = {},
            onWebsiteChanged = {},
            onShortDescriptionChanged = {},
            onLongDescriptionChanged = {},
            onAddressChanged = {},
        )
    }
}


package se.scmv.morocco.account.presentation.statistics

import androidx.compose.runtime.Stable
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.domain.models.AccountStatisticsMetric

@Stable
data class AccountStatisticsViewState(
    val metrics: ImmutableList<AccountStatisticsMetric> = persistentListOf(),
    val loading: Boolean = true,
    val errorMessage: UiText? = null
)

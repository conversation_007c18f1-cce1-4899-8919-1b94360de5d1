package se.scmv.morocco.account.presentation.edit_account

import androidx.compose.runtime.Stable
import kotlinx.collections.immutable.ImmutableList
import se.scmv.morocco.designsystem.components.DropdownData
import se.scmv.morocco.designsystem.utils.UiText

@Stable
sealed interface EditAccountViewState {
    data object Nothing : EditAccountViewState
    @Stable
    data class Private(
        val name: String,
        val email: String,
        val phone: String,
        val phoneVisibility: Boolean,
        val selectedCity: DropdownData?,
        val nameError: UiText? = null,
        val emailError: UiText? = null,
        val phoneError: UiText? = null,
        val cityError: UiText? = null,
        val emailEditEnabled: Boolean = false,
        val cities: ImmutableList<DropdownData>,
        val loading: Boolean
    ) : EditAccountViewState

    @Stable
    data class Shop(
        val accountId: String,
        val name: String,
        val website: String,
        val shortDescription: String,
        val longDescription: String,
        val address: String,
        val phones: ImmutableList<String>,
        val email: String,
        val nameError: UiText? = null,
        val shortDescriptionError: UiText? = null,
        val loading: Boolean
    ) : EditAccountViewState
}

sealed interface EditAccountOneTimeEvents {
    data object LogoutSuccess : EditAccountOneTimeEvents
    data object OpenLogin: EditAccountOneTimeEvents
    @JvmInline
    value class ShowHideProgress(val isLoading: Boolean) : EditAccountOneTimeEvents
}



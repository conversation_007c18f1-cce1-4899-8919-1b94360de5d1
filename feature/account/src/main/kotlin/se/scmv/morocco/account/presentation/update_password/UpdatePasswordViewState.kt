package se.scmv.morocco.account.presentation.update_password

import se.scmv.morocco.designsystem.utils.UiText

data class UpdatePasswordViewState(
    val oldPassword: String = "",
    val oldPasswordError: UiText? = null,
    val newPassword: String = "",
    val newPasswordError: UiText? = null,
    val loading: Boolean = false
)

sealed interface UpdatePasswordOneTimeEvents {
    data object DismissDialog: UpdatePasswordOneTimeEvents
    data class ShowSnackBar(
        val message: UiText,
        val isSuccess: Boolean
    ): UpdatePasswordOneTimeEvents
    data object NavigateToLogin: UpdatePasswordOneTimeEvents
}

package se.scmv.morocco.account.presentation.bookmarks.master

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import kotlinx.coroutines.launch
import se.scmv.morocco.account.presentation.bookmarks.ads.AccountBookmarkedAdsRoute
import se.scmv.morocco.account.presentation.bookmarks.searches.AccountBookmarkedSearchesRoute
import se.scmv.morocco.designsystem.components.AvTabs
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.domain.models.Account
import se.scmv.morocco.domain.models.BookmarkedSearch

@Composable
fun AccountBookmarksRoute(
    tab: AccountBookmarksPages?,
    navigateToListing: () -> Unit,
    applySearch: (BookmarkedSearch) -> Unit,
    notifyAdRemovedFromFavorites: (String) -> Unit,
    navigateToAdView: (String) -> Unit,
    navigateToAuthentication: () -> Unit,
    account: Account,
    onSendMessage: (String) -> Unit,
    modifier: Modifier = Modifier,
) {
    val pagerState = rememberPagerState(
        initialPage = tab?.ordinal ?: 0,
        pageCount = { AccountBookmarksPages.entries.size }
    )
    val coroutineScope = rememberCoroutineScope()
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(top = MaterialTheme.dimens.medium)
    ) {
        AvTabs(
            modifier = Modifier.fillMaxWidth(),
            tabs = accountBookmarksTabs,
            selectedIndex = pagerState.currentPage,
        ) { tabIndex ->
            coroutineScope.launch { pagerState.animateScrollToPage(tabIndex) }
        }
        HorizontalPager(
            modifier = Modifier
                .padding(top = MaterialTheme.dimens.medium)
                .fillMaxSize(),
            state = pagerState
        ) {
            when (it) {
                AccountBookmarksPages.ADS.ordinal -> AccountBookmarkedAdsRoute(
                    navigateToListing = navigateToListing,
                    onAdRemovedFromFavorites = notifyAdRemovedFromFavorites,
                    onAdClicked = navigateToAdView,
                    navigateToAuthentication = navigateToAuthentication,
                    account = account,
                    onSendMessage = onSendMessage
                )

                AccountBookmarksPages.SEARCHES.ordinal -> AccountBookmarkedSearchesRoute(
                    navigateToListing = navigateToListing,
                    applySearch = applySearch
                )
            }
        }
    }
}
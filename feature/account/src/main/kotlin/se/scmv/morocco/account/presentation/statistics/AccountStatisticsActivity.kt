package se.scmv.morocco.account.presentation.statistics

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.consumeWindowInsets
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.unit.dp
import dagger.hilt.android.AndroidEntryPoint
import se.scmv.morocco.account.R
import se.scmv.morocco.analytics.AnalyticsHelper
import se.scmv.morocco.analytics.LocalAnalyticsHelper
import se.scmv.morocco.designsystem.components.AvTopAppBar
import se.scmv.morocco.designsystem.theme.AvitoTheme
import javax.inject.Inject

// TODO Remove this activity and add AccountStatisticsRoute composable to navigation, once all the app is in compose
@AndroidEntryPoint
class AccountStatisticsActivity : ComponentActivity() {

    @Inject
    lateinit var analyticsHelper: AnalyticsHelper

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setContent {
            AvitoTheme {
                CompositionLocalProvider(LocalAnalyticsHelper provides analyticsHelper) {
                    Scaffold(
                        topBar = {
                            AvTopAppBar(
                                modifier = Modifier.shadow(1.dp),
                                onNavigationIconClicked = {
                                    onBackPressedDispatcher.onBackPressed()
                                },
                                titleRes = R.string.account_master_screen_redirection_my_stats,
                            )
                        },
                    ) {
                        AccountStatisticsRoute(modifier = Modifier.consumeWindowInsets(it))
                    }
                }
            }
        }
    }
}
package se.scmv.morocco.account.presentation.bookmarks.master

import androidx.annotation.Keep
import kotlinx.collections.immutable.toImmutableList
import kotlinx.serialization.Serializable
import se.scmv.morocco.account.R
import se.scmv.morocco.designsystem.components.TabData

internal val accountBookmarksTabs = listOf(
    TabData(
        text = R.string.bookmarked_ads_screen_title,
        icon = R.drawable.ic_heart
    ),
    TabData(
        text = R.string.bookmarked_searches_screen_title,
        icon = R.drawable.ic_star
    ),
).toImmutableList()

@Serializable
@Keep
enum class AccountBookmarksPages {
    ADS, SEARCHES
}
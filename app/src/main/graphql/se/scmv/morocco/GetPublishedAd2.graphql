query GetPublishedAd2($listID: String!){
    getPublishedAd(
        query: {listId: $listID}
    ) {
        ad {
            adId
            listId
            listTime
            sellerType
            title
            description
            reservedDays
            category {
                id
                name
                trackingValue
                parent {
                    id
                    name
                    trackingValue
                    parent {
                        id
                        name
                        trackingValue
                    }
                }
            }
            type {
                name
                key
            }
            ...PublishedAdPrice
            discount
            media {
                mediaCount
                defaultImage {
                    paths {
                        smallThumbnail
                        largeThumbnail
                        standard
                        fullHd
                    }
                }
                media {
                    images {
                        paths {
                            smallThumbnail
                            largeThumbnail
                            standard
                            fullHd
                        }
                    }
                    videos {
                        defaultPath
                    }
                }
            }
            location {
                city {
                    id
                    name
                    trackingValue
                }
                area {
                    id
                    name
                    trackingValue
                }
            }
            seller {
                __typename
                ... on PrivateProfile {
                    accountId
                    name
                    registrationDay
                    phone{
                        number
                        verified
                    }
                }
                ... on StoreProfile {
                    storeId
                    name
                    registrationDay
                    isVerifiedSeller
                    location{
                        address
                    }
                    phone{
                        number
                        verified
                    }
                    logo {
                        defaultPath
                    }
                    website
                    type
                    numberOfActiveAds
                }
            }
            ...PublishedAdParam
            isHighlighted
            isInMyFavorites
            offersShipping
            isEcommerce
            isUrgent
            isHotDeal
        }
        similarAds{
            adId
            listId
            title
            listTime
            isInMyFavorites
            price{
                withCurrency
                withoutCurrency
            }
            category{
                id
            }
            location{
                city{
                    name
                }
            }
            media{
                defaultImage{
                    paths{
                        largeThumbnail
                        smallThumbnail
                    }
                }
                mediaCount
            }
            ...PublishedAdParam
        }
    }
}
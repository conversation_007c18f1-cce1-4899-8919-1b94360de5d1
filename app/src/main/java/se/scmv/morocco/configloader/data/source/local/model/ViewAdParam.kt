package se.scmv.morocco.configloader.data.source.local.model

import io.realm.RealmList
import io.realm.RealmObject
import io.realm.annotations.Ignore
import io.realm.annotations.PrimaryKey
import se.scmv.morocco.common.lang.LocaleManager

open class ViewAdParamRecord(
        var id: String? = null,
        var label: LabelRecord? = null,
        @PrimaryKey var name: String? = null,
        var type: String? = null,
        var validation: ValidationRecord? = null,
        var values: RealmList<ValueRecord> = RealmList(),
        var suffix: LabelRecord? = null
) : RealmObject()

open class LabelRecord(
        var fr: String? = null,
        var ar: String? = null,
        @PrimaryKey var uniqueKey: String? = null
) : RealmObject() {
        @Ignore
        var label: String? = null
                get() {
                        return if (LocaleManager.isAr() && !ar.isNullOrEmpty()) {
                                ar
                        } else fr
                }
}

open class ValidationRecord(
        var label: LabelRecord? = null,
        var optional: Boolean? = null,
        var regex: String? = null,
        @PrimaryKey var uniqueKey: String? = null
) : RealmObject()

open class ValueRecord(
        var key: String? = null,
        var label: LabelRecord? = null,
        var name: String? = null,
        var child: ValueRecord? = null,
        var iconPath: String? = null,
        var values: RealmList<ValueRecord> = RealmList()
) : RealmObject()


open class ViewAdParam(
        var category: String? = null,
        var adType: String? = null,
        var primaryParams: RealmList<ViewAdParamRecord> = RealmList(),
        var secondaryParams: RealmList<ViewAdParamRecord> = RealmList(),
        var extraParams: RealmList<ViewAdParamRecord> = RealmList(),
        @PrimaryKey var uniqueKey: String? = null
) : RealmObject()


open class AdInsertionAdParam(
        var category: String? = null,
        var adType: String? = null,
        var firstStep: RealmList<ViewAdParamRecord> = RealmList(),
        var secondStep: RealmList<ViewAdParamRecord> = RealmList(),
        var thirdStep: RealmList<ViewAdParamRecord> = RealmList(),
        var fourthStep: RealmList<ViewAdParamRecord> = RealmList(),
        @PrimaryKey var uniqueKey: String? = null
) : RealmObject()

package se.scmv.morocco.configloader.data.source.local.model

import io.realm.RealmList
import io.realm.RealmObject
import io.realm.annotations.PrimaryKey


open class SearchAdParamsRecord(
        @PrimaryKey var key: String? = null,
        var values: RealmList<SearchValueRecord> = RealmList()
) : RealmObject()

open class SearchValueRecord(
        var key: String? = null,
        var label: LabelRecord? = null,
        @PrimaryKey var uniqueKey: String? = null
) : RealmObject()

open class SearchFieldRecord(
        var key: String? = null,
        var childkey: String? = null,
        @PrimaryKey var id: String? = null,
        var label: LabelRecord? = null,
        var list: SearchAdParamsRecord? = null,
        var listKey: String? = null,
        var type: String? = null,
        var autoComplete: Boolean = false,
        var supportMultipleSelection: Boolean = false,
        var deepLinks: RealmList<DeepLinkRecord>? = null
) : RealmObject()

open class SearchAdParam(
        var category: String? = null,
        var adType: String? = null,
        var searchFields: RealmList<OrderedSearchField> = RealmList(),
        @PrimaryKey var uniqueKey: String? = null
) : RealmObject()


open class SearchField(
        @PrimaryKey var id: String? = null,
        var key: String? = null,
        var keyStart: String? = null,
        var keyEnd: String? = null,
        var type: String? = null,
        var label: LabelRecord? = null,
        var suffix: LabelRecord? = null,
        var interval: Interval? = null,
        var noLimit: Boolean? = null,
        var input: Boolean? = null,
        var noLimitLabel: LabelRecord? = null,
        var steps: RealmList<Step> = RealmList(),
        var mappings: RealmList<Mapping> = RealmList(),
        var supportsMapping: Boolean = false,
        var fields: RealmList<SearchFieldOrderedRecord> = RealmList(),
        var deepLinks: RealmList<DeepLinkRecord>? = null,
        var icon: String? = null
) : RealmObject()

open class OrderedSearchField(
        var order: Int? = null,
        var searchField: SearchField? = null,
        @PrimaryKey var uniqueKey: String? = null
) : RealmObject()

open class SearchFieldOrderedRecord(
        var order: Int? = null,
        var searchFieldRecord: SearchFieldRecord? = null,
        @PrimaryKey var uniqueKey: String? = null
) : RealmObject()

open class Interval(
        var min: String? = null,
        var max: String? = null,
        @PrimaryKey var uniqueKey: String? = null
) : RealmObject()

open class Mapping(
        var key: String? = null,
        var value: String? = null,
        @PrimaryKey var uniqueKey: String? = null
) : RealmObject()

open class Step(
        var threshold: String? = null,
        var step: String? = null,
        @PrimaryKey var uniqueKey: String? = null
) : RealmObject()

open class DeepLinkRecord(
        var key: String? = null,
        var type: String? = null,
        var min: String? = null,
        var max: String? = null,
        var childKey: String? = null,
        @PrimaryKey var uniqueKey: String? = null
) : RealmObject()
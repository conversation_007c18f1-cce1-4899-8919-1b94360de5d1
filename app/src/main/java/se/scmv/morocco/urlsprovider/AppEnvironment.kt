package se.scmv.morocco.urlsprovider

sealed class AppEnvironment(
        val label: String,
        val apiBaseUrl: String,
        val configApiBaseUrl: String,
        val webBaseUrl: String,
        val thumbBaseUrl: String,
        val fullImageBaseUrl: String,
        val mauHostUrl: String,
        val deliveryBaseUrl: String,
        val paymentAPIBaseUrl: String,
        val vasAPIBaseUrl: String,
        val vasGalleryBaseUrl: String,
        val monetizationImgBaseURL: String,
        val legacyMonetizationImgBaseURL: String,
        val phoneVerificationBaseUrl: String,
        val mediaConfigBaseUrl: String,
        val mediaAndroidConfigBaseUrl: String,
        val payZoneBaseUrl: String,
        val searchBaseUrl: String,
        val authHostBaseUrl: String,
        val mcAdsHostBaseUrl: String,
        val mcHostBaseUrl: String,
        val adViewAdParamsIconsUrl: String,
        val newAuthUrl: String,
        val adServerUrl: String,
        val graphQlEndpointUrl: String,
        val graphQlWebSocketUrl: String,
        val newAdInsertUrl: String,
        val newListingFilterUrl: String) {
        object Pro : AppEnvironment(
                label = "pro",
                apiBaseUrl = "https://services.avito.ma/api/v1",
                configApiBaseUrl = "https://services.avito.ma/api/v1",
                webBaseUrl = "https://www.avito.ma",
                thumbBaseUrl = "https://content.avito.ma/images/",
                fullImageBaseUrl = "https://content.avito.ma/images/",
                mauHostUrl = "https://profile.avito.ma/api/v1/user/",
                deliveryBaseUrl = "https://www.avito.ma/delivery",
                paymentAPIBaseUrl = "https://www2.avito.ma/api/v1",
                vasAPIBaseUrl = "https://www2.avito.ma/",
                vasGalleryBaseUrl = "https://gallery.avito.ma/api/v1/gallery/",
                monetizationImgBaseURL = "https://assets.avito.ma/android/",
                legacyMonetizationImgBaseURL = "https://www2.avito.ma/",
                phoneVerificationBaseUrl = "https://verify.avito.ma",
                mediaConfigBaseUrl = "https://avitomedia.com/",
                mediaAndroidConfigBaseUrl = "https://avitomedia.com/android_media",
                payZoneBaseUrl = "https://paiement.payzone.ma/transaction/",
                searchBaseUrl = "https://api.avito.ma/v1/",
                authHostBaseUrl = "https://oauth.avito.ma/api/v1/",
                mcAdsHostBaseUrl = "https://mcproxy.avito.ma/api/messaging/",
                mcHostBaseUrl = "https://mcproxy.avito.ma/proxy/",
                adViewAdParamsIconsUrl = "https://assets.avito.ma/android/ad_params/",
                newAuthUrl = "https://services.avito.ma/api/v2",
                adServerUrl = "https://media-server.avito.ma/",
                graphQlEndpointUrl = "https://gateway.avito.ma/graphql",
                graphQlWebSocketUrl = "wss://gateway.avito.ma/websocket",
                newAdInsertUrl = "https://services.avito.ma/api/v2",
                newListingFilterUrl = "https://services.avito.ma/api/v2/config/adlisting/"
        )

        object Pre : AppEnvironment(
                label = "pre",
                apiBaseUrl = "https://services-pre.avito.ma/api/v1",
                configApiBaseUrl = "https://services-pre.avito.ma/api/v1",
                webBaseUrl = "https://www-pre.avito.ma",
                thumbBaseUrl = "https://content-pre.avito.ma/images/",
                fullImageBaseUrl = "https://content-pre.avito.ma/mob_images/",
                mauHostUrl = "https://userprofile-pre.avito.ma/api/v1/user/",
                deliveryBaseUrl = "https://www-pre.avito.ma/delivery",
                paymentAPIBaseUrl = "https://www2-pre.avito.ma/api/v1",
                vasAPIBaseUrl = "https://www2-pre.avito.ma/",
                vasGalleryBaseUrl = "https://gallery-pre.avito.ma/api/v1/gallery/",
                monetizationImgBaseURL = "https://assets.avito.ma/android/",
                legacyMonetizationImgBaseURL = "https://www2-pre.avito.ma/",
                phoneVerificationBaseUrl = "https://verify-pre.avito.ma",
                mediaConfigBaseUrl = "https://avitomedia.com/",
                mediaAndroidConfigBaseUrl = "https://avitomedia.com/android_media",
                payZoneBaseUrl = "https://paiement.payzone.ma/transaction/",
                searchBaseUrl = "https://api-pre.avito.ma/v1/",
                authHostBaseUrl = "https://oauth-pre.avito.ma/api/v1/",
                mcAdsHostBaseUrl = "https://mcproxy-pre.avito.ma/api/messaging/",
                mcHostBaseUrl = "https://mcproxy-pre.avito.ma/proxy/",
                adViewAdParamsIconsUrl = "https://assets.avito.ma/android/ad_params/",
                newAuthUrl = "https://services-pre.avito.ma/api/v2",
                adServerUrl = "https://media-server-pre.avito.ma/",
                graphQlEndpointUrl = "https://gateway-pre.avito.ma/graphql",
                graphQlWebSocketUrl = "wss://gateway-pre.avito.ma/websocket",
                newAdInsertUrl = "https://services-pre.avito.ma/api/v2",
                newListingFilterUrl = "https://services-pre.avito.ma/api/v2/config/adlisting/"
        )

        object ConfigFallback : AppEnvironment(
                label = "config fallback",
                apiBaseUrl = "https://services.avito.ma/api/v1",
                configApiBaseUrl = "https://services.avito.ma/api/v1/fail",//to use fallback config.json file
                webBaseUrl = "https://www.avito.ma",
                thumbBaseUrl = "https://content.avito.ma/images/",
                fullImageBaseUrl = "https://content.avito.ma/mob_images/",
                mauHostUrl = "https://profile.avito.ma/api/v1/user/",
                deliveryBaseUrl = "https://www.avito.ma/delivery",
                paymentAPIBaseUrl = "https://www2.avito.ma/api/v1",
                vasAPIBaseUrl = "https://www2.avito.ma/",
                vasGalleryBaseUrl = "https://gallery.avito.ma/api/v1/gallery/",
                monetizationImgBaseURL = "https://assets.avito.ma/android/",
                legacyMonetizationImgBaseURL = "https://www2.avito.ma/",
                phoneVerificationBaseUrl = "https://verify.avito.ma",
                mediaConfigBaseUrl = "https://avitomedia.com/",
                mediaAndroidConfigBaseUrl = "https://avitomedia.com/android_media",
                payZoneBaseUrl = "https://paiement.payzone.ma/transaction/",
                searchBaseUrl = "https://api.avito.ma/v1/",
                authHostBaseUrl = "https://oauth.avito.ma/api/v1/",
                mcAdsHostBaseUrl = "https://mcproxy.avito.ma/api/messaging/",
                mcHostBaseUrl = "https://mcproxy.avito.ma/proxy/",
                adViewAdParamsIconsUrl = "https://assets.avito.ma/android/ad_params/",
                newAuthUrl = "https://services.avito.ma/api/v2",
                adServerUrl = "https://media-server.avito.ma/",
                graphQlEndpointUrl = "https://gateway.avito.ma/graphql",
                graphQlWebSocketUrl = "wss://gateway.avito.ma/websocket",
                newAdInsertUrl = "https://services.avito.ma/api/v2",
                newListingFilterUrl = "https://services-pre.avito.ma/api/v2/config/adlisting/"
        )


}


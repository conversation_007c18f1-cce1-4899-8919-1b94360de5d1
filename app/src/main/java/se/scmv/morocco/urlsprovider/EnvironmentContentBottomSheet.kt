package se.scmv.morocco.urlsprovider

import android.content.Context
import android.os.Bundle
import android.text.SpannableStringBuilder
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.core.text.bold
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import se.scmv.morocco.R

class EnvironmentContentBottomSheet(context: Context, environment: AppEnvironment) :
        BottomSheetDialog(context, R.style.OrionBottomSheetDialogTheme) {

        private var environmentUrls: TextView? = null
        private val appEnvironment: AppEnvironment = environment


        override fun onStart() {
                super.onStart()
                expandMe()
        }

        override fun onCreate(savedInstanceState: Bundle?) {
                super.onCreate(savedInstanceState)
                val root = View.inflate(context, R.layout.app_environment_urls, null)
                setContentView(root)
                init(root)
        }

        private fun displayEnvironmentUrls(): SpannableStringBuilder {

                return SpannableStringBuilder()
                        .bold { append("ApiBaseUrl:\n") }
                        .append(appEnvironment.apiBaseUrl)
                        .append("\n\n")
                        .bold { append("WebBaseUrl:\n") }
                        .append(appEnvironment.webBaseUrl)
                        .append("\n\n")
                        .bold { append("ThumbBaseUrl:\n") }
                        .append(appEnvironment.thumbBaseUrl)
                        .append("\n\n")
                        .bold { append("FullImageBaseUrl:\n") }
                        .append(appEnvironment.fullImageBaseUrl)
                        .append("\n\n")
                        .bold { append("MAUHostUrl:\n") }
                        .append(appEnvironment.mauHostUrl)
                        .append("\n\n")
                        .bold { append("VasAPIBaseUrl:\n") }
                        .append(appEnvironment.vasAPIBaseUrl)
                        .append("\n\n")
                        .bold { append("VasGalleryBaseUrl:\n") }
                        .append(appEnvironment.vasGalleryBaseUrl)
                        .append("\n\n")
                        .bold { append("LegacyMonetizationImgBaseURL:\n") }
                        .append(appEnvironment.legacyMonetizationImgBaseURL)
                        .append("\n\n")
                        .bold { append("PhoneVerificationBaseUrl:\n") }
                        .append(appEnvironment.phoneVerificationBaseUrl)
                        .append("\n\n")
                        .bold { append("MediaConfigBaseUrl:\n") }
                        .append(appEnvironment.mediaConfigBaseUrl)
                        .append("\n\n")
                        .bold { append("MediaAndroidConfigBaseUrl:\n") }
                        .append(appEnvironment.mediaAndroidConfigBaseUrl)
                        .append("\n\n")
                        .bold { append("SearchBaseUrl:\n") }
                        .append(appEnvironment.searchBaseUrl)
                        .append("\n\n")
                        .bold { append("AuthHostBaseUrl:\n") }
                        .append(appEnvironment.authHostBaseUrl)
                        .append("\n\n")
                        .bold { append("MCAdsHostBaseUrl:\n") }
                        .append(appEnvironment.mcAdsHostBaseUrl)
                        .append("\n\n")
                        .bold { append("MCHostBaseUrl:\n") }
                        .append(appEnvironment.mcHostBaseUrl)
                        .append("\n\n")
                        .bold { append("AdViewAdParamsIconsUrl:\n") }
                        .append(appEnvironment.adViewAdParamsIconsUrl + "***.svg")
                        .append("\n\n")
                        .append("\n\n")

        }

        private fun init(view: View) {
                environmentUrls = view.findViewById(R.id.environment_urls_tv)
                environmentUrls?.text = displayEnvironmentUrls()
                initTitle(view)
                view.findViewById<ImageView>(R.id.closeWindow).setOnClickListener { dismiss() }
        }

        private fun initTitle(view: View) {
                val title = view.findViewById<TextView>(R.id.environmentUrlsTitle)
                title.text = "Used Urls"
                title.setOnClickListener { dismiss() }
        }

        private fun expandMe() {
                window?.setLayout(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.MATCH_PARENT
                )
                val bottomSheet =
                        window?.decorView?.findViewById<View>(ma.avito.orion.R.id.design_bottom_sheet) as FrameLayout?
                bottomSheet?.let {
                        val mBehavior = BottomSheetBehavior.from(it)
                        mBehavior.state = BottomSheetBehavior.STATE_EXPANDED
                }
        }
}
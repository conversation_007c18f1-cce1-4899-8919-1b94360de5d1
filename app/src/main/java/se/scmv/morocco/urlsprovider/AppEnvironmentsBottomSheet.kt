package se.scmv.morocco.urlsprovider

import android.content.Context
import android.content.SharedPreferences
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import se.scmv.morocco.Avito
import se.scmv.morocco.R
import se.scmv.morocco.utils.Utils

class AppEnvironmentsBottomSheet(context: Context) :
        BottomSheetDialog(context, R.style.OrionBottomSheetDialogTheme) {

        private lateinit var recyclerView: RecyclerView
        private var environments: List<AppEnvironment> = listOf(
                AppEnvironment.Pro, AppEnvironment.Pre, AppEnvironment.ConfigFallback
        )
        private var selectedEnvironmentLabel: String? = null

        override fun onStart() {
                super.onStart()
                expandMe()
        }

        override fun onCreate(savedInstanceState: Bundle?) {
                super.onCreate(savedInstanceState)
                val root = View.inflate(context, R.layout.app_environments_items_dialog, null)
                setContentView(root)
                init(root)
        }

        private fun init(view: View) {
                selectedEnvironmentLabel = Avito.APP_ENV
                initRecyclerView(view)
                initTitle(view)
                view.findViewById<ImageView>(R.id.closeWindow).setOnClickListener { dismiss() }
        }

        private fun initRecyclerView(view: View) {
                recyclerView = view.findViewById(ma.avito.orion.R.id.itemRangeList)
                recyclerView.layoutManager = LinearLayoutManager(context)
                recyclerView.adapter = ItemAdapter(environments)
        }

        private fun initTitle(view: View) {
                val title = view.findViewById<TextView>(R.id.itemRangeTitle)
                title.text = "App Environments"
                title.setOnClickListener { dismiss() }
        }

        private fun expandMe() {
                window?.setLayout(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.MATCH_PARENT
                )
                val bottomSheet =
                        window?.decorView?.findViewById<View>(ma.avito.orion.R.id.design_bottom_sheet) as FrameLayout?
                bottomSheet?.let {
                        val mBehavior = BottomSheetBehavior.from(it)
                        mBehavior.state = BottomSheetBehavior.STATE_COLLAPSED
                }
        }

        private inner class ItemAdapter(private val mEnvironments: List<AppEnvironment>) :
                RecyclerView.Adapter<ItemAdapter.ViewHolder>() {

                override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
                        return ViewHolder(LayoutInflater.from(parent.context), parent)
                }

                override fun onBindViewHolder(holder: ViewHolder, position: Int) {
                        holder.text.text = mEnvironments[position].label

                        if (mEnvironments[position].label == selectedEnvironmentLabel) {
                                holder.text.setTextColor(
                                        ContextCompat.getColor(
                                                context,
                                                ma.avito.orion.R.color.orionColorPrimary
                                        )
                                )
                        } else {
                                holder.text.setTextColor(
                                        ContextCompat.getColor(
                                                context,
                                                ma.avito.orion.R.color.orionTextColorNormal
                                        )
                                )
                        }
                }


                override fun getItemCount(): Int {
                        return mEnvironments.size
                }

                inner class ViewHolder(inflater: LayoutInflater, parent: ViewGroup) :
                        RecyclerView.ViewHolder(
                                inflater.inflate(
                                        R.layout.app_environments_item,
                                        parent,
                                        false
                                )
                        ) {

                        val text: TextView = itemView.findViewById(R.id.text)
                        val contentIcon: ImageView = itemView.findViewById(R.id.display_content)


                        init {
                                contentIcon.setOnClickListener {
                                        EnvironmentContentBottomSheet(
                                                context,
                                                mEnvironments[adapterPosition]
                                        ).show()
                                }

                                itemView.setOnClickListener {
                                        if (selectedEnvironmentLabel != mEnvironments[adapterPosition].label) {
                                                selectedEnvironmentLabel =
                                                        mEnvironments[adapterPosition].label
                                                recyclerView.adapter?.notifyDataSetChanged()
                                                changeEnvironment()
                                        }
                                        dismiss()

                                }
                        }
                }
        }

        fun changeEnvironment() {
                val prefs: SharedPreferences = Utils.getSharedPreferences(Avito.context)
                prefs.edit().clear().apply()
                Utils.savePreference(Avito.context, Utils.APP_ENV, selectedEnvironmentLabel)
                Utils.rebirthApp(Avito.context)
        }
}
package se.scmv.morocco.similarads

import se.scmv.morocco.Avito
import se.scmv.morocco.R
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import java.util.concurrent.TimeUnit

class SimilarAdsUtils {

        companion object {
                fun timeAgo(date1: Date, date2: Date): String {
                        val context = Avito.context
                        val diffInMillies = Math.abs(date2.time - date1.time)
                        val seconds = TimeUnit.SECONDS.convert(diffInMillies, TimeUnit.MILLISECONDS)
                        val minute = seconds / 60

                        val date1Calendar = Calendar.getInstance()
                        date1Calendar.timeInMillis = date1.time
                        val date2Calendar = Calendar.getInstance()
                        date2Calendar.timeInMillis = date2.time
                        val isToday =
                                date2Calendar.get(Calendar.DATE) == date1Calendar.get(Calendar.DATE)
                        val isYesterday =
                                date2Calendar.get(Calendar.DATE) - date1Calendar.get(Calendar.DATE) == 1
                        val timeformat = SimpleDateFormat("HH:mm", Locale.FRENCH).format(date1)
                        return when {
                                seconds < 60 -> context!!.getString(R.string.similar_ads_secondes_ago)
                                seconds < 90 -> context!!.getString(R.string.similar_ads_a_minute_ago)
                                minute < 60 -> context!!.resources.getQuantityString(
                                        R.plurals.similar_ads_minutes_ago,
                                        minute.toInt(),
                                        minute.toInt()
                                )

                                isToday -> String.format(
                                        context!!.getString(R.string.similar_ads_today_at),
                                        timeformat
                                )
                                isYesterday -> String.format(
                                        context!!.getString(R.string.similar_ads_yesterday_at),
                                        timeformat
                                )
                                else -> SimpleDateFormat("dd MMM à HH:mm", Locale.FRENCH).format(
                                        date1
                                )

                        }
                }

                fun returnInteger(s: String?): String? {
                        if (s == null)
                                return null
                        else {
                                val characters = s.toCharArray()
                                var value: Int? = null
                                var isPrevDigit = false
                                for (i in characters.indices) {
                                        if (!isPrevDigit) {
                                                if (Character.isDigit(characters[i])) {
                                                        isPrevDigit = true
                                                        value =
                                                                Character.getNumericValue(characters[i])
                                                }
                                        } else {
                                                if (Character.isDigit(characters[i])) {
                                                        if (value != null) {
                                                                value =
                                                                        value * 10 + Character.getNumericValue(
                                                                                characters[i]
                                                                        )
                                                        }
                                                } else {
                                                        break
                                                }
                                        }
                                }
                                return value.toString()
                        }
                }
        }
}
package se.scmv.morocco.similarads

import se.scmv.morocco.Avito
import se.scmv.morocco.R
import se.scmv.morocco.common.lang.LocaleManager
import se.scmv.morocco.designsystem.utils.PriceFormatter
import se.scmv.morocco.utils.Mapping
import java.text.SimpleDateFormat
import java.util.Date

object SimilarAdsFormatter {

        fun prepareCarouselViewData(ads: List<SuggestedAd>): List<CarouselItemData> {
                val viewData = ArrayList<CarouselItemData>()
                ads.forEach { e -> viewData.add(prepareCarouselViewItem(e)) }
                return viewData
        }

        private fun prepareCarouselViewItem(element: SuggestedAd): CarouselItemData {
                val mapping = Mapping()
                return CarouselItemData(
                        element.id(),
                        element.title(),
                        prepareFormattedDate(element.timestamp()),
                        prepareFormattedPrice(element.price()),
                        element.category(),
                        mapping.getCitybyId(element.region(), LocaleManager.getCurrentLanguage()),
                        element.thumbUrl(),
                        element.nbImages().toString()
                )
        }

        private fun prepareFormattedPrice(price: String): String {
                return if (price.isNotBlank() && price != "0") PriceFormatter.formatForDisplay(price) + " " + Avito.context!!.getString(
                        R.string.currency
                ) else Avito.context!!.getString(R.string.price_not_specified)
        }

        private fun prepareFormattedDate(time: String): String {
                if (time.equals(""))
                        return ""
                val formatter = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                val date = formatter.parse(time)
                return SimilarAdsUtils.timeAgo(date, Date())
        }
}
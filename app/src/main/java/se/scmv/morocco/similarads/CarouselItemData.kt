package se.scmv.morocco.similarads


data class CarouselItemData(
        val itemId: String,
        val itemTitle: String,
        val date: String,
        val itemPrice: String,
        val category: Int,
        val region: String,
        val image: String,
        val imageCount: String
) : SuggestedAd {
        override fun id(): String {
                return this.itemId
        }

        override fun title(): String {
                return this.itemTitle
        }

        override fun price(): String {
                return this.itemPrice.toString()
        }

        override fun timestamp(): String {
                return date
        }

        override fun nbImages(): Int {
                return imageCount.toInt()
        }

        override fun thumbUrl(): String {
                return image
        }

        override fun category(): Int {
                return category
        }

        override fun region(): Int {
                return region.toInt()
        }
}

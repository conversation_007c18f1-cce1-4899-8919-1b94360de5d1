package se.scmv.morocco.userprofile

import se.scmv.morocco.similarads.CarouselItemData

interface UserProfileContract {
        interface Presenter {
                fun loadAds(userId: String, loadStrategy: UserProfilePresenter.LoadAdsStrategy)
                fun configureAdsListing(
                        userId: String,
                        numberOfActiveAds: Int,
                        sellerName: String?,
                        registrationDate: String?,
                        logo: String?
                )

                fun updateFilter(filters: UserProfilePresenter.ListingFilters)
        }

        interface Ui {
                fun isConnected(): Boolean
                fun showAds(
                        ads: List<CarouselItemData>,
                        loadStrategy: UserProfilePresenter.LoadAdsStrategy
                )
                fun showError()

                fun displayUserDetails(
                        sellerName: String?,
                        registrationDate: String?,
                        numberOfActiveAds: Int
                )

                fun showProgress()
                fun hideProgress()
        }
}
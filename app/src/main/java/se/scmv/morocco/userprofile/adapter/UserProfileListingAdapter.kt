package se.scmv.morocco.userprofile.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import se.scmv.morocco.R
import se.scmv.morocco.similarads.CarouselItemData
import se.scmv.morocco.utils.DrawablesUtils

class UserProfileListingAdapter : RecyclerView.Adapter<UserProfileListingAdapter.ViewHolder> {
        var items: MutableList<CarouselItemData>
        val context: Context?
        var onLoadMoreListener: OnLoadMoreListener? = null
        val onclick: (Int) -> Unit


        constructor(
                context: Context?,
                items: MutableList<CarouselItemData>,
                onclick: (Int) -> Unit
        ) {
                this.context = context
                this.items = items
                this.onclick = onclick
        }


        override fun onCreateViewHolder(viewGroup: ViewGroup, viewType: Int): ViewHolder {
                val view = LayoutInflater.from(context)
                        .inflate(R.layout.user_profile_list_item, viewGroup, false)
                return ViewHolder(view)
        }

        override fun getItemCount(): Int {
                return items.size
        }

        override fun onBindViewHolder(viewHolder: ViewHolder, position: Int) {
                viewHolder.render(items.get(position))
        }

        fun clearItems() {
                items = ArrayList()
                notifyDataSetChanged()
        }

        inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
                fun render(ad: CarouselItemData) {
                        title.text = ad.title()
                        imageCount.text = ad.imageCount
                        price.text = ad.price().toString()
                        date.text = ad.date
                        region.text = ad.region
                        if (!ad.image.equals(""))
                                Glide.with(context as Context).load(ad.image).placeholder(
                                        DrawablesUtils.getImagePlaceholder(
                                                context,
                                                ad.category
                                        )
                                ).into(image)
                        else {
                                image.scaleType = ImageView.ScaleType.CENTER
                                image.setImageDrawable(
                                        DrawablesUtils.getImagePlaceholder(
                                                context,
                                                (ad.category / 1000).toInt() * 1000
                                        )
                                )
                                context?.let {
                                        ContextCompat.getColor(
                                                it,
                                                R.color.placeholder_background
                                        )
                                }?.let { image.setBackgroundColor(it) }
                        }
                }

                val title: TextView
                val date: TextView
                val region: TextView
                val price: TextView
                val image: ImageView
                val imageCount: TextView

                init {
                        imageCount = itemView.findViewById(R.id.gallery_btn)
                        title = itemView.findViewById(R.id.ad_title_view)
                        region = itemView.findViewById(R.id.ad_location_view)
                        date = itemView.findViewById(R.id.ad_date_view)
                        price = itemView.findViewById(R.id.ad_price_view)
                        image = itemView.findViewById(R.id.ad_thumb_image_view)
                        itemView.setOnClickListener {
                                onclick(adapterPosition)
                        }
                }


        }


}
package se.scmv.morocco.userprofile

class Pagination(val pageSize: Int, val total: Int, val strategy: PaginationStrategy) {
        var currentpage = 0


        fun resetCount() {
                this.currentpage = 0
        }

        @Throws(PaginationException::class)
        fun moveToNextPage(): Int {
                currentpage = strategy.nextPage(currentpage, pageSize, total)
                return currentpage
        }

        interface PaginationStrategy {
                @Throws(PaginationException::class)
                fun nextPage(currentOffset: Int, pageSize: Int, total: Int): Int
        }

        class PaginationException(message: String?) : Exception(message)

        class DefaultPaginationStrategy : PaginationStrategy {
                override fun nextPage(currentOffset: Int, pageSize: Int, total: Int): Int {
                        val nextOffset: Int
                        var length: Int = pageSize
                        if (pageSize > total)
                                length = total
                        if (currentOffset * length > total)
                                throw PaginationException("End reached")
                        else {
                                nextOffset = currentOffset + 1
                        }
                        return nextOffset
                }

        }
}
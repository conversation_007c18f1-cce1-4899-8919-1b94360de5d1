package se.scmv.morocco.userprofile

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.bumptech.glide.Glide
import com.google.android.material.appbar.AppBarLayout
import dagger.hilt.android.AndroidEntryPoint
import se.scmv.morocco.R
import android.content.Intent
import se.scmv.morocco.activities.MainActivity
import se.scmv.morocco.utils.Keys
import se.scmv.morocco.databinding.FragmentUserProfileBinding
import se.scmv.morocco.fragments.BaseFragment
import se.scmv.morocco.similarads.CarouselItemData
import se.scmv.morocco.userprofile.adapter.LoadMoreScrollListener
import se.scmv.morocco.userprofile.adapter.OnLoadMoreListener
import se.scmv.morocco.userprofile.adapter.UserProfileListingAdapter
import se.scmv.morocco.userprofile.service.UserProfileApi
import se.scmv.morocco.userprofile.service.UserProfileService
import se.scmv.morocco.utils.isNotNull
import se.scmv.morocco.widgets.FilterView
import javax.inject.Inject

@AndroidEntryPoint
class UserProfileFragment : BaseFragment(), UserProfileContract.Ui,
        SwipeRefreshLayout.OnRefreshListener, FilterView.onChangeStateListener {

        private var _binding: FragmentUserProfileBinding? = null
        private val binding get() = _binding!!
        private var loadMoreScrollListener: LoadMoreScrollListener? = null

        @Inject
        lateinit var userProfileApi: UserProfileApi
        val presenter: UserProfilePresenter by lazy {
                UserProfilePresenter(
                        UserProfileService(userProfileApi),
                        lifecycleScope
                )
        }

        override fun isConnected(): Boolean {
                return mNetworkManager.isNotNull() && mNetworkManager?.isConnected == true
        }

        private fun lockFilterPanel() {
                binding.priceFilterView.isEnabled = false
                binding.dateFilterView.isEnabled = false
        }

        private fun unlockFilterPanel() {
                binding.priceFilterView.isEnabled = true
                binding.dateFilterView.isEnabled = true
        }

        override fun onChangeState(v: View, state: FilterView.State) {
                lockFilterPanel()
                val filter: UserProfilePresenter.ListingFilters = when (v.id) {
                        R.id.price_filter_view -> {
                                manageFilterUIChange(
                                        binding.priceFilterView,
                                        binding.dateFilterView
                                )
                                when (state) {
                                        FilterView.State.ASC -> UserProfilePresenter.ListingFilters.PRICE_DESC
                                        else -> UserProfilePresenter.ListingFilters.PRICE_ASC
                                }
                        }
                        R.id.date_filter_view -> {
                                manageFilterUIChange(
                                        binding.priceFilterView,
                                                binding.dateFilterView
                                )
                                when (state) {
                                        FilterView.State.ASC -> UserProfilePresenter.ListingFilters.DATE_DESC
                                        else -> UserProfilePresenter.ListingFilters.DATE_ASC
                                }
                        }

                        else -> {
                                UserProfilePresenter.ListingFilters.PRICE_ASC
                        }
                }
                presenter.updateFilter(filter)
                arguments?.getString(UserProfileActivity.USER_ID)?.let {
                        presenter.loadAds(it, UserProfilePresenter.LoadAdsStrategy.CLEAR)
                }
        }

        override fun displayUserDetails(
                sellerName: String?,
                registrationDate: String?,
                numberOfActiveAds: Int
        ) {
                with(binding) {
                        title.text = sellerName
                        memberSince.text =
                                String.format(getString(R.string.membre_since), registrationDate)
                        numberOfAds.text =
                                String.format(getString(R.string.number_of_ads), numberOfActiveAds)
                }
        }

        override fun showProgress() {
                lockFilterPanel()
                binding.progressBar.visibility = View.VISIBLE
        }

        override fun hideProgress() {
                unlockFilterPanel()
                binding.progressBar.visibility = View.GONE
                loadMoreScrollListener?.stopLoading()

        }

        override fun onRefresh() {
                arguments?.getString(UserProfileActivity.USER_ID)?.let {
                        presenter.loadAds(it, UserProfilePresenter.LoadAdsStrategy.CLEAR)
                }
        }

        override fun showAds(
                ads: List<CarouselItemData>,
                loadStrategy: UserProfilePresenter.LoadAdsStrategy
        ) {
                binding.layoutError.root.isVisible = false
                loadMoreScrollListener?.stopLoading()
                binding.swipeToRefreshLayout.isRefreshing = false
                binding.recyclerView.adapter.let {
                        when (loadStrategy) {
                                UserProfilePresenter.LoadAdsStrategy.CLEAR -> {
                                        (it as UserProfileListingAdapter).items.clear()
                                }
                                UserProfilePresenter.LoadAdsStrategy.APPEND -> {

                                }
                        }
                        (it as UserProfileListingAdapter).items.addAll(ads)
                        it.notifyDataSetChanged()
                }
        }

        override fun showError() {
                binding.swipeToRefreshLayout.isVisible = false
                binding.progressBar.isVisible = false
                binding.layoutError.root.isVisible = true
        }


        override fun onOffline() {
                binding.layoutError.root.isVisible = false
                binding.container.visibility = View.GONE
                binding.offlineLayout.visibility = View.VISIBLE
        }

        override fun onAttach(context: Context) {
                context.let { super.onAttach(it) }
                presenter.attach(this)
        }

        override fun onCreateView(
                inflater: LayoutInflater,
                container: ViewGroup?,
                savedInstanceState: Bundle?
        ): View? {
                _binding = FragmentUserProfileBinding.inflate(inflater, container, false)
                return _binding?.root
        }

        private fun onAdClicked(ads: List<CarouselItemData>, position: Int) {
                if (position <= ads.size) {
                        val adId = ads.get(position).id()
                        val intent = Intent(requireContext(), MainActivity::class.java).apply {
                                putExtra(Keys.AD_ID, adId)
                                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                                addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                        }
                        startActivity(intent)
                }
        }

        override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
                super.onViewCreated(view, savedInstanceState)

                val ads = ArrayList<CarouselItemData>()
                binding.recyclerView.let {
                        it.layoutManager = LinearLayoutManager(context)
                        it.adapter = UserProfileListingAdapter(
                                context,
                                ads,
                                { position -> onAdClicked(ads, position) })
                        loadMoreScrollListener = LoadMoreScrollListener(
                                it.layoutManager as LinearLayoutManager,
                                object : OnLoadMoreListener {
                                        override fun onLoadMore() {
                                                arguments?.getString(UserProfileActivity.USER_ID)
                                                        ?.let {
                                                                presenter.loadAds(
                                                                        it,
                                                                        UserProfilePresenter.LoadAdsStrategy.APPEND,
                                                                )
                                                        }
                                        }
                                })
                        loadMoreScrollListener?.let { loadMoreSafe ->
                                it.addOnScrollListener(loadMoreSafe)
                        }

                }
                Glide.with(requireContext())
                        .load(arguments?.getString(UserProfileActivity.SHOP_LOGO))
                        .error(R.mipmap.ic_launcher)
                        .into(binding.profileImage)
                binding.swipeToRefreshLayout.setOnRefreshListener(this)
                (activity as AppCompatActivity).apply {
                        setSupportActionBar(binding.toolbar)
                        supportActionBar?.setDisplayHomeAsUpEnabled(true)
                        supportActionBar?.setHomeButtonEnabled(true)
                        supportActionBar?.setDisplayShowTitleEnabled(false)
                }
                binding.dateFilterView.changeStateListener = this
                binding.priceFilterView.changeStateListener = this
                initWithUserInfo()
                binding.retryBtn.setOnClickListener {
                        binding.offlineLayout.visibility = View.GONE
                        binding.container.visibility = View.VISIBLE
                        initWithUserInfo()
                }
                binding.appbar.addOnOffsetChangedListener(object :
                        AppBarStateChangeListener() {
                        override fun onStateChanged(appBarLayout: AppBarLayout, state: State) {
                                when {
                                        state == State.COLLAPSED -> {
                                                binding.toolbar.title = binding.title.text

                                        }
                                        state == State.EXPANDED -> {
                                                binding.toolbar.title = ""
                                        }
                                        else -> {

                                        }
                                }
                        }

                })
                binding.layoutError.retryButton.setOnClickListener {
                        binding.layoutError.root.visibility = View.GONE
                        binding.container.visibility = View.VISIBLE
                        initWithUserInfo()
                }
        }

        private fun manageFilterUIChange(active: FilterView?, disabled: FilterView?) {
                disabled?.disable()
        }

        private fun initWithUserInfo() {
                val userId = arguments?.getString(UserProfileActivity.USER_ID)
                val adsCount = arguments?.getInt(UserProfileActivity.ADS_COUNT)
                val sellerName = arguments?.getString(UserProfileActivity.SELLER_NAME)
                val registrationDate = arguments?.getString(UserProfileActivity.REGISTRATION_DATE)
                val shopLogo = arguments?.getString(UserProfileActivity.SHOP_LOGO)
                if (userId != null && adsCount != null)
                        presenter.configureAdsListing(
                                userId,
                                adsCount,
                                sellerName,
                                registrationDate,
                                shopLogo
                        )

        }

        companion object {
                fun newInstance(
                        userId: String,
                        numberOfActiveAds: Int?,
                        sellerName: String?,
                        registrationDate: String?,
                        logo: String?
                ): Fragment {
                        val userProfileFragment = UserProfileFragment()
                        val arguments = Bundle()
                        arguments.putString(UserProfileActivity.USER_ID, userId)
                        if (numberOfActiveAds != null) {
                                arguments.putInt(UserProfileActivity.ADS_COUNT, numberOfActiveAds)
                        }
                        arguments.putString(UserProfileActivity.SELLER_NAME, sellerName)
                        arguments.putString(UserProfileActivity.REGISTRATION_DATE, registrationDate)
                        arguments.putString(UserProfileActivity.SHOP_LOGO, logo)

                        userProfileFragment.arguments = arguments

                        return userProfileFragment
                }
        }

        override fun onDetach() {
                presenter.detach()
                super.onDetach()
        }
}

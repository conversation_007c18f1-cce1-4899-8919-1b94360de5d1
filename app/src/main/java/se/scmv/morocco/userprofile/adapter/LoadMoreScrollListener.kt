package se.scmv.morocco.userprofile.adapter

import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView

class LoadMoreScrollListener(
        val layoutManager: LinearLayoutManager,
        val listener: OnLoadMoreListener
) : RecyclerView.OnScrollListener() {

        private var enabled = true
        private var loading: Boolean = false


        fun setEnabled(enabled: Boolean) {
                this.enabled = enabled
        }

        fun stopLoading() {
                this.loading = false
        }

        override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                val visibleItemCount = layoutManager.childCount
                val totalItemCount = layoutManager.itemCount
                val pastVisibleItems = layoutManager.findFirstVisibleItemPosition()

                if (enabled && !loading && visibleItemCount + pastVisibleItems >= totalItemCount) {
                        listener.onLoadMore()
                        loading = true
                }
        }


}
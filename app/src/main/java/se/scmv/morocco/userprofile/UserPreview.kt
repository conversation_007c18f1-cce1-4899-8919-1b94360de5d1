package se.scmv.morocco.userprofile

interface UserPreview {
        fun accountID(): String
        fun activeSince(): String
        fun adsCount(): Int
        fun username(): String
        fun userType(): String
        fun accountId(): String
        fun address(): String?
        fun region(): String?
        fun primaryPhone(): String?
        fun secondaryPhone(): String?
        fun sellerWebsite(): String?
        fun storeUrl(): String?
        fun storeLongDesc(): String?
        fun storeCategory(): String?
        fun storeLogo(): String?
}
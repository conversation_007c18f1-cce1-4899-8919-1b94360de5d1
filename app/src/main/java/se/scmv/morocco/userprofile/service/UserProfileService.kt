package se.scmv.morocco.userprofile.service

import retrofit2.http.GET
import retrofit2.http.Path
import retrofit2.http.Query
import retrofit2.http.QueryMap
import se.scmv.morocco.similarads.user.network.MoreAdsByUserResponse
import se.scmv.morocco.userprofile.UserProfilePresenter


interface UserProfileServiceInterface {
    suspend fun loadAds(
        userId: String,
        page: Int,
        filters: UserProfilePresenter.ListingFilters,
    ): MoreAdsByUserResponse
}

class UserProfileService(
    private val api: UserProfileApi
) : UserProfileServiceInterface {

    override suspend fun loadAds(
        userId: String,
        page: Int,
        filters: UserProfilePresenter.ListingFilters,
    ): MoreAdsByUserResponse {
        return api.getUserAds(
            userId = userId,
            page = page,
            limit = 6,
            sort = filters.value
        )
    }

}

interface UserProfileApi {

    @GET("{userId}/ads")
    suspend fun getUserAds(
        @Path("userId") userId: String,
        @Query("page") page: Int,
        @Query("limit") limit: Int,
        @QueryMap sort: Map<String, String>
    ): MoreAdsByUserResponse
}



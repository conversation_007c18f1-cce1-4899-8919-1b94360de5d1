package se.scmv.morocco.userprofile

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import se.scmv.morocco.core.BaseFragmentPresenter
import se.scmv.morocco.similarads.SimilarAdsFormatter
import se.scmv.morocco.userprofile.service.UserProfileServiceInterface
import se.scmv.morocco.utils.Log

class UserProfilePresenter(
        private val service: UserProfileServiceInterface,
        private val scope: CoroutineScope
) : BaseFragmentPresenter<UserProfileFragment?>(),
        UserProfileContract.Presenter {

        var pagination: Pagination? = null
        var filters: ListingFilters = ListingFilters.DATE_DESC

        override fun attach(v: UserProfileFragment?) {
                super.attach(v)
        }

        override fun configureAdsListing(
                userId: String,
                numberOfActiveAds: Int,
                sellerName: String?,
                registrationDate: String?,
                logo: String?
        ) {
                view?.isConnected()?.let {
                        if (!it) return
                }
                view?.displayUserDetails(sellerName, registrationDate, numberOfActiveAds)
                val strategy = Pagination.DefaultPaginationStrategy()
                pagination = Pagination(6, numberOfActiveAds, strategy)
                loadAds(userId, LoadAdsStrategy.CLEAR)
        }

        override fun loadAds(userId: String, loadStrategy: LoadAdsStrategy) {
                view?.showProgress()
                if (loadStrategy == LoadAdsStrategy.CLEAR)
                        pagination?.resetCount()
                scope.launch {
                        try {
                                val ads = service.loadAds(userId, pagination?.moveToNextPage() ?: 0, filters)
                                view?.showAds(
                                        SimilarAdsFormatter.prepareCarouselViewData(ads.ads),
                                        loadStrategy
                                )
                        } catch (error: Exception) {
                                Log.e(
                                        "UserProfilePresenter",
                                        "onError ${error.message}"
                                )
                                if (pagination?.currentpage == 1) {
                                        view?.showError()
                                }
                        } finally {
                                view?.hideProgress()
                        }
                }

        }

        override fun updateFilter(filters: ListingFilters) {
                view?.isConnected()?.let {
                        if (!it) return
                }
                this.filters = filters
        }

        enum class LoadAdsStrategy {
                APPEND, CLEAR
        }

        enum class ListingFilters(var value: Map<String, String>, var tag: String) {
                PRICE_ASC(mapOf("price" to "asc"), "price"),
                PRICE_DESC(mapOf("price" to "desc"), "price"),
                DATE_ASC(mapOf("date" to "asc"), "date"),
                DATE_DESC(mapOf("date" to "desc"), "date")
        }

}


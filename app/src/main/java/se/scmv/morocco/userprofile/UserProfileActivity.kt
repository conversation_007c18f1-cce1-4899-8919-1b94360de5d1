package se.scmv.morocco.userprofile

import android.content.Context
import android.content.Intent
import android.os.Bundle
import dagger.hilt.android.AndroidEntryPoint
import se.scmv.morocco.R
import se.scmv.morocco.activities.BaseActivity
import se.scmv.morocco.activities.MainActivity
import se.scmv.morocco.analytics.AnalyticsManager
import se.scmv.morocco.analytics.utmtagscollector.UTMTagsRetriever
import se.scmv.morocco.similarads.SimilarAdsUtils
import java.util.Locale

@AndroidEntryPoint
class UserProfileActivity : BaseActivity() {

        var isFromDeepLink = false


        override fun init() {
                showUserProfileFragment()
        }


        private fun showUserProfileFragment() {
                if (intent.dataString != null && intent.dataString?.contains("userprofile") == true) {
                        var uuid = ""
                        var additionalProperties = HashMap<String?, String?>()
                        additionalProperties[getString(R.string.amp_source)] =
                                getString(R.string.amp_source_deeplink)
                        isFromDeepLink = true
                        UTMTagsRetriever.retrieveUTMTags(intent.data)
                        if (intent.dataString?.lowercase(Locale.getDefault())
                                        ?.contains("storeid") == true
                        ) {
                                uuid = "S" + SimilarAdsUtils.returnInteger(intent.dataString)
                                additionalProperties["accountType"] = "store"
                        } else if (intent.dataString?.lowercase(Locale.getDefault())
                                        ?.contains("accountid") == true
                        ) {
                                uuid = "A" + SimilarAdsUtils.returnInteger(intent.dataString)
                                additionalProperties["accountType"] = "private"
                        }
                        AnalyticsManager.instance?.logEvent(
                                getString(R.string.clicked_on_mau_link),
                                additionalProperties,
                                true
                        )
                        supportFragmentManager.beginTransaction().replace(
                                android.R.id.content,
                                UserProfileFragment.newInstance(
                                        uuid,
                                        intent.getIntExtra(ADS_COUNT, 0),
                                        intent.getStringExtra(
                                                SELLER_NAME
                                        ),
                                        intent.getStringExtra(REGISTRATION_DATE),
                                        intent.getStringExtra(
                                                SHOP_LOGO
                                        )
                                ),
                                "UserProfileFragment"
                        ).commit()
                } else if (intent.getStringExtra(USER_ID) != null) {
                        isFromDeepLink = false
                        intent.getStringExtra(USER_ID)?.let {
                                supportFragmentManager.beginTransaction().replace(
                                        android.R.id.content,
                                        UserProfileFragment.newInstance(
                                                it,
                                                intent.getIntExtra(ADS_COUNT, 0),
                                                intent.getStringExtra(
                                                        SELLER_NAME
                                                ),
                                                intent.getStringExtra(REGISTRATION_DATE),
                                                intent.getStringExtra(
                                                        SHOP_LOGO
                                                )
                                        ),
                                        "UserProfileFragment"
                                ).commit()

                        }
                }
        }

        override fun onModel() {
        }

        override fun onOffline() {
        }


        companion object {
                const val USER_ID: String = "USER_ID"
                const val ADS_COUNT: String = "ADS_COUNT"
                const val SELLER_NAME: String = "SELLER_NAME"
                const val REGISTRATION_DATE: String = "REGISTRATION_DATE"
                const val SHOP_LOGO: String = "SHOP_LOGO"

                fun newIntent(
                        ctx: Context,
                        userId: String?,
                        adsCount: Int?,
                        sellerName: String?,
                        registrationDate: String?,
                        logo: String?
                ): Intent {
                        val intent = Intent(ctx, UserProfileActivity::class.java)
                        intent.putExtra(USER_ID, userId)
                        intent.putExtra(ADS_COUNT, adsCount)
                        intent.putExtra(SELLER_NAME, sellerName)
                        intent.putExtra(REGISTRATION_DATE, registrationDate)
                        intent.putExtra(SHOP_LOGO, logo)
                        return intent
                }
        }

        override fun onBackPressed() {
                if (isFromDeepLink) {
                        AnalyticsManager.instance?.logUserProfileRelatedEvent(
                                getString(R.string.clicked_on_back_from_deeplink),
                                HashMap()
                        )
                        val intent = Intent(this, MainActivity::class.java)
                        startActivity(intent)
                        finish()
                } else {
                        AnalyticsManager.instance?.logUserProfileRelatedEvent(
                                getString(R.string.clicked_on_back_to_ad_view),
                                HashMap()
                        )
                        super.onBackPressed()
                }
        }

        override fun onSupportNavigateUp(): Boolean {
                onBackPressed()
                return super.onSupportNavigateUp()
        }

        override fun getPageName(): String {
                return ""
        }

        override fun restoreInstanceState(savedInstanceState: Bundle?) {
                if (savedInstanceState != null) {
                        super.onRestoreInstanceState(savedInstanceState)
                }
        }

}

package se.scmv.morocco.avitov2.adview.presentation

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.consumeWindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.systemBars
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.material3.Scaffold
import androidx.compose.ui.Modifier
import androidx.lifecycle.lifecycleScope
import androidx.navigation.NavHostController
import androidx.navigation.compose.rememberNavController
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import se.scmv.morocco.ad.ad_view.TemporaryAdNavHost
import se.scmv.morocco.ad.ad_view.state.AdViewUiEvents
import se.scmv.morocco.authentication.presentation.AuthenticationActivity
import se.scmv.morocco.authentication.presentation.common.LoginType
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.domain.models.AdDetails
import se.scmv.morocco.domain.models.AdPrice
import se.scmv.morocco.domain.navigation.AccountNavigation
import se.scmv.morocco.domain.navigation.EcommerceNavigator
import se.scmv.morocco.domain.navigation.ShopNavigator
import se.scmv.morocco.ui.SnackBarHostForSnackBarController
import javax.inject.Inject


@AndroidEntryPoint
class AdViewActivity : ComponentActivity() {
    private lateinit var navController: NavHostController
    private var showMessageAfterAuth = false

    @Inject
    lateinit var shopNavigator: ShopNavigator

    @Inject
    lateinit var ecommerceNavigator: EcommerceNavigator

    @Inject
    lateinit var accountNavigation: AccountNavigation


    private var authResult: AdViewUiEvents? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Log.d("AdViewActivity", "onCreate called")

        val adId = intent.getStringExtra("adId")
        val imageUrl = intent.getStringExtra("imageUrl")
        val title = intent.getStringExtra("title")
        showMessageAfterAuth = intent.getBooleanExtra("showMessageAfterAuth", false)
        intent.removeExtra("showMessageAfterAuth")


        val date = intent.getStringExtra("date")
        val imageCount = intent.getIntExtra("imageCount", 0)
        val videoCount = intent.getIntExtra("videoCount", 0)
        val videoUrl = intent.getStringExtra("videoUrl")
        val isStore = intent.getBooleanExtra("isStore", false)
        val price = intent.getStringExtra("price")
        val oldPrice = intent.getStringExtra("oldPrice")
        val location = intent.getStringExtra("location")
        val category = intent.getStringExtra("category")
        val categoryIcon = intent.getStringExtra("categoryIcon")
        val isUrgent = intent.getBooleanExtra("isUrgent", false)
        val isHotDeal = intent.getBooleanExtra("isHotDeal", false)
        val discount = intent.getIntExtra("discount", 0)

        if (adId == null) {
            finish()
            return
        }
        setContent {
            Log.d("AdViewActivity", "Setting up Compose UI")
            AvitoTheme {
                navController = rememberNavController()
                Scaffold(
                    modifier = Modifier
                        .fillMaxSize()
                        .windowInsetsPadding(WindowInsets.systemBars),
                    snackbarHost = { SnackBarHostForSnackBarController() }
                ) {
                    TemporaryAdNavHost(
                        modifier = Modifier.consumeWindowInsets(it),
                        adId = adId,
                        imageUrl,
                        title,
                        date,
                        imageCount,
                        videoCount,
                        videoUrl = videoUrl,
                        isStore,
                        price?.let {
                            AdPrice.Available(
                                currentWithCurrency = it,
                                old = null,
                                oldWithCurrency = oldPrice,
                                changeType = null
                            )
                        } ?: AdPrice.Unavailable,
                        location,
                        category,
                        categoryIcon,
                        isUrgent,
                        isHotDeal,
                        discount,
                        navController = navController,
                        onNavigateBack = {
                            Log.d("AdViewActivity", "Navigating back, finishing activity.")
                            finish()
                        },
                        navigateToAuthentication = { navigateToAuthentication() },
                        showMessageAfterAuth = showMessageAfterAuth,
                        onShopPage = { id ->
                            onShopPageClick(id)
                        },
                        onBuyEcommerceProduct = { ad, sellerId ->
                            onBuyEcommerceProduct(ad, sellerId)
                        },
                        navigateToVasActivity = { adId, adCategoryKey, adType, application ->
                            accountNavigation.navigateToVasActivity(
                                adId,
                                adCategoryKey,
                                adType,
                                application
                            )
                        },
                        navigateToChat = { conversationId ->
                            // TODO: Implement chat navigation when the app is fully migrated to compose
                            // For now, we'll use a simple approach
                        },
                        authResult = authResult
                    )
                }
            }
        }
    }

    private fun onBuyEcommerceProduct(ad: AdDetails.Details, sellerId: String) {
        ecommerceNavigator.buyEcommerceProduct(this, ad, sellerId)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == RESULT_OK) {
            val token = data?.getStringExtra(AuthenticationActivity.TOKEN_KEY) ?: return
            val loginTypeName = data.getStringExtra(AuthenticationActivity.LOGIN_TYPE_KEY) ?: return
            val loginType = try {
                LoginType.valueOf(loginTypeName)
            } catch (e: IllegalArgumentException) {
                return
            }

            lifecycleScope.launch {
                val event = when (requestCode) {
                    AuthenticationActivity.REQUEST_SIGN_IN_MESSAGING -> AdViewUiEvents.OnMessagingBtnClicked
                    AuthenticationActivity.REQUEST_SIGN_IN_ECOMMERCE -> AdViewUiEvents.OnShopBtnClicked
                    else -> null
                }
                authResult = event

            }
        }
    }

    // TODO replace this by navigating to auth nestedNavHost, once all the app is in compose
    private fun navigateToAuthentication() {
        val intent = Intent(this, AuthenticationActivity::class.java)
        startActivityForResult(intent, AuthenticationActivity.REQUEST_SIGN_IN_MY_ACCOUNT)

    }

    private fun onShopPageClick(id: String) {
        shopNavigator.openShop(id)
    }

    companion object {
        fun open(
            context: Context,
            adId: String,
            imageUrl: String = "",
            title: String = "",
            date: String? = null,
            imageCount: Int = 0,
            videoCount: Int = 0,
            videoUrl: String? = null,
            isStore: Boolean = false,
            price: String? = null,
            oldPrice: String? = null,
            location: String? = null,
            category: String? = null,
            categoryIcon: String? = null,
            isUrgent: Boolean = false,
            isHotDeal: Boolean = false,
            discount: Int? = null,
            showMessageAfterAuth: Boolean = false
        ) {
            val intent = Intent(context, AdViewActivity::class.java).apply {
                putExtra("adId", adId)
                putExtra("imageUrl", imageUrl)
                putExtra("title", title)
                putExtra("date", date)
                putExtra("imageCount", imageCount)
                putExtra("videoCount", videoCount)
                putExtra("videoUrl", videoUrl)
                putExtra("isStore", isStore)
                putExtra("price", price)
                putExtra("oldPrice", oldPrice)
                putExtra("location", location)
                putExtra("category", category)
                putExtra("categoryIcon", categoryIcon)
                putExtra("isUrgent", isUrgent)
                putExtra("isHotDeal", isHotDeal)
                putExtra("discount", discount)
                putExtra("showMessageAfterAuth", showMessageAfterAuth)
                if (context !is Activity) {
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                }
            }
            context.startActivity(intent)
        }
    }
}
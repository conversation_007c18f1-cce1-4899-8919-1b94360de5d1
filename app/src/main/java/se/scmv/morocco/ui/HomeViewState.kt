package se.scmv.morocco.ui

import android.content.Context
import androidx.compose.runtime.Stable
import kotlinx.collections.immutable.PersistentList
import kotlinx.collections.immutable.persistentListOf
import se.scmv.morocco.R
import se.scmv.morocco.domain.models.Account
import se.scmv.morocco.domain.models.SearchSuggestion
import se.scmv.morocco.domain.models.orion.OrionBaseComponentValue

@Stable
data class HomeViewState(
    val searchQuery: String = "",
    val searchSuggestions: PersistentList<SearchSuggestion> = persistentListOf(),
    val recentSearches: PersistentList<SearchSuggestion> = persistentListOf(),
    val selectedFiltersCount: Int = 0,
    val isLoading: Boolean = false,
)

sealed interface HomeOneTimeEvents {
    data class NotifySearchSuggestionSelected(
        val filtersValues: List<OrionBaseComponentValue>
    ) : HomeOneTimeEvents
}

enum class HomeDrawerItems(
    val label: Int,
    val icon: Int,
    val analyticsName: String
) {
    AD_INSERT(R.string.menu_ad_insert, R.drawable.ic_camera_line, "ad_insert"),
    ACCOUNT(R.string.my_info, R.drawable.ic_outlined_user, "my_account"),
    LOGIN(R.string.common_login, R.drawable.ic_outlined_user, "login"),
    MESSAGING(R.string.menu_my_messages, R.drawable.ic_chat_white, "messaging"),
    FAVORITES(R.string.menu_my_favorites, R.drawable.ic_outlined_heart, "my_favorites"),
    STATS(R.string.menu_my_statistics, R.drawable.ic_outlined_statistics, "my_statistics"),
    LANGUAGE(R.string.menu_language, R.drawable.ic_translate, "language"),
    CONTACT_SUPPORT(R.string.menu_contact_us, R.drawable.ic_outlined_phone, "support"),
    INFORMATION(R.string.menu_information, R.drawable.ic_outlined_info, "information"),
    UPDATE_APP(R.string.inn_app_update_side_menu_item, R.drawable.ic_update_app_fill, "update_app");

    companion object {
        fun entriesForAccount(account: Account): Array<HomeDrawerItems> {
            val entries = mutableListOf<HomeDrawerItems>()
            entries.add(AD_INSERT)
            if (account.isLogged()) {
                entries.add(ACCOUNT)
            } else {
                entries.add(LOGIN)
            }
            entries.add(MESSAGING)
            if (account.isShop()) {
                entries.add(STATS)
            } else {
                entries.add(FAVORITES)
            }
            entries.add(LANGUAGE)
            entries.add(CONTACT_SUPPORT)
            entries.add(INFORMATION)
            entries.add(UPDATE_APP)
            return entries.toTypedArray()
        }
    }
}

fun SearchSuggestion.getBody(context: Context): String {
    val builder = StringBuilder()
    if (!keyword.isNullOrEmpty()) {
        builder.append("$keyword ")
    }
    if (!brandName.isNullOrEmpty()) {
        builder.append("$brandName ")
    }
    if (!modelName.isNullOrEmpty()) {
        builder.append(" (${modelName}) ")
    }
    if (!category?.name.isNullOrEmpty() && !keyword.isNullOrEmpty()) {
        builder.append("${category?.name} ")
    } else if (!category?.name.isNullOrEmpty() && (!keyword.isNullOrEmpty() || !brandName.isNullOrEmpty() || !modelName.isNullOrEmpty())) {
        builder.append("${context.getString(R.string.dans)} ${category?.name} ")
    } else if (!category?.name.isNullOrEmpty()) {
        builder.append("${context.getString(R.string.dans)} ${category?.name} ")
    }
    if (!adType?.name.isNullOrEmpty()) {
        builder.append(" ${adType?.name} ")
    }
    if (!city?.name.isNullOrEmpty()) {
        builder.append("${context.getString(R.string.a)} ${city?.name} ")
    }
    return builder.toString()
}
package se.scmv.morocco.charts.grid.axisscale.y

import se.scmv.morocco.charts.grid.ChartGridDefaults

class YAxisScaleStatic(
    override val min: Float,
    override val max: Float,
    override val numberOfHorizontalLines: Int = ChartGridDefaults.NUMBER_OF_GRID_LINES,
) : YAxisScale {
    override val step: Float

    init {

        if (numberOfHorizontalLines <= 0) {
            throw IllegalArgumentException("numberOfHorizontalLines must be positive")
        }

        val range = this.max - this.min
        this.step = range / numberOfHorizontalLines
    }

    override fun toString(): String {
        return "min: $min, max: $max, step: $step"
    }
}

package se.scmv.morocco.charts.line

import androidx.compose.runtime.Composable
import se.scmv.morocco.charts.grid.ChartGridDefaults

data class XAxisConfig(
    val markerLayout: @Composable (value: Any) -> Unit = ChartGridDefaults.XAxisMarkerLayout,
    val hideMarkersWhenOverlapping: Boolean = false,
    val alignFirstAndLastToChartEdges: Boolean = false,
    val roundMarkersToMultiplicationOf: Long = ChartGridDefaults.ROUND_X_AXIS_MARKERS_CLOSEST_TO,
    val maxVerticalLines: Int = ChartGridDefaults.NUMBER_OF_GRID_LINES,
)

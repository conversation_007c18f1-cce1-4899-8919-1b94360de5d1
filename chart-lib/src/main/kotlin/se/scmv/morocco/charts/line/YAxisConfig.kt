package se.scmv.morocco.charts.line

import androidx.compose.runtime.Composable
import se.scmv.morocco.charts.grid.ChartGridDefaults
import se.scmv.morocco.charts.grid.YAxisTitleData
import se.scmv.morocco.charts.grid.axisscale.y.YAxisScale

data class YAxisConfig(
    val markerLayout: (@Composable (value: Any) -> Unit)? = ChartGridDefaults.YAxisMarkerLayout,
    val yAxisTitleData: YAxisTitleData? = ChartGridDefaults.YAxisDataTitle,
    val scale: YAxisScale,
)

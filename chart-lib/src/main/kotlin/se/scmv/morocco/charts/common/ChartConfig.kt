package se.scmv.morocco.charts.common

import androidx.compose.runtime.Immutable
import androidx.compose.ui.unit.Dp
import se.scmv.morocco.charts.grid.ChartGridDefaults

/**
 * The customization parameters for a chart
 *
 * @param thickness Width of a single bar
 * @param cornerRadius 0 for square bars, thickness/2 for fully rounded corners
 * @param barsSpacing The space between bars in a cluster
 * @param maxHorizontalLinesCount Max number of lines that are allowed to draw for marking y-axis
 * @param roundMinMaxClosestTo Number to which min and max range will be rounded to
 */
@Immutable
data class ChartConfig(
    val thickness: Dp = ChartDefaults.BAR_THICKNESS,
    val cornerRadius: Dp = ChartDefaults.BAR_CORNER_RADIUS,
    val barsSpacing: Dp = ChartDefaults.BAR_HORIZONTAL_SPACING,
    val maxHorizontalLinesCount: Int = ChartGridDefaults.NUMBER_OF_GRID_LINES,
    val roundMinMaxClosestTo: Float = ChartGridDefaults.ROUND_Y_AXIS_MARKERS_CLOSEST_TO,
)
